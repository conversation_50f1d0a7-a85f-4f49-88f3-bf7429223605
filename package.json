{"name": "advantage-waste-enterprise", "version": "1.0.0", "description": "Enterprise waste management platform for Greystar Advantage Waste", "private": true, "scripts": {"dev:backend": "cd backend && .\\venv\\Scripts\\uvicorn.exe src.main:app --reload", "dev:frontend": "cd frontend && npm run dev", "install:backend": "cd backend && .\\venv\\Scripts\\pip.exe install -r requirements.txt", "install:frontend": "cd frontend && npm install", "install:all": "npm run install:backend && npm run install:frontend", "test:backend": "cd backend && .\\venv\\Scripts\\pytest.exe", "test:frontend": "cd frontend && npm test", "test:all": "npm run test:backend && npm run test:frontend", "build:frontend": "cd frontend && npm run build", "lint:backend": "cd backend && .\\venv\\Scripts\\ruff.exe check src/", "lint:frontend": "cd frontend && npm run lint", "format:frontend": "cd frontend && npm run format", "start:dev": "powershell -Command \"Start-Process powershell -ArgumentList '-Command', 'cd backend; .\\venv\\Scripts\\uvicorn.exe src.main:app --reload' -WindowStyle Normal; Start-Process powershell -ArgumentList '-Command', 'cd frontend; npm run dev' -WindowStyle Normal\""}, "keywords": ["waste-management", "greystar", "enterprise", "multifamily", "contract-analysis"], "author": "<PERSON> <<EMAIL>>", "license": "Private"}