import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

export default function RenewalDashboard() {
  const navigate = useNavigate()
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'overdue'>('all')

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-3xl font-bold text-gray-900">Contract Renewals</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="sm:flex sm:items-center sm:justify-between">
            <div>
              <div className="flex space-x-4">
                <button
                  onClick={() => setFilter('all')}
                  className={`px-4 py-2 rounded-md font-medium ${
                    filter === 'all'
                      ? 'bg-greystar-blue text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  All Contracts
                </button>
                <button
                  onClick={() => setFilter('upcoming')}
                  className={`px-4 py-2 rounded-md font-medium ${
                    filter === 'upcoming'
                      ? 'bg-greystar-blue text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Upcoming (90 days)
                </button>
                <button
                  onClick={() => setFilter('overdue')}
                  className={`px-4 py-2 rounded-md font-medium ${
                    filter === 'overdue'
                      ? 'bg-greystar-blue text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Overdue
                </button>
              </div>
            </div>
            <div className="mt-4 sm:mt-0">
              <button
                onClick={() => navigate('/contract-analysis')}
                className="btn-primary"
              >
                Upload Contract
              </button>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <p className="text-gray-500 text-center py-8">
              No contracts loaded yet. Upload a contract to get started.
            </p>
          </div>
        </div>
      </main>
    </div>
  )
}