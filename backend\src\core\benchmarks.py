"""
Industry Benchmarks and Standards for Waste Management
=====================================================

Comprehensive industry benchmarks, standards, and best practices for waste
management contracts based on Greystar's Advantage Waste methodology and
industry research.

All benchmarks are configurable and can be updated based on market conditions.
"""

from decimal import Decimal
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import datetime


class PropertyType(Enum):
    """Standard property types for waste management"""
    GARDEN_STYLE = "garden-style"
    MID_RISE = "mid-rise"
    HIGH_RISE = "high-rise"
    MIXED_USE = "mixed-use"
    STUDENT_HOUSING = "student-housing"
    SENIOR_LIVING = "senior-living"


class ContainerType(Enum):
    """Standard container types"""
    FRONT_LOAD = "front-load"
    COMPACTOR = "compactor"
    SELF_CONTAINED = "self-contained"
    ROLL_OFF = "roll-off"
    RECYCLING_BIN = "recycling-bin"


class RegionType(Enum):
    """Geographic regions for market analysis"""
    NORTHEAST = "northeast"
    SOUTHEAST = "southeast"
    MIDWEST = "midwest"
    SOUTHWEST = "southwest"
    WEST = "west"
    NATIONAL = "national"


@dataclass
class BenchmarkRange:
    """Benchmark range with min, max, and target values"""
    minimum: Decimal
    maximum: Decimal
    target: Decimal
    optimal_min: Decimal
    optimal_max: Decimal
    
    def is_within_range(self, value: Decimal) -> bool:
        """Check if value is within acceptable range"""
        return self.minimum <= value <= self.maximum
    
    def is_optimal(self, value: Decimal) -> bool:
        """Check if value is within optimal range"""
        return self.optimal_min <= value <= self.optimal_max
    
    def variance_from_target(self, value: Decimal) -> Decimal:
        """Calculate percentage variance from target"""
        if self.target == 0:
            return Decimal("0")
        return ((value - self.target) / self.target) * 100


@dataclass
class ContractTermBenchmarks:
    """Contract term benchmarks and best practices"""
    contract_length_months: BenchmarkRange
    termination_notice_days: BenchmarkRange
    max_annual_increase_percent: BenchmarkRange
    fuel_surcharge_percent: BenchmarkRange
    environmental_fee_percent: BenchmarkRange
    admin_fee_limit: Decimal


class IndustryBenchmarks:
    """
    Comprehensive industry benchmarks for waste management contracts.
    
    Based on Greystar's Advantage Waste methodology and industry research
    from major waste management companies and multifamily operators.
    """
    
    # Core financial benchmarks by property type
    COST_PER_DOOR_BENCHMARKS = {
        PropertyType.GARDEN_STYLE: BenchmarkRange(
            minimum=Decimal("18"), maximum=Decimal("32"), target=Decimal("25"),
            optimal_min=Decimal("20"), optimal_max=Decimal("28")
        ),
        PropertyType.MID_RISE: BenchmarkRange(
            minimum=Decimal("14"), maximum=Decimal("26"), target=Decimal("20"),
            optimal_min=Decimal("16"), optimal_max=Decimal("23")
        ),
        PropertyType.HIGH_RISE: BenchmarkRange(
            minimum=Decimal("10"), maximum=Decimal("22"), target=Decimal("16"),
            optimal_min=Decimal("12"), optimal_max=Decimal("19")
        ),
        PropertyType.MIXED_USE: BenchmarkRange(
            minimum=Decimal("12"), maximum=Decimal("24"), target=Decimal("18"),
            optimal_min=Decimal("14"), optimal_max=Decimal("21")
        ),
        PropertyType.STUDENT_HOUSING: BenchmarkRange(
            minimum=Decimal("22"), maximum=Decimal("38"), target=Decimal("30"),
            optimal_min=Decimal("25"), optimal_max=Decimal("33")
        ),
        PropertyType.SENIOR_LIVING: BenchmarkRange(
            minimum=Decimal("8"), maximum=Decimal("18"), target=Decimal("13"),
            optimal_min=Decimal("10"), optimal_max=Decimal("16")
        )
    }
    
    # Volume benchmarks (cubic yards per door per month)
    YARDS_PER_DOOR_BENCHMARKS = {
        PropertyType.GARDEN_STYLE: BenchmarkRange(
            minimum=Decimal("1.8"), maximum=Decimal("2.5"), target=Decimal("2.1"),
            optimal_min=Decimal("1.9"), optimal_max=Decimal("2.3")
        ),
        PropertyType.MID_RISE: BenchmarkRange(
            minimum=Decimal("1.3"), maximum=Decimal("2.0"), target=Decimal("1.6"),
            optimal_min=Decimal("1.4"), optimal_max=Decimal("1.8")
        ),
        PropertyType.HIGH_RISE: BenchmarkRange(
            minimum=Decimal("1.0"), maximum=Decimal("1.7"), target=Decimal("1.3"),
            optimal_min=Decimal("1.1"), optimal_max=Decimal("1.5")
        ),
        PropertyType.MIXED_USE: BenchmarkRange(
            minimum=Decimal("1.2"), maximum=Decimal("1.8"), target=Decimal("1.5"),
            optimal_min=Decimal("1.3"), optimal_max=Decimal("1.7")
        ),
        PropertyType.STUDENT_HOUSING: BenchmarkRange(
            minimum=Decimal("2.2"), maximum=Decimal("3.2"), target=Decimal("2.7"),
            optimal_min=Decimal("2.4"), optimal_max=Decimal("2.9")
        ),
        PropertyType.SENIOR_LIVING: BenchmarkRange(
            minimum=Decimal("0.8"), maximum=Decimal("1.4"), target=Decimal("1.1"),
            optimal_min=Decimal("0.9"), optimal_max=Decimal("1.3")
        )
    }
    
    # Contract term benchmarks
    CONTRACT_TERMS = ContractTermBenchmarks(
        contract_length_months=BenchmarkRange(
            minimum=Decimal("12"), maximum=Decimal("36"), target=Decimal("24"),
            optimal_min=Decimal("18"), optimal_max=Decimal("24")
        ),
        termination_notice_days=BenchmarkRange(
            minimum=Decimal("30"), maximum=Decimal("120"), target=Decimal("60"),
            optimal_min=Decimal("30"), optimal_max=Decimal("90")
        ),
        max_annual_increase_percent=BenchmarkRange(
            minimum=Decimal("0"), maximum=Decimal("6"), target=Decimal("3"),
            optimal_min=Decimal("2"), optimal_max=Decimal("4")
        ),
        fuel_surcharge_percent=BenchmarkRange(
            minimum=Decimal("0"), maximum=Decimal("8"), target=Decimal("0"),
            optimal_min=Decimal("0"), optimal_max=Decimal("5")
        ),
        environmental_fee_percent=BenchmarkRange(
            minimum=Decimal("0"), maximum=Decimal("7"), target=Decimal("0"),
            optimal_min=Decimal("0"), optimal_max=Decimal("3")
        ),
        admin_fee_limit=Decimal("50")  # Maximum monthly admin fee
    )
    
    # Service frequency benchmarks
    SERVICE_FREQUENCY_BENCHMARKS = {
        "standard": {
            "min_pickups_weekly": 1,
            "max_pickups_weekly": 3,
            "optimal_pickups_weekly": 2
        },
        "high_volume": {
            "min_pickups_weekly": 2,
            "max_pickups_weekly": 5,
            "optimal_pickups_weekly": 3
        },
        "low_volume": {
            "min_pickups_weekly": 1,
            "max_pickups_weekly": 2,
            "optimal_pickups_weekly": 1
        }
    }
    
    # Container size benchmarks by property size
    CONTAINER_SIZE_BENCHMARKS = {
        "small_property": {  # <100 units
            "recommended_sizes": [2, 4, 6],
            "optimal_size": 4,
            "compactor_threshold": 75
        },
        "medium_property": {  # 100-300 units
            "recommended_sizes": [4, 6, 8, 30, 34],
            "optimal_size": 34,  # Compactor
            "compactor_threshold": 150
        },
        "large_property": {  # 300+ units
            "recommended_sizes": [30, 34, 40],
            "optimal_size": 34,  # Compactor
            "compactor_threshold": 200
        }
    }
    
    # Regional cost adjustment factors
    REGIONAL_COST_FACTORS = {
        RegionType.NORTHEAST: Decimal("1.15"),  # 15% above national average
        RegionType.SOUTHEAST: Decimal("0.90"),  # 10% below national average
        RegionType.MIDWEST: Decimal("0.85"),    # 15% below national average
        RegionType.SOUTHWEST: Decimal("0.95"),  # 5% below national average
        RegionType.WEST: Decimal("1.25"),       # 25% above national average
        RegionType.NATIONAL: Decimal("1.00")    # National average baseline
    }
    
    # Vendor performance scoring weights
    VENDOR_SCORING_WEIGHTS = {
        "pricing": Decimal("0.30"),           # 30% weight
        "service_quality": Decimal("0.25"),   # 25% weight
        "contract_terms": Decimal("0.20"),    # 20% weight
        "reliability": Decimal("0.15"),       # 15% weight
        "environmental": Decimal("0.10")      # 10% weight
    }
    
    # Contract renewal risk factors
    RENEWAL_RISK_FACTORS = {
        "high_risk": [
            "contract_length > 36 months",
            "fuel_surcharge > 8%",
            "environmental_fee > 5%",
            "no_price_cap",
            "auto_renewal_enabled",
            "cost_per_door > optimal_max + 20%"
        ],
        "medium_risk": [
            "contract_length > 24 months",
            "fuel_surcharge > 5%",
            "environmental_fee > 3%",
            "annual_increase > 5%",
            "termination_notice > 90 days"
        ],
        "low_risk": [
            "contract_length <= 24 months",
            "fuel_surcharge <= 5%",
            "environmental_fee <= 3%",
            "annual_increase <= 4%",
            "competitive_pricing"
        ]
    }

    @classmethod
    def get_cost_benchmark(cls, property_type: PropertyType, region: RegionType = RegionType.NATIONAL) -> BenchmarkRange:
        """
        Get cost per door benchmark adjusted for property type and region.
        
        Args:
            property_type: Type of property
            region: Geographic region
            
        Returns:
            Adjusted benchmark range
        """
        base_benchmark = cls.COST_PER_DOOR_BENCHMARKS[property_type]
        regional_factor = cls.REGIONAL_COST_FACTORS[region]
        
        return BenchmarkRange(
            minimum=base_benchmark.minimum * regional_factor,
            maximum=base_benchmark.maximum * regional_factor,
            target=base_benchmark.target * regional_factor,
            optimal_min=base_benchmark.optimal_min * regional_factor,
            optimal_max=base_benchmark.optimal_max * regional_factor
        )
    
    @classmethod
    def get_volume_benchmark(cls, property_type: PropertyType) -> BenchmarkRange:
        """
        Get volume per door benchmark for property type.
        
        Args:
            property_type: Type of property
            
        Returns:
            Volume benchmark range
        """
        return cls.YARDS_PER_DOOR_BENCHMARKS[property_type]
    
    @classmethod
    def recommend_container_size(cls, unit_count: int, current_volume: Decimal) -> Dict:
        """
        Recommend optimal container configuration.
        
        Args:
            unit_count: Number of units in property
            current_volume: Current monthly volume
            
        Returns:
            Container recommendations
        """
        if unit_count < 100:
            category = "small_property"
        elif unit_count <= 300:
            category = "medium_property"
        else:
            category = "large_property"
            
        config = cls.CONTAINER_SIZE_BENCHMARKS[category]
        
        # Determine if compactor is recommended
        compactor_recommended = unit_count >= config["compactor_threshold"]
        
        # Calculate optimal container quantity based on volume
        optimal_size = config["optimal_size"]
        estimated_pickups_weekly = 2  # Standard assumption
        
        # Volume per pickup
        volume_per_pickup = current_volume / (estimated_pickups_weekly * Decimal("4.33"))
        containers_needed = max(1, int((volume_per_pickup / optimal_size).to_integral_value()))
        
        return {
            "category": category,
            "recommended_sizes": config["recommended_sizes"],
            "optimal_size": optimal_size,
            "compactor_recommended": compactor_recommended,
            "estimated_containers_needed": containers_needed,
            "estimated_pickups_weekly": estimated_pickups_weekly
        }
    
    @classmethod
    def evaluate_contract_terms(cls, contract_data: Dict) -> Dict:
        """
        Evaluate contract terms against benchmarks.
        
        Args:
            contract_data: Dictionary with contract terms
            
        Returns:
            Evaluation results with scores and recommendations
        """
        terms = cls.CONTRACT_TERMS
        evaluation = {}
        
        # Contract length evaluation
        length = Decimal(str(contract_data.get("contract_length_months", 0)))
        evaluation["contract_length"] = {
            "value": length,
            "benchmark": terms.contract_length_months,
            "within_range": terms.contract_length_months.is_within_range(length),
            "optimal": terms.contract_length_months.is_optimal(length),
            "score": cls._calculate_term_score(length, terms.contract_length_months)
        }
        
        # Termination notice evaluation
        notice = Decimal(str(contract_data.get("termination_notice_days", 0)))
        evaluation["termination_notice"] = {
            "value": notice,
            "benchmark": terms.termination_notice_days,
            "within_range": terms.termination_notice_days.is_within_range(notice),
            "optimal": terms.termination_notice_days.is_optimal(notice),
            "score": cls._calculate_term_score(notice, terms.termination_notice_days, reverse=True)
        }
        
        # Annual increase evaluation
        increase = Decimal(str(contract_data.get("max_annual_increase_percent", 0)))
        evaluation["annual_increase"] = {
            "value": increase,
            "benchmark": terms.max_annual_increase_percent,
            "within_range": terms.max_annual_increase_percent.is_within_range(increase),
            "optimal": terms.max_annual_increase_percent.is_optimal(increase),
            "score": cls._calculate_term_score(increase, terms.max_annual_increase_percent, reverse=True)
        }
        
        # Fuel surcharge evaluation
        fuel = Decimal(str(contract_data.get("fuel_surcharge_percent", 0)))
        evaluation["fuel_surcharge"] = {
            "value": fuel,
            "benchmark": terms.fuel_surcharge_percent,
            "within_range": terms.fuel_surcharge_percent.is_within_range(fuel),
            "optimal": terms.fuel_surcharge_percent.is_optimal(fuel),
            "score": cls._calculate_term_score(fuel, terms.fuel_surcharge_percent, reverse=True)
        }
        
        # Environmental fee evaluation
        env_fee = Decimal(str(contract_data.get("environmental_fee_percent", 0)))
        evaluation["environmental_fee"] = {
            "value": env_fee,
            "benchmark": terms.environmental_fee_percent,
            "within_range": terms.environmental_fee_percent.is_within_range(env_fee),
            "optimal": terms.environmental_fee_percent.is_optimal(env_fee),
            "score": cls._calculate_term_score(env_fee, terms.environmental_fee_percent, reverse=True)
        }
        
        # Calculate overall terms score
        scores = [eval_data["score"] for eval_data in evaluation.values()]
        overall_score = sum(scores) / len(scores) if scores else 0
        evaluation["overall_score"] = round(overall_score, 1)
        
        return evaluation
    
    @classmethod
    def _calculate_term_score(cls, value: Decimal, benchmark: BenchmarkRange, reverse: bool = False) -> float:
        """
        Calculate score for a contract term (0-100).
        
        Args:
            value: Actual value
            benchmark: Benchmark range
            reverse: True if lower values are better
            
        Returns:
            Score from 0 to 100
        """
        if benchmark.is_optimal(value):
            return 100.0
        elif benchmark.is_within_range(value):
            return 75.0
        else:
            # Calculate distance from acceptable range
            if reverse:
                if value > benchmark.maximum:
                    distance = float((value - benchmark.maximum) / benchmark.maximum)
                    return max(0.0, 50.0 - (distance * 50.0))
                elif value < benchmark.minimum:
                    return 100.0  # Below minimum is good for reverse scoring
            else:
                if value < benchmark.minimum:
                    distance = float((benchmark.minimum - value) / benchmark.minimum)
                    return max(0.0, 50.0 - (distance * 50.0))
                elif value > benchmark.maximum:
                    distance = float((value - benchmark.maximum) / benchmark.maximum)
                    return max(0.0, 50.0 - (distance * 50.0))
        
        return 50.0  # Default score for edge cases


class MarketIntelligence:
    """Market intelligence and pricing trend data"""
    
    # Historical pricing trends (percentage change year-over-year)
    PRICING_TRENDS = {
        "national_average": {
            "2023": Decimal("4.2"),  # 4.2% increase
            "2024": Decimal("3.8"),  # 3.8% increase
            "2025_projected": Decimal("3.5")  # 3.5% projected
        },
        "fuel_costs": {
            "2023": Decimal("8.5"),  # 8.5% increase
            "2024": Decimal("-2.1"), # 2.1% decrease
            "2025_projected": Decimal("2.0")  # 2.0% projected increase
        }
    }
    
    # Competitive landscape data
    MARKET_SHARE = {
        "waste_management": Decimal("0.32"),    # 32% market share
        "republic_services": Decimal("0.28"),   # 28% market share
        "gfl_environmental": Decimal("0.12"),   # 12% market share
        "casella": Decimal("0.08"),             # 8% market share
        "other_regional": Decimal("0.20")       # 20% regional/local
    }
    
    @classmethod
    def get_market_pricing_trend(cls, region: RegionType, year: int = 2024) -> Decimal:
        """
        Get market pricing trend for region and year.
        
        Args:
            region: Geographic region
            year: Year for trend data
            
        Returns:
            Percentage change in pricing
        """
        base_trend = cls.PRICING_TRENDS["national_average"].get(str(year), Decimal("3.5"))
        regional_factor = IndustryBenchmarks.REGIONAL_COST_FACTORS[region]
        
        # Adjust trend based on regional factors
        if regional_factor > Decimal("1.1"):  # High-cost regions
            return base_trend * Decimal("1.2")  # 20% higher trend
        elif regional_factor < Decimal("0.9"):  # Low-cost regions
            return base_trend * Decimal("0.8")  # 20% lower trend
        else:
            return base_trend
    
    @classmethod
    def get_competitive_position(cls, vendor_name: str) -> Dict:
        """
        Get competitive position data for vendor.
        
        Args:
            vendor_name: Name of vendor
            
        Returns:
            Competitive position analysis
        """
        vendor_key = vendor_name.lower().replace(" ", "_").replace(".", "")
        market_share = cls.MARKET_SHARE.get(vendor_key, Decimal("0.05"))  # Default 5%
        
        if market_share >= Decimal("0.25"):
            tier = "national_leader"
        elif market_share >= Decimal("0.10"):
            tier = "major_player"
        elif market_share >= Decimal("0.05"):
            tier = "regional_player"
        else:
            tier = "local_player"
            
        return {
            "vendor": vendor_name,
            "market_share": float(market_share),
            "tier": tier,
            "pricing_power": "high" if market_share >= Decimal("0.20") else "medium" if market_share >= Decimal("0.10") else "low",
            "negotiation_leverage": "low" if market_share >= Decimal("0.20") else "medium" if market_share >= Decimal("0.10") else "high"
        }