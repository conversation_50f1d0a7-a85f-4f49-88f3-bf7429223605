# Advantage Waste Enterprise - Quick Start Guide
# This script demonstrates how to use Claude Code with your new enterprise setup

Write-Host "🚀 Advantage Waste Enterprise - Quick Start Guide" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

$projectRoot = "C:\Users\<USER>\Documents\Claude Code\advantage-waste-enterprise"

# Verify we're in the right location
if (!(Test-Path $projectRoot)) {
    Write-Host "❌ Project not found at $projectRoot" -ForegroundColor Red
    exit 1
}

Set-Location $projectRoot

Write-Host "`n📋 Project Status Check:" -ForegroundColor Yellow

# Check project structure
$requiredDirs = @(
    ".claude",
    "PRPs", 
    "backend",
    "frontend",
    "examples"
)

foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "  ✅ $dir" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $dir missing" -ForegroundColor Red
    }
}

# Check key files
$requiredFiles = @(
    "CLAUDE.md",
    "INITIAL.md", 
    "README.md",
    ".claude\settings.local.json",
    "examples\contract_analysis_example.py",
    "backend\requirements.txt",
    "frontend\package.json",
    "docker-compose.yml"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file missing" -ForegroundColor Red
    }
}

Write-Host "`n🎯 Available Development Options:" -ForegroundColor Yellow

Write-Host "`n1. 🤖 Claude Code Development (Recommended)" -ForegroundColor Cyan
Write-Host "   Start Claude Code with full project context:" -ForegroundColor White
Write-Host "   > .\start-claude.bat" -ForegroundColor Green
Write-Host "   Then use commands like:" -ForegroundColor White
Write-Host "   > /analyze-contract examples/sample_contract.pdf" -ForegroundColor Green
Write-Host "   > /generate-prp INITIAL.md" -ForegroundColor Green
Write-Host "   > /execute-prp PRPs/your-feature.md" -ForegroundColor Green

Write-Host "`n2. 💻 Traditional Development" -ForegroundColor Cyan
Write-Host "   Start development servers:" -ForegroundColor White
Write-Host "   > .\start-dev.ps1" -ForegroundColor Green
Write-Host "   Or manually:" -ForegroundColor White
Write-Host "   > cd backend && .\venv\Scripts\activate && uvicorn src.main:app --reload" -ForegroundColor Green
Write-Host "   > cd frontend && npm run dev" -ForegroundColor Green

Write-Host "`n3. 🐳 Docker Development" -ForegroundColor Cyan
Write-Host "   Full containerized environment:" -ForegroundColor White
Write-Host "   > docker-compose up -d" -ForegroundColor Green
Write-Host "   With dev tools:" -ForegroundColor White
Write-Host "   > docker-compose --profile dev up -d" -ForegroundColor Green

Write-Host "`n4. 🖥️ Cursor IDE Integration" -ForegroundColor Cyan
Write-Host "   Open in Cursor with full configuration:" -ForegroundColor White
Write-Host "   > cursor ." -ForegroundColor Green
Write-Host "   Or VS Code:" -ForegroundColor White
Write-Host "   > code ." -ForegroundColor Green

Write-Host "`n📚 Next Steps - Building Your First Feature:" -ForegroundColor Yellow

Write-Host "`n1. Define Your Feature in INITIAL.md:" -ForegroundColor White
Write-Host @"
## FEATURE:
Contract comparison dashboard that shows the GFL vs Waste Management 
analysis from the reference binder, displaying the `$12,539 annual savings 
calculation and generating executive summary reports.

## EXAMPLES:
- examples/contract_analysis_example.py - Contract analysis patterns

## DOCUMENTATION:
- Greystar Waste Reference Binder methodology
- FastAPI documentation for API endpoints
- React patterns for dashboard components

## OTHER CONSIDERATIONS:
- Must handle PDF contract uploads
- Integration with Advantage Waste portal authentication
- Support 252-unit property analysis patterns
- Generate reports suitable for property managers
"@ -ForegroundColor Gray

Write-Host "`n2. Generate a PRP (Product Requirements Prompt):" -ForegroundColor White
Write-Host "   > .\start-claude.bat" -ForegroundColor Green
Write-Host "   > /generate-prp INITIAL.md" -ForegroundColor Green

Write-Host "`n3. Implement the Feature:" -ForegroundColor White
Write-Host "   > /execute-prp PRPs/contract-comparison-dashboard.md" -ForegroundColor Green

Write-Host "`n🔧 Helpful Commands:" -ForegroundColor Yellow

Write-Host "`nClaude Code Commands:" -ForegroundColor White
Write-Host "  /analyze-contract [file]     - Analyze waste contracts" -ForegroundColor Green
Write-Host "  /generate-prp [initial.md]   - Create implementation plan" -ForegroundColor Green
Write-Host "  /execute-prp [prp-file]      - Implement feature" -ForegroundColor Green

Write-Host "`nDevelopment Commands:" -ForegroundColor White
Write-Host "  Backend Tests:  pytest backend/tests/ -v" -ForegroundColor Green
Write-Host "  Frontend Tests: npm test" -ForegroundColor Green
Write-Host "  Linting:        ruff check backend/src/" -ForegroundColor Green
Write-Host "  Type Check:     mypy backend/src/" -ForegroundColor Green

Write-Host "`n🌐 Service URLs (when running):" -ForegroundColor Yellow
Write-Host "  Backend API:        http://localhost:8000" -ForegroundColor Green
Write-Host "  API Documentation:  http://localhost:8000/docs" -ForegroundColor Green
Write-Host "  Frontend:           http://localhost:3000" -ForegroundColor Green
Write-Host "  PgAdmin (Docker):   http://localhost:5050" -ForegroundColor Green
Write-Host "  Redis UI (Docker):  http://localhost:8081" -ForegroundColor Green

Write-Host "`n💡 Key Features of This Setup:" -ForegroundColor Yellow
Write-Host "  ✅ Context Engineering - Claude understands waste management" -ForegroundColor Green
Write-Host "  ✅ Industry Knowledge - Built-in formulas and benchmarks" -ForegroundColor Green  
Write-Host "  ✅ Enterprise Ready - Scalable for 3,850+ properties" -ForegroundColor Green
Write-Host "  ✅ Integration Ready - Advantage Waste portal patterns" -ForegroundColor Green
Write-Host "  ✅ Production Patterns - Security, testing, monitoring" -ForegroundColor Green

Write-Host "`n🎊 Ready to build enterprise waste management solutions!" -ForegroundColor Green
Write-Host "Start with: .\start-claude.bat" -ForegroundColor Yellow

# Ask user what they want to do
Write-Host "`n❓ What would you like to do next?" -ForegroundColor Yellow
Write-Host "1. Start Claude Code" -ForegroundColor White
Write-Host "2. Start development servers" -ForegroundColor White  
Write-Host "3. Open in Cursor IDE" -ForegroundColor White
Write-Host "4. Just show me the setup info" -ForegroundColor White

$choice = Read-Host "Enter your choice (1-4)"

switch ($choice) {
    "1" {
        Write-Host "Starting Claude Code..." -ForegroundColor Green
        & ".\start-claude.bat"
    }
    "2" {
        Write-Host "Starting development servers..." -ForegroundColor Green
        & ".\start-dev.ps1"
    }
    "3" {
        Write-Host "Opening Cursor IDE..." -ForegroundColor Green
        if (Get-Command cursor -ErrorAction SilentlyContinue) {
            Start-Process cursor -ArgumentList "."
        } else {
            Write-Host "Cursor not found. Opening with VS Code..." -ForegroundColor Yellow
            code .
        }
    }
    "4" {
        Write-Host "Setup complete! Use the commands above to get started." -ForegroundColor Green
    }
    default {
        Write-Host "Invalid choice. Setup complete! Use the commands above to get started." -ForegroundColor Green
    }
}
