import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Suspense, lazy } from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import { Toaster } from 'react-hot-toast'

const RenewalDashboard = lazy(() => import('./pages/RenewalDashboard'))
const RenewalDetails = lazy(() => import('./pages/RenewalDetails'))
const ContractAnalysis = lazy(() => import('./pages/ContractAnalysis'))
const Reports = lazy(() => import('./pages/Reports'))

function ErrorFallback({ error }: { error: Error }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="card max-w-md w-full">
        <h2 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h2>
        <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">{error.message}</pre>
        <button
          onClick={() => window.location.reload()}
          className="btn-primary mt-4"
        >
          Reload page
        </button>
      </div>
    </div>
  )
}

function LoadingFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-greystar-blue"></div>
    </div>
  )
}

function App() {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#10b981',
                  secondary: '#fff',
                },
              },
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#fff',
                },
              },
            }}
          />
          <Suspense fallback={<LoadingFallback />}>
            <Routes>
              <Route path="/" element={<Navigate to="/renewals" replace />} />
              <Route path="/renewals" element={<RenewalDashboard />} />
              <Route path="/renewals/:id" element={<RenewalDetails />} />
              <Route path="/contract-analysis" element={<ContractAnalysis />} />
              <Route path="/reports" element={<Reports />} />
              <Route path="*" element={<Navigate to="/renewals" replace />} />
            </Routes>
          </Suspense>
        </div>
      </Router>
    </ErrorBoundary>
  )
}

export default App