"""
Initial Database Schema Migration for Advantage Waste Enterprise
===============================================================

Creates the complete database schema for contract renewal tracking system
with optimized indexes for enterprise-scale performance (3,850+ properties).

Migration: 001_initial_schema
Created: 2024-01-01
Description: Initial schema with properties, vendors, contracts, and renewal tracking
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """Create initial database schema with all tables and indexes"""
    
    # Create enums first
    contract_status_enum = postgresql.ENUM(
        'DRAFT', 'ACTIVE', 'PENDING_RENEWAL', 'RENEWED', 'TERMINATED', 'EXPIRED', 'CANCELLED',
        name='contractstatus'
    )
    contract_status_enum.create(op.get_bind())
    
    contract_grade_enum = postgresql.ENUM(
        'A+', 'A', 'B', 'C', 'D',
        name='contractgrade'
    )
    contract_grade_enum.create(op.get_bind())
    
    container_type_enum = postgresql.ENUM(
        'front-load', 'compactor', 'roll-off', 'rear-load', 'side-load',
        name='containertype'
    )
    container_type_enum.create(op.get_bind())
    
    property_type_enum = postgresql.ENUM(
        'garden-style', 'mid-rise', 'high-rise', 'mixed-use', 'senior-living',
        name='propertytype'
    )
    property_type_enum.create(op.get_bind())
    
    cpi_index_enum = postgresql.ENUM(
        'CPI-U', 'CPI-W', 'regional-cpi', 'custom',
        name='cpiindex'
    )
    cpi_index_enum.create(op.get_bind())
    
    alert_status_enum = postgresql.ENUM(
        'pending', 'sent', 'acknowledged', 'in_progress', 'completed', 'dismissed', 'escalated',
        name='alertstatus'
    )
    alert_status_enum.create(op.get_bind())
    
    alert_priority_enum = postgresql.ENUM(
        'low', 'medium', 'high', 'critical', 'urgent',
        name='alertpriority'
    )
    alert_priority_enum.create(op.get_bind())
    
    notification_type_enum = postgresql.ENUM(
        'email', 'sms', 'dashboard', 'slack', 'teams', 'webhook',
        name='notificationtype'
    )
    notification_type_enum.create(op.get_bind())
    
    analysis_type_enum = postgresql.ENUM(
        'renewal_review', 'cost_comparison', 'benchmark_analysis', 
        'market_analysis', 'performance_review', 'compliance_check',
        name='analysistype'
    )
    analysis_type_enum.create(op.get_bind())
    
    renewal_decision_enum = postgresql.ENUM(
        'renew_current', 'renew_negotiated', 'switch_vendor', 
        'cancel_service', 'extend_evaluation', 'pending_approval',
        name='renewaldecision'
    )
    renewal_decision_enum.create(op.get_bind())
    
    user_role_enum = postgresql.ENUM(
        'property_manager', 'regional_manager', 'operations_director',
        'procurement_manager', 'finance_director', 'executive', 'waste_specialist',
        name='userrole'
    )
    user_role_enum.create(op.get_bind())
    
    # Create properties table
    op.create_table(
        'properties',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('greystar_property_id', sa.String(length=50), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('address', sa.Text(), nullable=False),
        sa.Column('city', sa.String(length=100), nullable=False),
        sa.Column('state', sa.String(length=2), nullable=False),
        sa.Column('zip_code', sa.String(length=10), nullable=False),
        sa.Column('unit_count', sa.Integer(), nullable=False),
        sa.Column('property_type', property_type_enum, nullable=False),
        sa.Column('square_footage', sa.Integer(), nullable=True),
        sa.Column('occupied_units', sa.Integer(), nullable=True),
        sa.Column('occupancy_rate', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('property_manager', sa.String(length=255), nullable=True),
        sa.Column('property_manager_email', sa.String(length=255), nullable=True),
        sa.Column('regional_manager', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.CheckConstraint('unit_count > 0', name='check_unit_count_positive'),
        sa.CheckConstraint('occupied_units >= 0', name='check_occupied_units_non_negative'),
        sa.CheckConstraint('occupied_units <= unit_count', name='check_occupied_units_lte_total'),
        sa.CheckConstraint('occupancy_rate >= 0.0', name='check_occupancy_rate_non_negative'),
        sa.CheckConstraint('occupancy_rate <= 1.0', name='check_occupancy_rate_lte_one'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('greystar_property_id')
    )
    
    # Create vendors table
    op.create_table(
        'vendors',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('legal_name', sa.String(length=255), nullable=True),
        sa.Column('vendor_code', sa.String(length=50), nullable=True),
        sa.Column('primary_contact', sa.String(length=255), nullable=True),
        sa.Column('contact_email', sa.String(length=255), nullable=True),
        sa.Column('contact_phone', sa.String(length=20), nullable=True),
        sa.Column('website', sa.String(length=500), nullable=True),
        sa.Column('headquarters_city', sa.String(length=100), nullable=True),
        sa.Column('headquarters_state', sa.String(length=2), nullable=True),
        sa.Column('service_territories', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('service_types', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('average_rating', sa.Numeric(precision=3, scale=2), nullable=True),
        sa.Column('contract_count', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.CheckConstraint('average_rating >= 0.0', name='check_rating_non_negative'),
        sa.CheckConstraint('average_rating <= 5.0', name='check_rating_lte_five'),
        sa.CheckConstraint('contract_count >= 0', name='check_contract_count_non_negative'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name'),
        sa.UniqueConstraint('vendor_code')
    )
    
    # Create contracts table
    op.create_table(
        'contracts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('contract_number', sa.String(length=100), nullable=False),
        sa.Column('property_id', sa.Integer(), nullable=False),
        sa.Column('vendor_id', sa.Integer(), nullable=False),
        sa.Column('status', contract_status_enum, nullable=False),
        sa.Column('grade', contract_grade_enum, nullable=True),
        sa.Column('contact_name', sa.String(length=255), nullable=True),
        sa.Column('contact_email', sa.String(length=255), nullable=True),
        sa.Column('contact_phone', sa.String(length=20), nullable=True),
        sa.Column('quote_number', sa.String(length=100), nullable=True),
        sa.Column('contract_length_months', sa.Integer(), nullable=False),
        sa.Column('effective_date', sa.Date(), nullable=False),
        sa.Column('expiration_date', sa.Date(), nullable=False),
        sa.Column('automatic_renewal', sa.Boolean(), nullable=True),
        sa.Column('renewal_term_months', sa.Integer(), nullable=True),
        sa.Column('termination_notice_days', sa.Integer(), nullable=False),
        sa.Column('container_size_yards', sa.Integer(), nullable=False),
        sa.Column('container_type', container_type_enum, nullable=False),
        sa.Column('container_quantity', sa.Integer(), nullable=False),
        sa.Column('pickup_frequency_weekly', sa.Integer(), nullable=False),
        sa.Column('service_description', sa.Text(), nullable=True),
        sa.Column('special_requirements', sa.Text(), nullable=True),
        sa.Column('recyclables_included', sa.Boolean(), nullable=True),
        sa.Column('base_monthly_rate', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('fuel_surcharge_percent', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('environmental_fee_percent', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('admin_fee', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('container_rental_fee', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('initial_delivery_charge', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('extra_pickup_cost', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('cpi_increases', sa.Boolean(), nullable=True),
        sa.Column('cpi_frequency', sa.String(length=20), nullable=True),
        sa.Column('cpi_index', cpi_index_enum, nullable=True),
        sa.Column('max_annual_increase_percent', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('early_termination_fee_calculation', sa.Text(), nullable=True),
        sa.Column('payment_terms_days', sa.Integer(), nullable=True),
        sa.Column('right_of_first_refusal_days', sa.Integer(), nullable=True),
        sa.Column('performance_standards', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('compliance_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('contract_document_url', sa.String(length=500), nullable=True),
        sa.Column('insurance_certificate_url', sa.String(length=500), nullable=True),
        sa.Column('last_analysis_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('calculated_monthly_cost', sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column('calculated_cost_per_door', sa.Numeric(precision=8, scale=2), nullable=True),
        sa.Column('calculated_yards_per_door', sa.Numeric(precision=6, scale=3), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_by', sa.String(length=255), nullable=True),
        sa.Column('updated_by', sa.String(length=255), nullable=True),
        sa.CheckConstraint('base_monthly_rate > 0', name='check_base_rate_positive'),
        sa.CheckConstraint('fuel_surcharge_percent >= 0', name='check_fuel_surcharge_non_negative'),
        sa.CheckConstraint('environmental_fee_percent >= 0', name='check_env_fee_non_negative'),
        sa.CheckConstraint('admin_fee >= 0', name='check_admin_fee_non_negative'),
        sa.CheckConstraint('container_rental_fee >= 0', name='check_rental_fee_non_negative'),
        sa.CheckConstraint('max_annual_increase_percent >= 0', name='check_max_increase_non_negative'),
        sa.CheckConstraint('max_annual_increase_percent <= 100', name='check_max_increase_reasonable'),
        sa.CheckConstraint('contract_length_months > 0', name='check_contract_length_positive'),
        sa.CheckConstraint('container_size_yards > 0', name='check_container_size_positive'),
        sa.CheckConstraint('container_quantity > 0', name='check_container_quantity_positive'),
        sa.CheckConstraint('pickup_frequency_weekly > 0', name='check_pickup_frequency_positive'),
        sa.CheckConstraint('pickup_frequency_weekly <= 7', name='check_pickup_frequency_reasonable'),
        sa.CheckConstraint('termination_notice_days >= 0', name='check_termination_notice_non_negative'),
        sa.CheckConstraint('payment_terms_days > 0', name='check_payment_terms_positive'),
        sa.CheckConstraint('expiration_date > effective_date', name='check_dates_logical'),
        sa.ForeignKeyConstraint(['property_id'], ['properties.id'], ),
        sa.ForeignKeyConstraint(['vendor_id'], ['vendors.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('contract_number')
    )
    
    # Create contract_analyses table
    op.create_table(
        'contract_analyses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.String(length=50), nullable=False),
        sa.Column('contract_id', sa.Integer(), nullable=False),
        sa.Column('analysis_type', analysis_type_enum, nullable=False),
        sa.Column('analysis_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('triggered_by', sa.String(length=100), nullable=True),
        sa.Column('performed_by', sa.String(length=255), nullable=True),
        sa.Column('analysis_version', sa.String(length=20), nullable=True),
        sa.Column('overall_grade', sa.String(length=5), nullable=True),
        sa.Column('overall_score', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('cost_per_door', sa.Numeric(precision=8, scale=2), nullable=False),
        sa.Column('yards_per_door', sa.Numeric(precision=6, scale=3), nullable=False),
        sa.Column('monthly_cost', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('annual_cost', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('cost_vs_benchmark_percent', sa.Numeric(precision=6, scale=2), nullable=True),
        sa.Column('volume_vs_benchmark_percent', sa.Numeric(precision=6, scale=2), nullable=True),
        sa.Column('metrics', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('benchmark_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('terms_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('cost_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('recommendations', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('potential_annual_savings', sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column('savings_confidence', sa.Numeric(precision=3, scale=2), nullable=True),
        sa.Column('market_comparison', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('competitor_quotes', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('service_quality_score', sa.Numeric(precision=3, scale=2), nullable=True),
        sa.Column('compliance_score', sa.Numeric(precision=3, scale=2), nullable=True),
        sa.Column('vendor_performance_notes', sa.Text(), nullable=True),
        sa.Column('renewal_recommendation', renewal_decision_enum, nullable=True),
        sa.Column('recommendation_confidence', sa.Numeric(precision=3, scale=2), nullable=True),
        sa.Column('recommendation_notes', sa.Text(), nullable=True),
        sa.Column('reviewed_by', sa.String(length=255), nullable=True),
        sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('approved_by', sa.String(length=255), nullable=True),
        sa.Column('approved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.CheckConstraint('overall_score >= 0', name='check_overall_score_non_negative'),
        sa.CheckConstraint('overall_score <= 100', name='check_overall_score_lte_hundred'),
        sa.CheckConstraint('cost_per_door > 0', name='check_cost_per_door_positive'),
        sa.CheckConstraint('yards_per_door > 0', name='check_yards_per_door_positive'),
        sa.CheckConstraint('monthly_cost > 0', name='check_monthly_cost_positive'),
        sa.CheckConstraint('annual_cost > 0', name='check_annual_cost_positive'),
        sa.CheckConstraint('savings_confidence >= 0', name='check_savings_confidence_non_negative'),
        sa.CheckConstraint('savings_confidence <= 1', name='check_savings_confidence_lte_one'),
        sa.CheckConstraint('service_quality_score >= 0', name='check_service_quality_non_negative'),
        sa.CheckConstraint('service_quality_score <= 5', name='check_service_quality_lte_five'),
        sa.CheckConstraint('compliance_score >= 0', name='check_compliance_score_non_negative'),
        sa.CheckConstraint('compliance_score <= 5', name='check_compliance_score_lte_five'),
        sa.CheckConstraint('recommendation_confidence >= 0', name='check_recommendation_confidence_non_negative'),
        sa.CheckConstraint('recommendation_confidence <= 1', name='check_recommendation_confidence_lte_one'),
        sa.ForeignKeyConstraint(['contract_id'], ['contracts.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('analysis_id')
    )
    
    # Create renewal_alerts table
    op.create_table(
        'renewal_alerts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('alert_id', sa.String(length=50), nullable=False),
        sa.Column('contract_id', sa.Integer(), nullable=False),
        sa.Column('alert_date', sa.Date(), nullable=False),
        sa.Column('priority', alert_priority_enum, nullable=False),
        sa.Column('status', alert_status_enum, nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('action_required', sa.Text(), nullable=True),
        sa.Column('contract_expiration_date', sa.Date(), nullable=False),
        sa.Column('days_to_expiration', sa.Integer(), nullable=False),
        sa.Column('recommended_action_date', sa.Date(), nullable=True),
        sa.Column('escalation_level', sa.Integer(), nullable=True),
        sa.Column('escalated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('escalated_to', sa.String(length=255), nullable=True),
        sa.Column('acknowledged_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('acknowledged_by', sa.String(length=255), nullable=True),
        sa.Column('response_notes', sa.Text(), nullable=True),
        sa.Column('analysis_id', sa.Integer(), nullable=True),
        sa.Column('recommendations', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('estimated_savings', sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column('auto_generated', sa.Boolean(), nullable=True),
        sa.Column('generation_rule', sa.String(length=100), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
        sa.CheckConstraint('days_to_expiration >= -365', name='check_days_to_expiration_reasonable'),
        sa.CheckConstraint('escalation_level >= 0', name='check_escalation_level_non_negative'),
        sa.CheckConstraint('escalation_level <= 5', name='check_escalation_level_reasonable'),
        sa.CheckConstraint('estimated_savings >= 0', name='check_estimated_savings_non_negative'),
        sa.ForeignKeyConstraint(['analysis_id'], ['contract_analyses.id'], ),
        sa.ForeignKeyConstraint(['contract_id'], ['contracts.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('alert_id')
    )
    
    # Create notification_preferences table
    op.create_table(
        'notification_preferences',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=255), nullable=False),
        sa.Column('user_role', user_role_enum, nullable=False),
        sa.Column('user_name', sa.String(length=255), nullable=True),
        sa.Column('property_id', sa.Integer(), nullable=True),
        sa.Column('region', sa.String(length=100), nullable=True),
        sa.Column('is_global', sa.Boolean(), nullable=True),
        sa.Column('email_enabled', sa.Boolean(), nullable=True),
        sa.Column('email_address', sa.String(length=255), nullable=True),
        sa.Column('sms_enabled', sa.Boolean(), nullable=True),
        sa.Column('sms_number', sa.String(length=20), nullable=True),
        sa.Column('dashboard_enabled', sa.Boolean(), nullable=True),
        sa.Column('slack_enabled', sa.Boolean(), nullable=True),
        sa.Column('slack_webhook', sa.String(length=500), nullable=True),
        sa.Column('renewal_alerts', sa.Boolean(), nullable=True),
        sa.Column('escalation_alerts', sa.Boolean(), nullable=True),
        sa.Column('analysis_completed', sa.Boolean(), nullable=True),
        sa.Column('cost_anomalies', sa.Boolean(), nullable=True),
        sa.Column('performance_issues', sa.Boolean(), nullable=True),
        sa.Column('immediate_for_critical', sa.Boolean(), nullable=True),
        sa.Column('daily_digest', sa.Boolean(), nullable=True),
        sa.Column('weekly_summary', sa.Boolean(), nullable=True),
        sa.Column('monthly_report', sa.Boolean(), nullable=True),
        sa.Column('receive_low_priority', sa.Boolean(), nullable=True),
        sa.Column('receive_medium_priority', sa.Boolean(), nullable=True),
        sa.Column('receive_high_priority', sa.Boolean(), nullable=True),
        sa.Column('receive_critical_priority', sa.Boolean(), nullable=True),
        sa.Column('receive_urgent_priority', sa.Boolean(), nullable=True),
        sa.Column('quiet_hours_start', sa.String(length=5), nullable=True),
        sa.Column('quiet_hours_end', sa.String(length=5), nullable=True),
        sa.Column('timezone', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_notification_sent', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['property_id'], ['properties.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'property_id', name='uq_user_property_preferences'),
        sa.UniqueConstraint('user_id', 'region', name='uq_user_region_preferences')
    )
    
    # Create notification_logs table
    op.create_table(
        'notification_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('notification_id', sa.String(length=50), nullable=False),
        sa.Column('alert_id', sa.Integer(), nullable=True),
        sa.Column('recipient_user_id', sa.String(length=255), nullable=False),
        sa.Column('recipient_role', user_role_enum, nullable=False),
        sa.Column('notification_type', notification_type_enum, nullable=False),
        sa.Column('subject', sa.String(length=500), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('destination', sa.String(length=500), nullable=False),
        sa.Column('sent_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('opened_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('clicked_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('delivery_status', sa.String(length=50), nullable=False),
        sa.Column('delivery_response', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=True),
        sa.Column('contract_id', sa.Integer(), nullable=True),
        sa.Column('property_id', sa.Integer(), nullable=True),
        sa.Column('priority', alert_priority_enum, nullable=False),
        sa.Column('provider', sa.String(length=100), nullable=False),
        sa.Column('provider_message_id', sa.String(length=255), nullable=True),
        sa.Column('provider_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.CheckConstraint('retry_count >= 0', name='check_retry_count_non_negative'),
        sa.CheckConstraint('retry_count <= 10', name='check_retry_count_reasonable'),
        sa.ForeignKeyConstraint(['alert_id'], ['renewal_alerts.id'], ),
        sa.ForeignKeyConstraint(['contract_id'], ['contracts.id'], ),
        sa.ForeignKeyConstraint(['property_id'], ['properties.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('notification_id')
    )
    
    # Create renewal_workflows table
    op.create_table(
        'renewal_workflows',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('workflow_id', sa.String(length=50), nullable=False),
        sa.Column('contract_id', sa.Integer(), nullable=False),
        sa.Column('workflow_type', sa.String(length=50), nullable=False),
        sa.Column('current_stage', sa.String(length=100), nullable=False),
        sa.Column('initiated_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('initiated_by', sa.String(length=255), nullable=False),
        sa.Column('target_completion_date', sa.Date(), nullable=False),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('final_decision', renewal_decision_enum, nullable=True),
        sa.Column('decision_maker', sa.String(length=255), nullable=True),
        sa.Column('decision_date', sa.Date(), nullable=True),
        sa.Column('decision_notes', sa.Text(), nullable=True),
        sa.Column('stages_completed', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('stage_history', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('analysis_ids', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('document_urls', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['contract_id'], ['contracts.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('workflow_id')
    )
    
    # Create all performance indexes
    create_performance_indexes()


def create_performance_indexes():
    """Create optimized indexes for enterprise-scale performance"""
    
    # Properties indexes
    op.create_index('ix_properties_greystar_property_id', 'properties', ['greystar_property_id'])
    op.create_index('ix_properties_name', 'properties', ['name'])
    op.create_index('ix_properties_city', 'properties', ['city'])
    op.create_index('ix_properties_state', 'properties', ['state'])
    op.create_index('ix_properties_property_type', 'properties', ['property_type'])
    op.create_index('ix_properties_is_active', 'properties', ['is_active'])
    op.create_index('ix_properties_location', 'properties', ['city', 'state'])
    op.create_index('ix_properties_type_units', 'properties', ['property_type', 'unit_count'])
    
    # Vendors indexes
    op.create_index('ix_vendors_name', 'vendors', ['name'])
    op.create_index('ix_vendors_vendor_code', 'vendors', ['vendor_code'])
    op.create_index('ix_vendors_is_active', 'vendors', ['is_active'])
    
    # Contracts indexes (critical for renewal monitoring)
    op.create_index('ix_contracts_contract_number', 'contracts', ['contract_number'])
    op.create_index('ix_contracts_property_id', 'contracts', ['property_id'])
    op.create_index('ix_contracts_vendor_id', 'contracts', ['vendor_id'])
    op.create_index('ix_contracts_status', 'contracts', ['status'])
    op.create_index('ix_contracts_grade', 'contracts', ['grade'])
    op.create_index('ix_contracts_quote_number', 'contracts', ['quote_number'])
    op.create_index('ix_contracts_effective_date', 'contracts', ['effective_date'])
    op.create_index('ix_contracts_expiration_date', 'contracts', ['expiration_date'])
    op.create_index('ix_contracts_container_type', 'contracts', ['container_type'])
    
    # Critical compound indexes for renewal monitoring
    op.create_index('ix_contracts_dates', 'contracts', ['effective_date', 'expiration_date'])
    op.create_index('ix_contracts_status_expiration', 'contracts', ['status', 'expiration_date'])
    op.create_index('ix_contracts_property_vendor', 'contracts', ['property_id', 'vendor_id'])
    op.create_index('ix_contracts_renewal_monitoring', 'contracts', ['expiration_date', 'status', 'automatic_renewal'])
    op.create_index('ix_contracts_cost_analysis', 'contracts', ['calculated_cost_per_door', 'property_id'])
    
    # Contract analyses indexes
    op.create_index('ix_contract_analyses_analysis_id', 'contract_analyses', ['analysis_id'])
    op.create_index('ix_contract_analyses_contract_id', 'contract_analyses', ['contract_id'])
    op.create_index('ix_contract_analyses_analysis_type', 'contract_analyses', ['analysis_type'])
    op.create_index('ix_contract_analyses_analysis_date', 'contract_analyses', ['analysis_date'])
    op.create_index('ix_contract_analyses_overall_grade', 'contract_analyses', ['overall_grade'])
    op.create_index('ix_contract_analyses_contract_date', 'contract_analyses', ['contract_id', 'analysis_date'])
    op.create_index('ix_contract_analyses_type_grade', 'contract_analyses', ['analysis_type', 'overall_grade'])
    op.create_index('ix_contract_analyses_renewal_tracking', 'contract_analyses', ['contract_id', 'analysis_type', 'analysis_date'])
    op.create_index('ix_contract_analyses_savings_opportunities', 'contract_analyses', ['potential_annual_savings', 'renewal_recommendation'])
    op.create_index('ix_contract_analyses_performance', 'contract_analyses', ['service_quality_score', 'compliance_score'])
    
    # Renewal alerts indexes (critical for real-time monitoring)
    op.create_index('ix_renewal_alerts_alert_id', 'renewal_alerts', ['alert_id'])
    op.create_index('ix_renewal_alerts_contract_id', 'renewal_alerts', ['contract_id'])
    op.create_index('ix_renewal_alerts_alert_date', 'renewal_alerts', ['alert_date'])
    op.create_index('ix_renewal_alerts_priority', 'renewal_alerts', ['priority'])
    op.create_index('ix_renewal_alerts_status', 'renewal_alerts', ['status'])
    op.create_index('ix_renewal_alerts_contract_expiration_date', 'renewal_alerts', ['contract_expiration_date'])
    op.create_index('ix_renewal_alerts_days_to_expiration', 'renewal_alerts', ['days_to_expiration'])
    op.create_index('ix_renewal_alerts_recommended_action_date', 'renewal_alerts', ['recommended_action_date'])
    op.create_index('ix_renewal_alerts_escalation_level', 'renewal_alerts', ['escalation_level'])
    
    # Critical compound indexes for alert management
    op.create_index('ix_renewal_alerts_priority_status', 'renewal_alerts', ['priority', 'status'])
    op.create_index('ix_renewal_alerts_expiration_monitoring', 'renewal_alerts', ['contract_expiration_date', 'status'])
    op.create_index('ix_renewal_alerts_escalation', 'renewal_alerts', ['escalation_level', 'escalated_at'])
    op.create_index('ix_renewal_alerts_action_date', 'renewal_alerts', ['recommended_action_date', 'status'])
    op.create_index('ix_renewal_alerts_pending_critical', 'renewal_alerts', ['status', 'priority', 'alert_date'])
    
    # Notification preferences indexes
    op.create_index('ix_notification_preferences_user_id', 'notification_preferences', ['user_id'])
    op.create_index('ix_notification_preferences_user_role', 'notification_preferences', ['user_role'])
    op.create_index('ix_notification_preferences_property_id', 'notification_preferences', ['property_id'])
    op.create_index('ix_notification_preferences_region', 'notification_preferences', ['region'])
    op.create_index('ix_notification_preferences_user_scope', 'notification_preferences', ['user_id', 'property_id', 'region'])
    op.create_index('ix_notification_preferences_role_region', 'notification_preferences', ['user_role', 'region'])
    
    # Notification logs indexes
    op.create_index('ix_notification_logs_notification_id', 'notification_logs', ['notification_id'])
    op.create_index('ix_notification_logs_alert_id', 'notification_logs', ['alert_id'])
    op.create_index('ix_notification_logs_recipient_user_id', 'notification_logs', ['recipient_user_id'])
    op.create_index('ix_notification_logs_recipient_role', 'notification_logs', ['recipient_role'])
    op.create_index('ix_notification_logs_notification_type', 'notification_logs', ['notification_type'])
    op.create_index('ix_notification_logs_sent_at', 'notification_logs', ['sent_at'])
    op.create_index('ix_notification_logs_delivery_status', 'notification_logs', ['delivery_status'])
    op.create_index('ix_notification_logs_contract_id', 'notification_logs', ['contract_id'])
    op.create_index('ix_notification_logs_property_id', 'notification_logs', ['property_id'])
    op.create_index('ix_notification_logs_priority', 'notification_logs', ['priority'])
    op.create_index('ix_notification_logs_provider_message_id', 'notification_logs', ['provider_message_id'])
    
    # Critical compound indexes for notification tracking
    op.create_index('ix_notification_logs_recipient_date', 'notification_logs', ['recipient_user_id', 'sent_at'])
    op.create_index('ix_notification_logs_delivery_tracking', 'notification_logs', ['delivery_status', 'sent_at'])
    op.create_index('ix_notification_logs_alert_tracking', 'notification_logs', ['alert_id', 'notification_type'])
    op.create_index('ix_notification_logs_provider_tracking', 'notification_logs', ['provider', 'provider_message_id'])
    op.create_index('ix_notification_logs_contract_notifications', 'notification_logs', ['contract_id', 'sent_at'])
    
    # Renewal workflows indexes
    op.create_index('ix_renewal_workflows_workflow_id', 'renewal_workflows', ['workflow_id'])
    op.create_index('ix_renewal_workflows_contract_id', 'renewal_workflows', ['contract_id'])
    op.create_index('ix_renewal_workflows_workflow_type', 'renewal_workflows', ['workflow_type'])
    op.create_index('ix_renewal_workflows_current_stage', 'renewal_workflows', ['current_stage'])
    op.create_index('ix_renewal_workflows_initiated_at', 'renewal_workflows', ['initiated_at'])
    op.create_index('ix_renewal_workflows_target_completion_date', 'renewal_workflows', ['target_completion_date'])
    op.create_index('ix_renewal_workflows_contract_stage', 'renewal_workflows', ['contract_id', 'current_stage'])
    op.create_index('ix_renewal_workflows_timeline', 'renewal_workflows', ['target_completion_date', 'completed_at'])
    op.create_index('ix_renewal_workflows_active', 'renewal_workflows', ['workflow_type', 'current_stage', 'completed_at'])


def downgrade():
    """Drop all tables and enums"""
    
    # Drop tables in reverse dependency order
    op.drop_table('renewal_workflows')
    op.drop_table('notification_logs')
    op.drop_table('notification_preferences')
    op.drop_table('renewal_alerts')
    op.drop_table('contract_analyses')
    op.drop_table('contracts')
    op.drop_table('vendors')
    op.drop_table('properties')
    
    # Drop enums
    postgresql.ENUM(name='userrole').drop(op.get_bind())
    postgresql.ENUM(name='renewaldecision').drop(op.get_bind())
    postgresql.ENUM(name='analysistype').drop(op.get_bind())
    postgresql.ENUM(name='notificationtype').drop(op.get_bind())
    postgresql.ENUM(name='alertpriority').drop(op.get_bind())
    postgresql.ENUM(name='alertstatus').drop(op.get_bind())
    postgresql.ENUM(name='cpiindex').drop(op.get_bind())
    postgresql.ENUM(name='propertytype').drop(op.get_bind())
    postgresql.ENUM(name='containertype').drop(op.get_bind())
    postgresql.ENUM(name='contractgrade').drop(op.get_bind())
    postgresql.ENUM(name='contractstatus').drop(op.get_bind())