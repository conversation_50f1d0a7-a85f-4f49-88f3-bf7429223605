"""
Integration Test Suite for Advantage Waste Enterprise Renewal Workflows
======================================================================

End-to-end integration tests validating complete renewal workflows from
contract detection through analysis completion and stakeholder notification.

Built by Integration Testing Agent - Enterprise Development Force
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, List

# Import application components
from src.models.renewal import (
    Contract, Property, Vendor, RenewalAlert, RenewalAnalysis,
    NotificationPreference, RenewalNotification, AlertType, RenewalStatus
)
from src.services.renewal_analyzer import EnterpriseRenewalAnalyzer
from src.services.email_service import EmailService, EmailRecipient, EmailTemplate
from src.tasks.renewal_scanner import (
    scan_contract_expirations, schedule_contract_analysis,
    process_notification_queue, schedule_analysis_notification
)
from src.core.database import get_database_session


class TestCompleteRenewalWorkflow:
    """Test complete renewal workflow from detection to completion"""
    
    @pytest.fixture
    def integration_property(self):
        """Property for integration testing"""
        return Property(
            id="integration-prop-001",
            name="Lakeside Manor Apartments", 
            address="1500 Lake Shore Drive",
            city="Atlanta",
            state="GA",
            zip_code="30309",
            unit_count=318,
            property_type="mid-rise",
            greystar_property_id="GS-ATL-001",
            regional_manager_email="<EMAIL>"
        )
    
    @pytest.fixture
    def integration_vendor(self):
        """Vendor for integration testing"""
        return Vendor(
            id="integration-vendor-001",
            name="Waste Management Inc",
            contact_email="<EMAIL>",
            contact_phone="**************",
            service_areas={"GA": ["Atlanta", "Marietta", "Alpharetta"]},
            performance_history={"avg_rating": 4.2, "contracts_serviced": 45},
            financial_rating="A"
        )
    
    @pytest.fixture
    def integration_contract(self, integration_property, integration_vendor):
        """Contract for integration testing with realistic data"""
        return Contract(
            id="integration-contract-001",
            property_id=integration_property.id,
            vendor_id=integration_vendor.id,
            contract_number="WM-ATL-2024-318",
            vendor_name="Waste Management Inc",
            contact_name="Jennifer Martinez",
            contact_email="<EMAIL>",
            
            # Contract lifecycle
            contract_start_date=datetime(2024, 1, 1),
            contract_end_date=datetime.utcnow() + timedelta(days=75),  # 75 days to expiration
            contract_length_months=24,
            automatic_renewal=True,
            renewal_term_months=24,
            termination_notice_days=60,
            
            # Service configuration optimized for mid-rise property
            container_size_yards=30,
            container_type="compactor",
            container_quantity=2,
            pickup_frequency_weekly=2,
            
            # Financial terms reflecting market rates
            base_monthly_rate=Decimal("6750.00"),  # $21.23/door
            fuel_surcharge_percent=Decimal("6.5"),
            environmental_fee_percent=Decimal("3.0"),
            admin_fee=Decimal("75.00"),
            container_rental_fee=Decimal("150.00"),
            
            # Contract terms
            cpi_increases=True,
            max_annual_increase_percent=Decimal("3.5"),
            payment_terms_days=30,
            
            # Status tracking
            renewal_status=RenewalStatus.PENDING,
            renewal_analysis_completed=False,
            
            # Relationships
            property=integration_property,
            vendor=integration_vendor
        )
    
    @pytest.fixture
    def mock_database_session(self):
        """Mock database session with realistic query responses"""
        session = Mock()
        
        # Mock query builder pattern
        query_mock = Mock()
        filter_mock = Mock()
        join_mock = Mock()
        
        # Chain the mocks
        session.query.return_value = query_mock
        query_mock.filter.return_value = filter_mock
        filter_mock.join.return_value = join_mock
        join_mock.join.return_value = join_mock
        join_mock.all.return_value = []
        join_mock.first.return_value = None
        join_mock.limit.return_value = join_mock
        
        # Mock session methods
        session.add = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.close = Mock()
        
        return session
    
    @pytest.mark.asyncio
    async def test_complete_90_day_renewal_workflow(
        self, 
        integration_contract, 
        integration_property, 
        mock_database_session
    ):
        """Test complete 90-day renewal notification workflow"""
        
        # === Phase 1: Contract Expiration Detection ===
        
        # Mock the scanner task finding the contract
        mock_database_session.query.return_value.filter.return_value.join.return_value.join.return_value.all.return_value = [
            integration_contract
        ]
        
        with patch('src.tasks.renewal_scanner.get_database_session', return_value=mock_database_session):
            with patch('src.tasks.renewal_scanner.process_notification_queue.delay') as mock_process_queue:
                with patch('src.tasks.renewal_scanner.schedule_contract_analysis.delay') as mock_schedule_analysis:
                    
                    # Run the daily expiration scan
                    scan_result = scan_contract_expirations()
                    
                    # Verify scan results
                    assert scan_result['status'] == 'success'
                    assert scan_result['contracts_processed'] == 1
                    assert scan_result['alerts_created'] == 1
                    assert scan_result['analyses_scheduled'] == 1
                    
                    # Verify analysis was scheduled
                    mock_schedule_analysis.assert_called_once_with(integration_contract.id)
                    
                    # Verify notification queue processing was triggered
                    mock_process_queue.assert_called_once()
        
        # === Phase 2: Contract Analysis Execution ===
        
        # Mock vendor alternatives for analysis
        alternative_vendor = Vendor(
            id="alt-vendor-001",
            name="Republic Services",
            service_areas={"GA": ["Atlanta"]},
            contact_email="<EMAIL>"
        )
        
        with patch('src.tasks.renewal_scanner.get_database_session', return_value=mock_database_session):
            # Mock contract retrieval
            mock_database_session.query.return_value.filter.return_value.join.return_value.join.return_value.first.return_value = integration_contract
            
            # Mock alternative vendor search
            mock_database_session.query.return_value.filter.return_value.limit.return_value.all.return_value = [alternative_vendor]
            
            with patch('src.tasks.renewal_scanner.schedule_analysis_notification.delay') as mock_notify:
                
                # Execute contract analysis
                analysis_result = schedule_contract_analysis(integration_contract.id)
                
                # Verify analysis completion
                assert analysis_result['status'] == 'success'
                assert analysis_result['contract_number'] == integration_contract.contract_number
                assert 'recommendation' in analysis_result
                assert 'savings_opportunity' in analysis_result
                
                # Verify notification was scheduled
                mock_notify.assert_called_once()
        
        # === Phase 3: Stakeholder Notification ===
        
        with patch('src.services.email_service.EmailService._send_email', return_value=True) as mock_send_email:
            email_service = EmailService()
            
            # Create renewal alert
            alert = RenewalAlert(
                id="alert-integration-001",
                contract_id=integration_contract.id,
                alert_type=AlertType.NINETY_DAY.value,
                days_before_expiration=75,
                priority_level="medium"
            )
            
            # Create recipients
            recipients = [
                EmailRecipient(
                    email="<EMAIL>",
                    name="Atlanta Property Manager",
                    role="property_manager"
                ),
                EmailRecipient(
                    email="<EMAIL>", 
                    name="Southeast Regional Director",
                    role="regional_director"
                )
            ]
            
            # Send notifications to all recipients
            notification_results = []
            for recipient in recipients:
                success = await email_service.send_renewal_notification(
                    recipient=recipient,
                    contract=integration_contract,
                    alert=alert
                )
                notification_results.append(success)
            
            # Verify all notifications sent successfully
            assert all(notification_results)
            assert mock_send_email.call_count == len(recipients)
            
            # Verify email content structure
            for call in mock_send_email.call_args_list:
                args, kwargs = call
                assert 'to_email' in kwargs
                assert 'subject' in kwargs
                assert 'html_body' in kwargs
                assert 'text_body' in kwargs
                assert 'Contract Renewal' in kwargs['subject']
        
        # === Phase 4: Workflow Completion Verification ===
        
        # Verify database updates would have occurred
        mock_database_session.add.assert_called()  # Alert and analysis records added
        mock_database_session.commit.assert_called()  # Changes committed
        
        # Verify contract status updates
        assert integration_contract.renewal_analysis_completed == True
        assert integration_contract.last_performance_review is not None
    
    @pytest.mark.asyncio
    async def test_high_value_contract_executive_escalation(
        self,
        integration_contract,
        mock_database_session
    ):
        """Test executive escalation for high-value contracts with significant savings"""
        
        # Create high-value contract scenario
        integration_contract.base_monthly_rate = Decimal("15000.00")  # $180K annual
        integration_contract.contract_end_date = datetime.utcnow() + timedelta(days=25)  # Critical timeframe
        
        # === Phase 1: Analysis with High Savings Opportunity ===
        
        analyzer = EnterpriseRenewalAnalyzer(mock_database_session)
        
        # Mock alternative vendor with significant savings
        alternative_vendor = Vendor(
            id="savings-vendor-001",
            name="Premium Waste Solutions",
            service_areas={"GA": ["Atlanta"]},
            contact_email="<EMAIL>"
        )
        
        with patch.object(analyzer, '_find_alternative_vendors', return_value=[alternative_vendor]):
            with patch.object(analyzer, '_estimate_vendor_pricing') as mock_pricing:
                # Mock 25% savings opportunity
                mock_pricing.return_value = {
                    "estimated_annual_cost": Decimal("135000.00")  # $45K annual savings
                }
                
                # Perform analysis
                analysis = analyzer.analyze_renewal_opportunity(
                    contract=integration_contract,
                    property_info=integration_contract.property,
                    alternative_vendors=[alternative_vendor]
                )
                
                # Verify high savings detected
                assert analysis.savings_opportunity > 30000  # >$30K savings
                assert analysis.primary_recommendation in ['switch_vendor', 'renegotiate']
                assert analysis.renewal_urgency in ['high', 'critical']
        
        # === Phase 2: Executive Notification Trigger ===
        
        with patch('src.tasks.renewal_scanner.schedule_executive_notification.delay') as mock_exec_notification:
            with patch('src.tasks.renewal_scanner.get_database_session', return_value=mock_database_session):
                
                # Mock high-value opportunity detection
                integration_contract.renewal_status = RenewalStatus.REQUIRES_APPROVAL
                
                # Trigger executive notification
                exec_result = schedule_executive_notification(
                    integration_contract.id, 
                    "high_value_opportunity"
                )
                
                assert exec_result['status'] == 'success'
                assert exec_result['executives_notified'] > 0
        
        # === Phase 3: Verify Executive Communication ===
        
        with patch('src.services.email_service.EmailService._send_email', return_value=True) as mock_send_email:
            email_service = EmailService()
            
            executive_recipient = EmailRecipient(
                email="<EMAIL>",
                name="Waste Management Director", 
                role="executive"
            )
            
            # Send executive alert
            success = await email_service.send_executive_alert(
                recipient=executive_recipient,
                contract=integration_contract,
                alert_type="high_value_opportunity"
            )
            
            assert success == True
            mock_send_email.assert_called_once()
            
            # Verify executive alert content
            call_kwargs = mock_send_email.call_args[1]
            assert call_kwargs['priority'] == 'high'
            assert 'High-Value Savings' in call_kwargs['subject']
    
    @pytest.mark.asyncio
    async def test_overdue_contract_critical_workflow(
        self,
        integration_contract,
        mock_database_session
    ):
        """Test critical workflow for overdue contracts"""
        
        # Create overdue contract scenario
        integration_contract.contract_end_date = datetime.utcnow() - timedelta(days=15)  # 15 days overdue
        
        # === Phase 1: Overdue Detection ===
        
        mock_database_session.query.return_value.filter.return_value.join.return_value.join.return_value.all.return_value = [
            integration_contract
        ]
        
        with patch('src.tasks.renewal_scanner.get_database_session', return_value=mock_database_session):
            with patch('src.tasks.renewal_scanner.schedule_executive_notification.delay') as mock_exec_notification:
                
                # Run expiration scan
                scan_result = scan_contract_expirations()
                
                # Verify overdue contract processing
                assert scan_result['status'] == 'success'
                assert scan_result['alerts_created'] >= 1
                
                # Verify immediate executive notification
                mock_exec_notification.assert_called_with(
                    integration_contract.id, 
                    "overdue_contract"
                )
        
        # === Phase 2: Critical Alert Generation ===
        
        with patch('src.services.email_service.EmailService._send_email', return_value=True) as mock_send_email:
            email_service = EmailService()
            
            # Create critical overdue alert
            overdue_alert = RenewalAlert(
                id="alert-overdue-001",
                contract_id=integration_contract.id,
                alert_type=AlertType.OVERDUE.value,
                days_before_expiration=-15,  # Negative = overdue
                priority_level="critical"
            )
            
            # Send critical notification
            critical_recipient = EmailRecipient(
                email="<EMAIL>",
                name="Emergency Response Team",
                role="emergency"
            )
            
            success = await email_service.send_renewal_notification(
                recipient=critical_recipient,
                contract=integration_contract,
                alert=overdue_alert
            )
            
            assert success == True
            
            # Verify critical alert formatting
            call_kwargs = mock_send_email.call_args[1]
            assert 'OVERDUE' in call_kwargs['subject'].upper()
            assert 'days overdue' in call_kwargs['html_body']
    
    @pytest.mark.asyncio 
    async def test_bulk_portfolio_processing(self, mock_database_session):
        """Test bulk processing of multiple contracts across portfolio"""
        
        # === Phase 1: Create Portfolio of Contracts ===
        
        portfolio_contracts = []
        for i in range(25):  # 25 contract batch
            contract = Mock()
            contract.id = f"portfolio-contract-{i:03d}"
            contract.contract_number = f"BATCH-{i:03d}"
            contract.contract_end_date = datetime.utcnow() + timedelta(days=(i % 120))  # Spread over 120 days
            contract.renewal_status = RenewalStatus.PENDING
            contract.renewal_analysis_completed = False
            contract.property = Mock(name=f"Property {i}", unit_count=250)
            contract.vendor = Mock(name=f"Vendor {i % 5}")  # 5 vendors total
            contract.base_monthly_rate = Decimal("5000.00")
            portfolio_contracts.append(contract)
        
        # === Phase 2: Bulk Scanning Performance ===
        
        mock_database_session.query.return_value.filter.return_value.join.return_value.join.return_value.all.return_value = portfolio_contracts
        
        with patch('src.tasks.renewal_scanner.get_database_session', return_value=mock_database_session):
            with patch('src.tasks.renewal_scanner.process_notification_queue.delay'):
                
                start_time = datetime.utcnow()
                
                # Process batch
                scan_result = scan_contract_expirations()
                
                end_time = datetime.utcnow()
                processing_time = (end_time - start_time).total_seconds()
                
                # Performance assertions
                assert scan_result['status'] == 'success'
                assert scan_result['contracts_processed'] == 25
                assert processing_time < 10.0  # Should complete in under 10 seconds
                
                # Verify batch processing efficiency
                avg_time_per_contract = processing_time / 25
                assert avg_time_per_contract < 0.5  # Less than 0.5 seconds per contract
        
        # === Phase 3: Verify Scalability Characteristics ===
        
        # Calculate theoretical performance for 3,850 properties
        theoretical_time_for_full_portfolio = avg_time_per_contract * 3850
        
        # Should handle full Greystar portfolio in reasonable time
        assert theoretical_time_for_full_portfolio < 1800  # Under 30 minutes
        
        # Verify batch processing maintained data integrity
        assert mock_database_session.commit.call_count >= 1  # Batched commits
    
    def test_error_recovery_and_resilience(self, integration_contract, mock_database_session):
        """Test system resilience and error recovery capabilities"""
        
        # === Phase 1: Database Connection Failure ===
        
        mock_database_session.commit.side_effect = Exception("Database connection lost")
        
        with patch('src.tasks.renewal_scanner.get_database_session', return_value=mock_database_session):
            with pytest.raises(Exception) as exc_info:
                scan_contract_expirations()
            
            assert "Database connection lost" in str(exc_info.value)
            
            # Verify rollback was attempted
            mock_database_session.rollback.assert_called()
        
        # === Phase 2: Email Service Failure Recovery ===
        
        with patch('src.services.email_service.EmailService._send_email', return_value=False) as mock_send_email:
            email_service = EmailService()
            
            alert = RenewalAlert(
                id="alert-error-test",
                contract_id=integration_contract.id,
                alert_type=AlertType.NINETY_DAY.value,
                days_before_expiration=90,
                priority_level="medium"
            )
            
            recipient = EmailRecipient(
                email="<EMAIL>",
                name="Test User",
                role="property_manager"
            )
            
            # Email sending should fail gracefully
            success = email_service.send_renewal_notification(
                recipient=recipient,
                contract=integration_contract,
                alert=alert
            )
            
            assert success == False  # Failed gracefully without exception
            mock_send_email.assert_called_once()
        
        # === Phase 3: Partial Data Scenarios ===
        
        # Test with incomplete contract data
        incomplete_contract = Mock()
        incomplete_contract.id = "incomplete-001"
        incomplete_contract.base_monthly_rate = None  # Missing critical data
        incomplete_contract.contract_end_date = datetime.utcnow() + timedelta(days=90)
        
        analyzer = EnterpriseRenewalAnalyzer(mock_database_session)
        
        # Should handle missing data gracefully
        try:
            analysis = analyzer.analyze_renewal_opportunity(
                contract=incomplete_contract,
                property_info=integration_contract.property
            )
            # If it doesn't raise an exception, verify data quality score reflects issues
            assert analysis.data_quality_score < 80  # Should indicate data quality issues
        except Exception:
            # If it raises an exception, that's also acceptable for incomplete data
            pass


class TestApiIntegration:
    """Test API endpoint integration with renewal services"""
    
    @pytest.mark.asyncio
    async def test_renewal_dashboard_api_integration(self):
        """Test renewal dashboard API endpoint integration"""
        
        # Mock API response data
        mock_contracts = [
            {
                "id": "api-contract-001",
                "contractNumber": "API-001",
                "vendorName": "Test Vendor",
                "propertyName": "Test Property",
                "propertyType": "garden-style",
                "unitCount": 200,
                "contractEndDate": (datetime.utcnow() + timedelta(days=60)).isoformat(),
                "renewalStatus": "pending",
                "costPerDoor": 22.50,
                "annualCost": 54000,
                "urgencyLevel": "medium",
                "daysToExpiration": 60
            }
        ]
        
        mock_stats = {
            "totalContracts": 1,
            "expiringNext30Days": 0,
            "expiringNext90Days": 1,
            "totalAnnualValue": 54000,
            "averageCostPerDoor": 22.50,
            "potentialSavings": 5400
        }
        
        # Simulate API call
        api_response = {
            "contracts": mock_contracts,
            "stats": mock_stats
        }
        
        # Verify API response structure
        assert "contracts" in api_response
        assert "stats" in api_response
        assert len(api_response["contracts"]) == 1
        assert api_response["stats"]["totalContracts"] == 1
        
        # Verify contract data completeness
        contract = api_response["contracts"][0]
        required_fields = [
            "id", "contractNumber", "vendorName", "propertyName",
            "contractEndDate", "renewalStatus", "annualCost", "urgencyLevel"
        ]
        
        for field in required_fields:
            assert field in contract
            assert contract[field] is not None


class TestDataFlowIntegration:
    """Test data flow between different system components"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_data_flow(self, integration_contract, mock_database_session):
        """Test data flow from contract detection through final reporting"""
        
        data_flow_log = []
        
        # === Step 1: Contract Detection ===
        data_flow_log.append({
            "step": "detection",
            "timestamp": datetime.utcnow(),
            "data": {
                "contract_id": integration_contract.id,
                "days_to_expiration": (integration_contract.contract_end_date - datetime.utcnow()).days
            }
        })
        
        # === Step 2: Alert Creation ===
        alert = RenewalAlert(
            id="dataflow-alert-001",
            contract_id=integration_contract.id,
            alert_type=AlertType.NINETY_DAY.value,
            days_before_expiration=75,
            priority_level="medium"
        )
        
        data_flow_log.append({
            "step": "alert_creation",
            "timestamp": datetime.utcnow(),
            "data": {
                "alert_id": alert.id,
                "alert_type": alert.alert_type,
                "priority": alert.priority_level
            }
        })
        
        # === Step 3: Analysis Execution ===
        analyzer = EnterpriseRenewalAnalyzer(mock_database_session)
        
        # Mock analysis result
        with patch.object(analyzer, 'analyze_renewal_opportunity') as mock_analysis:
            mock_analysis_result = Mock()
            mock_analysis_result.id = "dataflow-analysis-001"
            mock_analysis_result.primary_recommendation = "renegotiate"
            mock_analysis_result.savings_opportunity = Decimal("8500.00")
            mock_analysis_result.recommendation_confidence = Decimal("85.0")
            
            mock_analysis.return_value = mock_analysis_result
            
            analysis_result = analyzer.analyze_renewal_opportunity(
                contract=integration_contract,
                property_info=integration_contract.property
            )
            
            data_flow_log.append({
                "step": "analysis_completion",
                "timestamp": datetime.utcnow(),
                "data": {
                    "analysis_id": analysis_result.id,
                    "recommendation": analysis_result.primary_recommendation,
                    "savings": float(analysis_result.savings_opportunity),
                    "confidence": float(analysis_result.recommendation_confidence)
                }
            })
        
        # === Step 4: Notification Delivery ===
        with patch('src.services.email_service.EmailService._send_email', return_value=True):
            email_service = EmailService()
            
            recipient = EmailRecipient(
                email="<EMAIL>",
                name="Data Flow Test User",
                role="property_manager"
            )
            
            success = await email_service.send_renewal_notification(
                recipient=recipient,
                contract=integration_contract,
                alert=alert
            )
            
            data_flow_log.append({
                "step": "notification_delivery",
                "timestamp": datetime.utcnow(),
                "data": {
                    "recipient": recipient.email,
                    "success": success,
                    "template": EmailTemplate.RENEWAL_REMINDER.value
                }
            })
        
        # === Step 5: Verification of Complete Data Flow ===
        
        # Verify all steps completed
        assert len(data_flow_log) == 4
        
        # Verify chronological order
        for i in range(1, len(data_flow_log)):
            assert data_flow_log[i]["timestamp"] >= data_flow_log[i-1]["timestamp"]
        
        # Verify data consistency across steps
        contract_id = data_flow_log[0]["data"]["contract_id"]
        assert data_flow_log[1]["data"]["alert_id"] is not None
        assert data_flow_log[2]["data"]["recommendation"] in ["renew", "renegotiate", "switch_vendor", "terminate"]
        assert data_flow_log[3]["data"]["success"] == True
        
        # Verify business logic flow
        assert data_flow_log[2]["data"]["savings"] > 0  # Savings identified
        assert data_flow_log[2]["data"]["confidence"] >= 50  # Reasonable confidence
        assert data_flow_log[3]["data"]["success"] == True  # Notification delivered


# Run integration tests
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-m", "not performance"])