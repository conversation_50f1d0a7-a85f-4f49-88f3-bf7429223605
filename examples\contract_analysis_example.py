"""
Contract Analysis Example for Advantage Waste Enterprise
=======================================================

This file demonstrates the standard patterns and methods for analyzing waste management
contracts using the Greystar Advantage Waste methodology. Claude Code will use this
as a reference for implementing contract analysis features.

Based on the Greystar Waste Reference Binder analysis frameworks.
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from decimal import Decimal
from datetime import datetime
from enum import Enum

class ContractGrade(Enum):
    """Contract quality grades based on industry benchmarks"""
    EXCELLENT = "A+"
    GOOD = "A"
    ACCEPTABLE = "B"
    NEEDS_IMPROVEMENT = "C"
    POOR = "D"

@dataclass
class ContractTerms:
    """
    Standard waste management contract terms structure.
    Based on the Contract Data Template from Greystar Reference Binder.
    """
    # Vendor Information
    vendor_name: str
    contact_name: str
    contact_info: str
    quote_number: str
    
    # Contract Terms
    contract_length_months: int
    effective_date: datetime
    automatic_renewal: bool
    renewal_term_months: int
    termination_notice_days: int
    
    # Service Details
    container_size_yards: int
    container_type: str  # "front-load", "compactor", "roll-off"
    container_quantity: int
    pickup_frequency_weekly: int
    
    # Cost Structure
    base_monthly_rate: Decimal
    fuel_surcharge_percent: Optional[float] = None
    environmental_fee_percent: Optional[float] = None
    admin_fee: Optional[Decimal] = None
    container_rental_fee: Optional[Decimal] = None
    initial_delivery_charge: Optional[Decimal] = None
    extra_pickup_cost: Optional[Decimal] = None
    
    # Price Increase Terms
    cpi_increases: bool = False
    cpi_frequency: Optional[str] = None
    cpi_index: Optional[str] = None
    max_annual_increase_percent: Optional[float] = None
    
    # Contract Provisions
    early_termination_fee_calculation: Optional[str] = None
    payment_terms_days: int = 30
    right_of_first_refusal_days: Optional[int] = None

@dataclass
class PropertyInfo:
    """Property information for contract analysis"""
    name: str
    address: str
    unit_count: int
    property_type: str  # "garden-style", "mid-rise", "high-rise"

class ContractAnalyzer:
    """
    Analyzes waste management contracts using Advantage Waste methodology.
    Implements the analysis framework from the Greystar Reference Binder.
    """
    
    # Industry benchmarks from Greystar Waste Reference Binder
    INDUSTRY_BENCHMARKS = {
        "cost_per_door_range": {
            "garden-style": (20, 30),
            "mid-rise": (15, 25), 
            "high-rise": (10, 20)
        },
        "yards_per_door_range": {
            "garden-style": (2.0, 2.25),
            "mid-rise": (1.5, 1.8),
            "high-rise": (1.0, 1.5)
        },
        "max_annual_increase": 0.04,  # 4% max recommended
        "optimal_contract_length": (12, 24),  # 12-24 months for flexibility
        "max_fuel_surcharge": 0.05,  # 5% maximum
        "max_termination_notice": 90,  # 90 days maximum
    }
    
    # Waste density factors (lbs per cubic yard)
    WASTE_DENSITY = {
        "trash_loose": 225,  # average
        "trash_compacted": 750,  # 3:1 ratio
        "mixed_recycling": 100,
        "cardboard": 100,
        "food_waste": 450
    }
    
    def analyze_contract(self, contract: ContractTerms, property: PropertyInfo) -> Dict:
        """
        Comprehensive contract analysis following Advantage Waste methodology.
        
        Args:
            contract: Contract terms to analyze
            property: Property information for context
            
        Returns:
            Complete analysis with metrics, benchmarks, and recommendations
        """
        # Calculate core metrics
        metrics = self._calculate_metrics(contract, property)
        
        # Compare against industry benchmarks
        benchmark_analysis = self._analyze_benchmarks(metrics, property.property_type)
        
        # Analyze contract terms
        terms_analysis = self._analyze_contract_terms(contract)
        
        # Generate cost efficiency analysis
        cost_analysis = self._analyze_cost_efficiency(contract, metrics)
        
        # Calculate overall grade
        overall_grade = self._calculate_overall_grade(benchmark_analysis, terms_analysis)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            contract, metrics, benchmark_analysis, terms_analysis
        )
        
        return {
            "property_info": {
                "name": property.name,
                "units": property.unit_count,
                "type": property.property_type
            },
            "metrics": metrics,
            "benchmark_analysis": benchmark_analysis,
            "terms_analysis": terms_analysis,
            "cost_analysis": cost_analysis,
            "overall_grade": overall_grade.value,
            "recommendations": recommendations,
            "analysis_date": datetime.now().isoformat()
        }
    
    def _calculate_metrics(self, contract: ContractTerms, property: PropertyInfo) -> Dict:
        """Calculate key waste management metrics"""
        # Monthly service volume calculation
        # Formula: Container Size × Quantity × Pickups/Week × 4.33 weeks/month
        weekly_volume = (contract.container_size_yards * 
                        contract.container_quantity * 
                        contract.pickup_frequency_weekly)
        monthly_volume = Decimal(str(weekly_volume * 4.33))
        
        # Calculate total monthly cost including fees
        total_monthly_cost = contract.base_monthly_rate
        
        if contract.fuel_surcharge_percent:
            fuel_charge = contract.base_monthly_rate * Decimal(str(contract.fuel_surcharge_percent / 100))
            total_monthly_cost += fuel_charge
            
        if contract.environmental_fee_percent:
            env_fee = contract.base_monthly_rate * Decimal(str(contract.environmental_fee_percent / 100))
            total_monthly_cost += env_fee
            
        if contract.admin_fee:
            total_monthly_cost += contract.admin_fee
            
        if contract.container_rental_fee:
            total_monthly_cost += contract.container_rental_fee
        
        # Key efficiency metrics
        cost_per_yard = total_monthly_cost / monthly_volume
        cost_per_door = total_monthly_cost / property.unit_count
        yards_per_door = monthly_volume / property.unit_count
        annual_cost = total_monthly_cost * 12
        
        return {
            "monthly_volume_yards": float(monthly_volume),
            "total_monthly_cost": float(total_monthly_cost),
            "base_monthly_cost": float(contract.base_monthly_rate),
            "cost_per_yard": float(cost_per_yard),
            "cost_per_door": float(cost_per_door),
            "yards_per_door": float(yards_per_door),
            "annual_cost": float(annual_cost),
            "weekly_capacity": weekly_volume,
            "fuel_surcharge_amount": float(contract.base_monthly_rate * Decimal(str(contract.fuel_surcharge_percent / 100))) if contract.fuel_surcharge_percent else 0,
            "environmental_fee_amount": float(contract.base_monthly_rate * Decimal(str(contract.environmental_fee_percent / 100))) if contract.environmental_fee_percent else 0
        }
    
    def _analyze_benchmarks(self, metrics: Dict, property_type: str) -> Dict:
        """Compare metrics against industry benchmarks"""
        cost_benchmark = self.INDUSTRY_BENCHMARKS["cost_per_door_range"].get(
            property_type, self.INDUSTRY_BENCHMARKS["cost_per_door_range"]["mid-rise"]
        )
        volume_benchmark = self.INDUSTRY_BENCHMARKS["yards_per_door_range"].get(
            property_type, self.INDUSTRY_BENCHMARKS["yards_per_door_range"]["mid-rise"]
        )
        
        cost_status = "within_range" if cost_benchmark[0] <= metrics["cost_per_door"] <= cost_benchmark[1] else "outside_range"
        volume_status = "within_range" if volume_benchmark[0] <= metrics["yards_per_door"] <= volume_benchmark[1] else "outside_range"
        
        return {
            "cost_per_door": {
                "value": metrics["cost_per_door"],
                "benchmark_range": cost_benchmark,
                "status": cost_status,
                "variance_percent": self._calculate_variance(metrics["cost_per_door"], cost_benchmark)
            },
            "yards_per_door": {
                "value": metrics["yards_per_door"],
                "benchmark_range": volume_benchmark,
                "status": volume_status,
                "variance_percent": self._calculate_variance(metrics["yards_per_door"], volume_benchmark)
            }
        }
    
    def _analyze_contract_terms(self, contract: ContractTerms) -> Dict:
        """Analyze contract terms against best practices"""
        analysis = {}
        
        # Contract length analysis
        optimal_length = self.INDUSTRY_BENCHMARKS["optimal_contract_length"]
        length_status = "optimal" if optimal_length[0] <= contract.contract_length_months <= optimal_length[1] else "suboptimal"
        
        analysis["contract_length"] = {
            "months": contract.contract_length_months,
            "status": length_status,
            "optimal_range": optimal_length
        }
        
        # Price increase protection
        max_increase = contract.max_annual_increase_percent or 100  # No cap = 100%
        increase_status = "good" if max_increase <= self.INDUSTRY_BENCHMARKS["max_annual_increase"] * 100 else "poor"
        
        analysis["price_increases"] = {
            "max_annual_percent": max_increase,
            "status": increase_status,
            "benchmark_max": self.INDUSTRY_BENCHMARKS["max_annual_increase"] * 100
        }
        
        # Fuel surcharge analysis
        fuel_surcharge = contract.fuel_surcharge_percent or 0
        fuel_status = "good" if fuel_surcharge <= self.INDUSTRY_BENCHMARKS["max_fuel_surcharge"] * 100 else "poor"
        
        analysis["fuel_surcharge"] = {
            "percent": fuel_surcharge,
            "status": fuel_status,
            "benchmark_max": self.INDUSTRY_BENCHMARKS["max_fuel_surcharge"] * 100
        }
        
        # Termination notice
        notice_status = "good" if contract.termination_notice_days <= self.INDUSTRY_BENCHMARKS["max_termination_notice"] else "poor"
        
        analysis["termination_notice"] = {
            "days": contract.termination_notice_days,
            "status": notice_status,
            "benchmark_max": self.INDUSTRY_BENCHMARKS["max_termination_notice"]
        }
        
        return analysis
    
    def _analyze_cost_efficiency(self, contract: ContractTerms, metrics: Dict) -> Dict:
        """Analyze cost efficiency and identify savings opportunities"""
        efficiency_score = 0
        factors = []
        
        # Analyze cost components
        base_cost_ratio = metrics["base_monthly_cost"] / metrics["total_monthly_cost"]
        fees_ratio = 1 - base_cost_ratio
        
        if fees_ratio < 0.1:  # Less than 10% fees
            efficiency_score += 25
            factors.append("Low fee structure")
        elif fees_ratio > 0.2:  # More than 20% fees
            factors.append("High fee burden")
        
        # Container utilization efficiency
        theoretical_max_volume = contract.container_size_yards * contract.pickup_frequency_weekly * 4.33
        utilization = metrics["monthly_volume_yards"] / theoretical_max_volume
        
        if utilization > 0.8:  # Good utilization
            efficiency_score += 25
            factors.append("High container utilization")
        elif utilization < 0.5:  # Poor utilization
            factors.append("Underutilized containers")
        
        return {
            "efficiency_score": efficiency_score,
            "base_cost_ratio": base_cost_ratio,
            "fees_ratio": fees_ratio,
            "container_utilization": utilization,
            "efficiency_factors": factors
        }
    
    def _calculate_overall_grade(self, benchmark_analysis: Dict, terms_analysis: Dict) -> ContractGrade:
        """Calculate overall contract grade"""
        score = 0
        
        # Benchmark scoring (40 points)
        if benchmark_analysis["cost_per_door"]["status"] == "within_range":
            score += 20
        if benchmark_analysis["yards_per_door"]["status"] == "within_range":
            score += 20
        
        # Terms scoring (60 points)
        terms_scores = {
            "optimal": 15,
            "good": 15,
            "poor": 0,
            "suboptimal": 5
        }
        
        for term_analysis in terms_analysis.values():
            if isinstance(term_analysis, dict) and "status" in term_analysis:
                score += terms_scores.get(term_analysis["status"], 0)
        
        # Grade assignment
        if score >= 90:
            return ContractGrade.EXCELLENT
        elif score >= 80:
            return ContractGrade.GOOD
        elif score >= 70:
            return ContractGrade.ACCEPTABLE
        elif score >= 60:
            return ContractGrade.NEEDS_IMPROVEMENT
        else:
            return ContractGrade.POOR
    
    def _generate_recommendations(self, contract: ContractTerms, metrics: Dict, 
                                benchmark_analysis: Dict, terms_analysis: Dict) -> List[str]:
        """Generate specific recommendations for contract improvement"""
        recommendations = []
        
        # Cost optimization recommendations
        if benchmark_analysis["cost_per_door"]["status"] == "outside_range":
            variance = benchmark_analysis["cost_per_door"]["variance_percent"]
            if variance > 0:
                recommendations.append(f"Cost per door is {variance:.1f}% above benchmark - negotiate lower rates")
        
        # Contract term recommendations
        if terms_analysis["contract_length"]["status"] == "suboptimal":
            if contract.contract_length_months > 24:
                recommendations.append("Consider shorter contract term (12-24 months) for better flexibility")
        
        # Price increase recommendations
        if terms_analysis["price_increases"]["status"] == "poor":
            recommendations.append("Negotiate better price increase protection (CPI or 4% cap)")
        
        # Fee structure recommendations
        if contract.fuel_surcharge_percent and contract.fuel_surcharge_percent > 5:
            recommendations.append("Fuel surcharge is high - consider contracts with capped or eliminated fuel fees")
        
        if contract.environmental_fee_percent and contract.environmental_fee_percent > 3:
            recommendations.append("Environmental fees add unnecessary cost - negotiate removal")
        
        # Service optimization recommendations
        if metrics["yards_per_door"] > 2.5:
            recommendations.append("High volume per door - consider waste reduction or recycling programs")
        
        # Add potential annual savings estimate
        potential_savings = self._calculate_potential_savings(metrics, benchmark_analysis)
        if potential_savings > 0:
            recommendations.append(f"Potential annual savings: ${potential_savings:,.2f} ({potential_savings/metrics['annual_cost']*100:.1f}%)")
        
        return recommendations
    
    def _calculate_variance(self, value: float, benchmark_range: Tuple[float, float]) -> float:
        """Calculate percentage variance from benchmark range"""
        if value < benchmark_range[0]:
            return ((benchmark_range[0] - value) / benchmark_range[0]) * -100
        elif value > benchmark_range[1]:
            return ((value - benchmark_range[1]) / benchmark_range[1]) * 100
        else:
            return 0.0  # Within range
    
    def _calculate_potential_savings(self, metrics: Dict, benchmark_analysis: Dict) -> float:
        """Calculate potential annual savings if contract was optimized"""
        current_annual = metrics["annual_cost"]
        
        # Use midpoint of benchmark range as target
        cost_benchmark = benchmark_analysis["cost_per_door"]["benchmark_range"]
        target_cost_per_door = (cost_benchmark[0] + cost_benchmark[1]) / 2
        
        # Estimate savings if cost per door was at target
        variance = benchmark_analysis["cost_per_door"]["variance_percent"]
        if variance > 0:  # Only calculate savings if above benchmark
            target_annual = target_cost_per_door * 12  # Rough estimate
            return max(0, current_annual - target_annual)
        
        return 0.0

# Example usage patterns for Claude Code
def analyze_example_contracts():
    """Example contract analysis scenarios for Claude Code reference"""
    
    # Example property
    property_info = PropertyInfo(
        name="Columbia Square Living",
        address="123 Main St, Dallas, TX",
        unit_count=252,
        property_type="garden-style"
    )
    
    # Example Contract 1: GFL Environmental (from reference document)
    gfl_contract = ContractTerms(
        vendor_name="GFL Environmental",
        contact_name="John Smith",
        contact_info="<EMAIL>",
        quote_number="GFL-2024-001",
        contract_length_months=60,
        effective_date=datetime(2024, 1, 1),
        automatic_renewal=True,
        renewal_term_months=12,
        termination_notice_days=30,
        container_size_yards=34,
        container_type="compactor",
        container_quantity=1,
        pickup_frequency_weekly=1,  # On-call approximated as weekly
        base_monthly_rate=Decimal("663.91"),
        fuel_surcharge_percent=8.0,
        environmental_fee_percent=5.0,
        extra_pickup_cost=Decimal("246.75"),
        max_annual_increase_percent=6.0
    )
    
    # Example Contract 2: Waste Management (from reference document)
    wm_contract = ContractTerms(
        vendor_name="Waste Management",
        contact_name="Jane Doe",
        contact_info="<EMAIL>", 
        quote_number="WM-2024-002",
        contract_length_months=12,
        effective_date=datetime(2024, 1, 1),
        automatic_renewal=True,
        renewal_term_months=12,
        termination_notice_days=30,
        container_size_yards=34,
        container_type="compactor",
        container_quantity=1,
        pickup_frequency_weekly=1,
        base_monthly_rate=Decimal("745.00"),
        container_rental_fee=Decimal("650.00"),
        fuel_surcharge_percent=None,  # Prohibited
        environmental_fee_percent=None,  # Prohibited
        extra_pickup_cost=Decimal("195.00"),
        max_annual_increase_percent=4.0
    )
    
    # Analyze both contracts
    analyzer = ContractAnalyzer()
    
    print("=== GFL Environmental Analysis ===")
    gfl_analysis = analyzer.analyze_contract(gfl_contract, property_info)
    print(f"Grade: {gfl_analysis['overall_grade']}")
    print(f"Cost per door: ${gfl_analysis['metrics']['cost_per_door']:.2f}")
    print(f"Annual cost: ${gfl_analysis['metrics']['annual_cost']:.2f}")
    print("Recommendations:")
    for rec in gfl_analysis['recommendations']:
        print(f"  - {rec}")
    
    print("\n=== Waste Management Analysis ===")
    wm_analysis = analyzer.analyze_contract(wm_contract, property_info)
    print(f"Grade: {wm_analysis['overall_grade']}")
    print(f"Cost per door: ${wm_analysis['metrics']['cost_per_door']:.2f}")
    print(f"Annual cost: ${wm_analysis['metrics']['annual_cost']:.2f}")
    print("Recommendations:")
    for rec in wm_analysis['recommendations']:
        print(f"  - {rec}")
    
    # Calculate savings comparison
    savings = gfl_analysis['metrics']['annual_cost'] - wm_analysis['metrics']['annual_cost']
    print(f"\nAnnual savings with Waste Management: ${savings:.2f} ({savings/gfl_analysis['metrics']['annual_cost']*100:.1f}%)")

if __name__ == "__main__":
    analyze_example_contracts()
