# Advantage Waste Enterprise

Enterprise-grade waste management solution for Greystar's multifamily property portfolio, built with context engineering and Claude Code optimization.

## 🎯 Project Overview

This application implements the complete waste management workflow from the Greystar Waste Reference Binder, providing:

- **Contract Analysis & Comparison**: Automated analysis of vendor proposals with industry benchmarking
- **Cost Optimization**: Identification of savings opportunities across 3,850+ properties  
- **Performance Analytics**: Real-time KPI tracking and reporting
- **Vendor Management**: Streamlined supplier relationships and service optimization
- **Compliance Monitoring**: Regulatory compliance and operational risk management

Built specifically for Greystar's Advantage Waste division using proven enterprise patterns.

## 🏗️ Architecture

- **Backend**: FastAPI with SQLAlchemy, PostgreSQL
- **Frontend**: React/TypeScript with Vite and Tailwind CSS
- **Integration**: RESTful APIs with existing Advantage Waste portal
- **Deployment**: Docker containers with systematic GitHub integration
- **AI Integration**: Claude Code with context engineering for rapid development

## 📁 Project Structure

```
advantage-waste-enterprise/
├── .claude/                     # Claude Code configuration
│   ├── commands/               # Custom slash commands
│   └── settings.local.json     # Tool permissions and project context
├── PRPs/                       # Product Requirements Prompts
│   ├── templates/             # PRP templates for feature development
│   ├── completed/             # Historical PRPs for reference
│   └── active/                # Current development PRPs
├── backend/                    # Enterprise FastAPI backend
├── frontend/                   # React/TypeScript frontend
├── examples/                   # Code patterns for Claude Code
├── docs/                       # Comprehensive documentation
├── scripts/                    # Automation and deployment scripts
├── research/                   # Context engineering documentation
├── CLAUDE.md                   # Global Claude Code rules and context
└── INITIAL.md                  # Feature request template
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Node.js 18+
- PostgreSQL 14+
- Docker (optional)
- Claude Code (for AI-assisted development)

### Setup

1. **Clone and navigate to project**
   ```bash
   cd "C:\Users\<USER>\Documents\Claude Code\advantage-waste-enterprise"
   ```

2. **Set up backend environment**
   ```bash
   cd backend
   python -m venv venv
   venv\Scripts\activate  # Windows
   pip install -r requirements.txt
   ```

3. **Set up frontend environment**
   ```bash
   cd frontend
   npm install
   ```

4. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your database and API keys
   ```

5. **Initialize database**
   ```bash
   cd backend
   alembic upgrade head
   ```

6. **Start development servers**
   ```bash
   # Backend (terminal 1)
   cd backend
   uvicorn src.main:app --reload

   # Frontend (terminal 2)  
   cd frontend
   npm run dev
   ```

## 🤖 Claude Code Development

This project is optimized for Claude Code with context engineering:

### Available Commands

- `/analyze-contract [file]` - Analyze waste management contracts
- `/generate-prp [feature]` - Create Product Requirements Prompts  
- `/execute-prp [prp-file]` - Implement features from PRPs
- `/optimize-waste [property]` - Optimize waste operations
- `/generate-report [type]` - Create business reports

### Development Workflow

1. **Define Feature**: Update `INITIAL.md` with your requirements
2. **Generate PRP**: Run `/generate-prp INITIAL.md` in Claude Code
3. **Review PRP**: Claude will research and create comprehensive implementation plan
4. **Execute PRP**: Run `/execute-prp PRPs/your-feature.md` to implement
5. **Test & Validate**: Run automated tests and validation
6. **Commit**: Push to GitHub with systematic commits

### Example Patterns

The `examples/` directory contains reference implementations:

- `contract_analysis_example.py` - Complete contract analysis workflow
- `waste_calculation_example.py` - Industry-standard calculations
- `property_optimization_example.py` - Property-specific optimizations

Claude Code uses these patterns to generate consistent, production-ready code.

## 📊 Industry Context

### Key Metrics
- **Cost per door**: $10-30 per unit monthly (varies by property type)
- **Yards per door**: 1.0-2.5 cubic yards per unit monthly  
- **Pickup frequency**: 1-3 times per week typical
- **Container sizes**: 2, 4, 6, 8 yards (front-load), 30-34 yards (compactor)

### Calculation Formulas
- **Monthly volume**: Container Size × Quantity × Pickups/Week × 4.33
- **Cost per yard**: Total Monthly Cost ÷ Total Monthly Volume
- **Cost per door**: Total Monthly Cost ÷ Number of Units

### Property Types
- **Garden-style**: 2.0-2.25 yd³/door, higher costs
- **Mid-rise**: ~1.5 yd³/door, moderate costs
- **High-rise**: 1.0-1.5 yd³/door, lower costs

## 🔧 API Endpoints

### Contract Management
- `POST /api/contracts/analyze` - Analyze contract terms
- `GET /api/contracts/compare` - Compare multiple contracts
- `POST /api/contracts/upload` - Upload contract documents

### Property Management  
- `GET /api/properties/{id}/metrics` - Property waste metrics
- `POST /api/properties/{id}/optimize` - Optimize property operations
- `GET /api/properties/{id}/contracts` - Property contract history

### Analytics & Reporting
- `GET /api/analytics/dashboard` - Executive dashboard data
- `POST /api/reports/generate` - Generate custom reports
- `GET /api/analytics/benchmarks` - Industry benchmarks

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest tests/ -v --cov=src

# Frontend tests  
cd frontend
npm test

# Integration tests
npm run test:e2e
```

## 🚢 Deployment

### Local Development
```bash
docker-compose up -d
```

### Production
```bash
# Build containers
docker build -t advantage-waste-api ./backend
docker build -t advantage-waste-ui ./frontend

# Deploy with orchestration platform
kubectl apply -f k8s/
```

## 📈 Business Impact

This solution directly supports Greystar's Advantage Waste goals:

- **Cost Savings**: Typical 10%+ reduction through optimized vendor negotiations
- **Operational Efficiency**: Streamlined workflows for property managers
- **Data-Driven Decisions**: Real-time analytics and benchmarking
- **Scalability**: Enterprise-grade solution for 3,850+ properties
- **Risk Mitigation**: Automated compliance and performance monitoring

## 🔗 Integration

### Existing Systems
- **Advantage Waste Portal**: Single sign-on and user management
- **Greystar Property Management**: Property data synchronization  
- **Financial Systems**: Cost tracking and reporting integration
- **Vendor APIs**: Direct integration with waste management companies

### Frontend Integration
Your existing Lovable frontend can be integrated by:
1. Copying project files to `frontend/` directory
2. Updating API endpoints to use new backend
3. Adding new components for contract analysis features

## 📝 Documentation

- **API Documentation**: Auto-generated OpenAPI/Swagger docs at `/docs`
- **User Guides**: Step-by-step workflows in `docs/user-guide/`
- **Technical Docs**: Architecture and integration guides in `docs/technical/`
- **Industry Standards**: Waste management references in `docs/industry/`

## 🤝 Contributing

This project uses Claude Code for development:

1. **Create Feature Request**: Update `INITIAL.md` with requirements
2. **Generate PRP**: Use `/generate-prp` command for implementation plan
3. **Implement**: Use `/execute-prp` for AI-assisted development
4. **Test**: Comprehensive testing with realistic scenarios
5. **Document**: Update documentation and examples
6. **Review**: Code review with focus on business logic accuracy

## 📞 Support

For questions about Advantage Waste requirements or Greystar integration:
- **Technical**: Use Claude Code for implementation assistance
- **Business Logic**: Reference Greystar Waste Reference Binder
- **Industry Standards**: Consult `examples/` and `docs/industry/`

## 🔒 Security & Compliance

- **Data Encryption**: All sensitive data encrypted at rest and in transit
- **Access Control**: Role-based permissions (property managers, directors, admins)
- **Audit Logging**: All financial calculations and decisions logged
- **Data Retention**: Corporate data retention policies enforced
- **Privacy**: Vendor and property data handled with appropriate controls

---

**Built with ❤️ for Greystar's Advantage Waste division**

*Leveraging context engineering and Claude Code for enterprise-grade waste management solutions*
