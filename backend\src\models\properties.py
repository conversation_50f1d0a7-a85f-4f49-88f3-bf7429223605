"""
Property-related database models for Advantage Waste Enterprise.
Handles Greystar property data and waste management metrics.
"""

from sqlalchemy import (
    Column, Integer, String, Numeric, Boolean, Date, DateTime, 
    JSON, Index, Text
)
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from datetime import date
from typing import Optional, Dict, Any

from ..core.database import Base
from .base import TimestampMixin


class PropertyType(PyEnum):
    """Property type enumeration based on Greystar categories"""
    GARDEN_STYLE = "garden_style"
    MID_RISE = "mid_rise"
    HIGH_RISE = "high_rise"
    MIXED_USE = "mixed_use"
    STUDENT_HOUSING = "student_housing"
    SENIOR_LIVING = "senior_living"


class Property(Base, TimestampMixin):
    """
    Greystar property model.
    Represents multifamily properties in the Greystar portfolio.
    """
    __tablename__ = "properties"

    id = Column(Integer, primary_key=True, index=True)
    
    # Property identification
    greystar_property_id = Column(String(20), unique=True, nullable=False, index=True)
    property_name = Column(String(200), nullable=False, index=True)
    property_code = Column(String(10), nullable=True)
    
    # Location details
    address = Column(Text, nullable=False)
    city = Column(String(100), nullable=False, index=True)
    state = Column(String(2), nullable=False, index=True)
    zip_code = Column(String(10), nullable=False)
    market = Column(String(50), nullable=True, index=True)
    region = Column(String(50), nullable=True, index=True)
    
    # Property characteristics
    property_type = Column(String(20), nullable=False)  # Using string for flexibility
    unit_count = Column(Integer, nullable=False)
    building_count = Column(Integer, nullable=True)
    square_footage = Column(Integer, nullable=True)
    year_built = Column(Integer, nullable=True)
    
    # Waste management specifics
    dumpster_area_count = Column(Integer, nullable=True)
    has_compactor = Column(Boolean, default=False)
    has_recycling_program = Column(Boolean, default=True)
    waste_room_access = Column(String(20), nullable=True)  # ground, basement, etc.
    
    # Property management
    property_manager = Column(String(100), nullable=True)
    regional_manager = Column(String(100), nullable=True)
    waste_coordinator = Column(String(100), nullable=True)
    
    # Contact information
    primary_contact_email = Column(String(200), nullable=True)
    property_phone = Column(String(20), nullable=True)
    
    # Operational status
    is_active = Column(Boolean, default=True, nullable=False)
    lease_up_date = Column(Date, nullable=True)
    occupancy_rate = Column(Numeric(5, 2), nullable=True)  # As percentage
    
    # Additional metadata
    property_metadata = Column(JSON, nullable=True)
    greystar_systems_data = Column(JSON, nullable=True)
    
    # Indexes for performance
    __table_args__ = (
        Index("idx_property_state_city", "state", "city"),
        Index("idx_property_market_region", "market", "region"),
        Index("idx_property_manager", "property_manager"),
        Index("idx_property_type_units", "property_type", "unit_count"),
    )
    
    @property
    def estimated_monthly_volume(self) -> float:
        """
        Estimate monthly waste volume based on property type and unit count.
        Uses industry benchmarks for waste generation.
        """
        # Industry benchmarks (cubic yards per door per month)
        volume_per_door = {
            "garden_style": 2.125,      # 2.0-2.25 range
            "mid_rise": 1.5,            # ~1.5 range
            "high_rise": 1.25,          # 1.0-1.5 range
            "mixed_use": 1.5,           # Similar to mid-rise
            "student_housing": 2.0,     # Higher generation
            "senior_living": 1.0,       # Lower generation
        }
        
        multiplier = volume_per_door.get(self.property_type, 1.5)
        return float(self.unit_count * multiplier)
    
    @property
    def target_cost_per_door(self) -> float:
        """
        Target cost per door based on property type and market conditions.
        Uses Advantage Waste benchmarks.
        """
        # Target cost ranges (per door per month)
        cost_targets = {
            "garden_style": 22.0,       # Higher costs due to accessibility
            "mid_rise": 18.0,           # Moderate costs
            "high_rise": 15.0,          # Lower costs per door
            "mixed_use": 20.0,          # Variable based on commercial mix
            "student_housing": 25.0,    # Higher due to turnover
            "senior_living": 16.0,      # Lower generation
        }
        
        return cost_targets.get(self.property_type, 18.0)


class PropertyMetrics(Base, TimestampMixin):
    """
    Property waste management metrics and performance tracking.
    Stores monthly metrics for analysis and optimization.
    """
    __tablename__ = "property_metrics"

    id = Column(Integer, primary_key=True, index=True)
    
    # Property and time period
    greystar_property_id = Column(String(20), nullable=False, index=True)
    metric_date = Column(Date, nullable=False, index=True)
    
    # Basic metrics
    unit_count = Column(Integer, nullable=False)
    occupancy_rate = Column(Numeric(5, 2), nullable=True)
    occupied_units = Column(Integer, nullable=True)
    
    # Waste volume metrics
    total_volume_yards = Column(Numeric(8, 2), nullable=True)
    volume_per_door = Column(Numeric(5, 2), nullable=True)
    volume_per_occupied_door = Column(Numeric(5, 2), nullable=True)
    
    # Cost metrics
    total_waste_cost = Column(Numeric(10, 2), nullable=True)
    cost_per_door = Column(Numeric(8, 2), nullable=True)
    cost_per_yard = Column(Numeric(8, 2), nullable=True)
    cost_per_occupied_door = Column(Numeric(8, 2), nullable=True)
    
    # Service metrics
    pickup_count = Column(Integer, nullable=True)
    missed_pickups = Column(Integer, default=0)
    service_requests = Column(Integer, default=0)
    service_complaints = Column(Integer, default=0)
    
    # Container metrics
    container_count = Column(Integer, nullable=True)
    container_size_avg = Column(Numeric(4, 1), nullable=True)
    utilization_rate = Column(Numeric(5, 2), nullable=True)  # As percentage
    
    # Environmental metrics
    recycling_volume = Column(Numeric(8, 2), nullable=True)
    recycling_rate = Column(Numeric(5, 2), nullable=True)  # As percentage
    diversion_rate = Column(Numeric(5, 2), nullable=True)  # As percentage
    
    # Performance scores
    cost_performance_score = Column(Numeric(3, 1), nullable=True)  # 1-10 scale
    service_performance_score = Column(Numeric(3, 1), nullable=True)
    environmental_score = Column(Numeric(3, 1), nullable=True)
    overall_score = Column(Numeric(3, 1), nullable=True)
    
    # Benchmark comparisons
    cost_vs_benchmark = Column(Numeric(6, 2), nullable=True)  # Percentage variance
    volume_vs_benchmark = Column(Numeric(6, 2), nullable=True)
    peer_ranking = Column(Integer, nullable=True)
    
    # Additional data
    notes = Column(Text, nullable=True)
    data_source = Column(String(50), nullable=True)
    verified = Column(Boolean, default=False)
    metrics_metadata = Column(JSON, nullable=True)
    
    # Indexes for performance
    __table_args__ = (
        Index("idx_metrics_property_date", "greystar_property_id", "metric_date"),
        Index("idx_metrics_date", "metric_date"),
        Index("idx_metrics_cost_performance", "cost_performance_score"),
    )
    
    @property
    def efficiency_ratio(self) -> float:
        """Calculate waste efficiency ratio (volume per occupied door)"""
        if self.occupied_units and self.occupied_units > 0 and self.total_volume_yards:
            return float(self.total_volume_yards / self.occupied_units)
        return 0.0
    
    @property
    def cost_efficiency(self) -> float:
        """Calculate cost efficiency (cost per yard)"""
        if self.total_volume_yards and self.total_volume_yards > 0 and self.total_waste_cost:
            return float(self.total_waste_cost / self.total_volume_yards)
        return 0.0