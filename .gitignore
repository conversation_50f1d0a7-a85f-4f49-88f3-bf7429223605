# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# React/Vite
dist/
build/
.vite/
.cache/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database
*.sqlite
*.sqlite3
*.db

# Logs
*.log
logs/

# Testing
.coverage
.pytest_cache/
.nyc_output/
coverage/

# Docker
.dockerignore

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Claude Code generated files (optional)
# .claude/sessions/
# .claude/cache/

# Waste management specific
contracts/uploaded/
reports/generated/
data/exports/

# Security
*.pem
*.key
*.crt
secrets.json
credentials.json

# Backup files
*.bak
*.backup
