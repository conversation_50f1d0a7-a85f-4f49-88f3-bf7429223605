# Analyze Waste Contract

Analyze a waste management contract using the Advantage Waste methodology and generate a comprehensive analysis report.

## Arguments: $ARGUMENTS

The first argument should be the path to a contract file (PDF, Excel, or text) or contract data.

## Process

1. **Read the contract data** specified in $ARGUMENTS
2. **Extract key contract terms** using the ContractTerms structure from examples/contract_analysis_example.py
3. **Calculate efficiency metrics** following the industry formulas in CLAUDE.md
4. **Compare against benchmarks** using property type and unit count
5. **Analyze contract terms** for compliance with best practices
6. **Generate recommendations** for optimization and cost savings
7. **Create analysis report** with clear metrics and actionable insights

## Key Data to Extract

- Vendor information (name, contact, quote number)
- Contract terms (length, renewal, termination notice)
- Service details (container size, quantity, pickup frequency)
- Cost structure (base rate, fees, surcharges)
- Price increase provisions
- Special terms and conditions

## Calculations to Perform

- Monthly service volume: Container Size × Quantity × Frequency × 4.33
- Cost per cubic yard: Total Monthly Cost ÷ Monthly Volume
- Cost per door: Total Monthly Cost ÷ Property Units  
- Yards per door: Monthly Volume ÷ Property Units
- Annual cost projection with fees and increases

## Industry Benchmarks to Apply

From CLAUDE.md:
- Cost per door: $10-30/unit monthly (varies by property type)
- Yards per door: 1.0-2.5 cubic yards/unit monthly
- Contract length: 12-24 months optimal
- Price increases: ≤4% annually
- Fuel surcharge: ≤5%

## Output Format

Generate a structured analysis report including:

1. **Executive Summary**
   - Overall contract grade (A+ to D)
   - Key metrics summary
   - Top 3 recommendations

2. **Financial Analysis**
   - Cost breakdown with fees
   - Benchmark comparison
   - Potential savings opportunities

3. **Contract Terms Review** 
   - Contract length assessment
   - Price protection analysis
   - Termination provisions
   - Risk factors

4. **Recommendations**
   - Specific improvement opportunities
   - Negotiation priorities
   - Alternative strategies

5. **Appendix**
   - Detailed calculations
   - Industry benchmark references
   - Supporting documentation

## Code Pattern to Follow

Use the ContractAnalyzer class from examples/contract_analysis_example.py as the foundation. This provides:

- Standard data structures (ContractTerms, PropertyInfo)
- Industry benchmark constants
- Calculation methods following Greystar methodology
- Comprehensive analysis framework
- Professional reporting format

## Validation

- Verify all calculations use proper decimal precision
- Check that benchmarks match property type
- Ensure recommendations are specific and actionable
- Validate that analysis follows Advantage Waste standards
- Test with realistic contract scenarios (200+ unit properties)

## Integration Notes

- Support multiple input formats (PDF parsing, Excel upload, manual entry)
- Generate reports suitable for property managers and executives
- Include data that supports vendor negotiations
- Format output for integration with Greystar systems
- Maintain audit trail of all analyses performed

Remember: This analysis directly impacts multi-million dollar waste management decisions across Greystar's 3,850+ properties. Accuracy and professional presentation are critical.
