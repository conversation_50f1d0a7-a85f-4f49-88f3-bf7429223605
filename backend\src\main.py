"""
Advantage Waste Enterprise API
==============================

Enterprise waste management solution for Greystar's multifamily property portfolio.
This FastAPI application provides contract analysis, cost optimization, and 
comprehensive reporting capabilities.

Built with context engineering patterns for Claude Code integration.
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import structlog
import time
from contextlib import asynccontextmanager

# API routers
from .api.renewals import router as renewals_router
from .api.notifications import router as notifications_router
# from .api.contracts import router as contracts_router
# from .api.properties import router as properties_router  
# from .api.vendors import router as vendors_router
# from .api.analytics import router as analytics_router
# from .api.optimization import router as optimization_router

# Core services and middleware
from .core.exceptions import AdvantageWasteException, to_http_exception
from .core.settings import settings
from .middleware.auth import (
    rate_limit_middleware,
    security_headers_middleware, 
    audit_logging_middleware,
    request_validation_middleware,
    cors_validation_middleware
)

logger = structlog.get_logger()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    logger.info("Starting Advantage Waste Enterprise API")
    yield
    logger.info("Shutting down Advantage Waste Enterprise API")

# FastAPI application with enterprise configuration
app = FastAPI(
    title="Advantage Waste Enterprise API",
    description="Enterprise waste management solution for Greystar multifamily properties",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan,
    # Custom OpenAPI schema for better documentation
    openapi_tags=[
        {
            "name": "renewals",
            "description": "Contract renewal management and analysis",
        },
        {
            "name": "notifications",
            "description": "Notification preferences and communication",
        },
        {
            "name": "contracts",
            "description": "Contract analysis and comparison operations",
        },
        {
            "name": "properties", 
            "description": "Property management and metrics",
        },
        {
            "name": "vendors",
            "description": "Vendor management and relationships",
        },
        {
            "name": "analytics",
            "description": "Analytics, reporting, and dashboards",
        },
        {
            "name": "optimization",
            "description": "Waste and cost optimization tools",
        },
        {
            "name": "system",
            "description": "System health and information endpoints",
        }
    ]
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Gzip compression for better performance
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Custom security and validation middleware
app.middleware("http")(cors_validation_middleware)
app.middleware("http")(request_validation_middleware)
app.middleware("http")(security_headers_middleware)
app.middleware("http")(rate_limit_middleware)
app.middleware("http")(audit_logging_middleware)

# Global exception handlers for enterprise error handling
@app.exception_handler(AdvantageWasteException)
async def advantage_waste_exception_handler(request: Request, exc: AdvantageWasteException):
    """Handle custom AdvantageWasteException errors"""
    http_exc = to_http_exception(exc)
    logger.warning(
        "Business logic exception",
        error_code=exc.error_code,
        error_message=exc.message,
        path=request.url.path,
        method=request.method,
        details=exc.details
    )
    return JSONResponse(
        status_code=http_exc.status_code,
        content=http_exc.detail
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors"""
    logger.error(
        "Unhandled exception",
        error=str(exc),
        error_type=type(exc).__name__,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred. Please contact support.",
            "request_id": str(int(time.time()))  # Simple request ID
        }
    )

# Health check endpoints
@app.get("/health", tags=["system"])
async def health_check():
    """Health check endpoint for load balancers and monitoring"""
    return {
        "status": "healthy",
        "service": "advantage-waste-enterprise-api",
        "version": "1.0.0",
        "timestamp": time.time()
    }

@app.get("/", tags=["system"])
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Advantage Waste Enterprise API",
        "description": "Enterprise waste management solution for Greystar multifamily properties", 
        "version": "1.0.0",
        "docs_url": "/docs",
        "redoc_url": "/redoc",
        "capabilities": [
            "Contract analysis and comparison",
            "Property waste metrics and optimization",
            "Vendor management and evaluation", 
            "Cost optimization and savings identification",
            "Industry benchmark analysis",
            "Executive reporting and analytics"
        ]
    }

# Include API routers
app.include_router(renewals_router, prefix="/api/renewals", tags=["renewals"])
app.include_router(notifications_router, prefix="/api/notifications", tags=["notifications"])
# app.include_router(contracts_router, prefix="/api/contracts", tags=["contracts"])
# app.include_router(properties_router, prefix="/api/properties", tags=["properties"])
# app.include_router(vendors_router, prefix="/api/vendors", tags=["vendors"])
# app.include_router(analytics_router, prefix="/api/analytics", tags=["analytics"])  
# app.include_router(optimization_router, prefix="/api/optimization", tags=["optimization"])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
