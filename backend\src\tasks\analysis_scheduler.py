"""
Contract analysis scheduler tasks for Advantage Waste Enterprise.
Automated scheduling and execution of contract renewal analysis.
"""

from celery import current_task
from datetime import date, datetime, timedelta
from typing import List, Dict, Any, Optional
import structlog
from sqlalchemy import and_, or_
from sqlalchemy.orm import joinedload
from decimal import Decimal

from ..core.celery_app import celery_app
from ..core.database import get_sync_db
from ..models.contracts import Contract, ContractRenewal, ContractAnalysis, ContractStatus, RenewalStatus
from ..models.vendors import Vendor, VendorPerformance
from ..models.properties import Property, PropertyMetrics
from ..models.tasks import TaskExecution, TaskStatus

logger = structlog.get_logger()


@celery_app.task(bind=True, name="tasks.analysis_scheduler.schedule_pending_analyses")
def schedule_pending_analyses(self, priority_threshold_days: int = 60):
    """
    Daily task to schedule contract analyses for pending renewals.
    
    Args:
        priority_threshold_days: Days until expiry to prioritize analysis
        
    Returns:
        Dict containing scheduling results
    """
    task_id = self.request.id
    start_time = datetime.utcnow()
    
    logger.info(
        "Starting analysis scheduling",
        task_id=task_id,
        priority_threshold_days=priority_threshold_days
    )
    
    try:
        results = _schedule_analyses(priority_threshold_days)
        
        logger.info(
            "Analysis scheduling completed",
            task_id=task_id,
            results=results
        )
        
        return results
        
    except Exception as e:
        error_msg = f"Analysis scheduling failed: {str(e)}"
        logger.error("Analysis scheduling failed", task_id=task_id, error=str(e))
        raise


def _schedule_analyses(priority_threshold_days: int) -> Dict[str, Any]:
    """
    Schedule contract analyses for pending renewals.
    
    Args:
        priority_threshold_days: Days threshold for priority scheduling
        
    Returns:
        Dict with scheduling results
    """
    with next(get_sync_db()) as db:
        # Find renewals needing analysis
        renewals_query = db.query(ContractRenewal).options(
            joinedload(ContractRenewal.contract).joinedload(Contract.vendor),
            joinedload(ContractRenewal.contract).joinedload(Contract.analyses)
        ).filter(
            and_(
                ContractRenewal.status == RenewalStatus.PENDING,
                ContractRenewal.renewal_date >= date.today()
            )
        )
        
        renewals = renewals_query.all()
        
        results = {
            "renewals_checked": len(renewals),
            "analyses_scheduled": 0,
            "high_priority_scheduled": 0,
            "skipped_existing": 0,
            "errors": []
        }
        
        for renewal in renewals:
            try:
                # Check if analysis already exists for this renewal
                existing_analysis = next(
                    (a for a in renewal.contract.analyses 
                     if a.renewal_id == renewal.id and a.analysis_type == "renewal"),
                    None
                )
                
                if existing_analysis:
                    results["skipped_existing"] += 1
                    continue
                
                # Determine priority based on days until expiry
                days_until_expiry = renewal.contract.days_until_expiration
                is_high_priority = days_until_expiry <= priority_threshold_days
                
                # Schedule analysis task
                task_kwargs = {
                    "renewal_id": renewal.id,
                    "priority": "high" if is_high_priority else "medium"
                }
                
                run_contract_analysis.delay(**task_kwargs)
                
                results["analyses_scheduled"] += 1
                if is_high_priority:
                    results["high_priority_scheduled"] += 1
                
                logger.info(
                    "Scheduled contract analysis",
                    renewal_id=renewal.id,
                    contract_number=renewal.contract.contract_number,
                    priority="high" if is_high_priority else "medium",
                    days_until_expiry=days_until_expiry
                )
                
            except Exception as e:
                error_msg = f"Failed to schedule analysis for renewal {renewal.id}: {str(e)}"
                results["errors"].append(error_msg)
                logger.error(
                    "Analysis scheduling failed",
                    renewal_id=renewal.id,
                    error=str(e)
                )
        
        return results


@celery_app.task(bind=True, name="tasks.analysis_scheduler.run_contract_analysis")
def run_contract_analysis(self, renewal_id: int, priority: str = "medium", force_rerun: bool = False):
    """
    Run comprehensive contract analysis for a renewal.
    
    Args:
        renewal_id: ContractRenewal ID to analyze
        priority: Analysis priority (high, medium, low)
        force_rerun: Whether to rerun existing analysis
        
    Returns:
        Dict with analysis results
    """
    task_id = self.request.id
    start_time = datetime.utcnow()
    
    logger.info(
        "Starting contract analysis",
        task_id=task_id,
        renewal_id=renewal_id,
        priority=priority
    )
    
    try:
        results = _run_analysis(renewal_id, force_rerun)
        
        # Update renewal status to in_progress
        with next(get_sync_db()) as db:
            renewal = db.query(ContractRenewal).get(renewal_id)
            if renewal and renewal.status == RenewalStatus.PENDING:
                renewal.status = RenewalStatus.IN_PROGRESS
                db.commit()
        
        # Schedule notification if analysis is complete
        if results.get("recommendation"):
            from .email_notifications import send_analysis_complete_notification
            send_analysis_complete_notification.delay(renewal_id)
        
        logger.info(
            "Contract analysis completed",
            task_id=task_id,
            renewal_id=renewal_id,
            recommendation=results.get("recommendation"),
            confidence=results.get("confidence_level")
        )
        
        return results
        
    except Exception as e:
        error_msg = f"Contract analysis failed: {str(e)}"
        logger.error("Contract analysis failed", task_id=task_id, error=str(e))
        raise


def _run_analysis(renewal_id: int, force_rerun: bool) -> Dict[str, Any]:
    """
    Perform comprehensive contract analysis.
    
    Args:
        renewal_id: Renewal ID to analyze
        force_rerun: Whether to overwrite existing analysis
        
    Returns:
        Dict with analysis results
    """
    with next(get_sync_db()) as db:
        # Get renewal with related data
        renewal = db.query(ContractRenewal).options(
            joinedload(ContractRenewal.contract).joinedload(Contract.vendor),
            joinedload(ContractRenewal.contract).joinedload(Contract.analyses)
        ).get(renewal_id)
        
        if not renewal:
            raise ValueError(f"Renewal {renewal_id} not found")
        
        contract = renewal.contract
        
        # Check for existing analysis
        existing_analysis = next(
            (a for a in contract.analyses 
             if a.renewal_id == renewal_id and a.analysis_type == "renewal"),
            None
        )
        
        if existing_analysis and not force_rerun:
            logger.info(
                "Analysis already exists",
                renewal_id=renewal_id,
                analysis_id=existing_analysis.id
            )
            return _convert_analysis_to_dict(existing_analysis)
        
        # Gather analysis data
        analysis_data = _gather_analysis_data(db, contract)
        
        # Perform cost analysis
        cost_analysis = _analyze_costs(db, contract, analysis_data)
        
        # Perform vendor analysis
        vendor_analysis = _analyze_vendor_performance(db, contract, analysis_data)
        
        # Generate recommendation
        recommendation_data = _generate_recommendation(
            contract, cost_analysis, vendor_analysis, analysis_data
        )
        
        # Create or update analysis record
        if existing_analysis:
            analysis = existing_analysis
            analysis.analysis_date = datetime.utcnow()
        else:
            analysis = ContractAnalysis(
                contract_id=contract.id,
                renewal_id=renewal_id,
                analysis_type="renewal",
                analysis_date=datetime.utcnow()
            )
            db.add(analysis)
        
        # Update analysis with results
        _update_analysis_record(analysis, cost_analysis, vendor_analysis, recommendation_data, analysis_data)
        
        db.commit()
        
        return _convert_analysis_to_dict(analysis)


def _gather_analysis_data(db, contract: Contract) -> Dict[str, Any]:
    """
    Gather comprehensive data for contract analysis.
    
    Args:
        db: Database session
        contract: Contract to analyze
        
    Returns:
        Dict with gathered analysis data
    """
    # Get property information
    property_info = db.query(Property).filter(
        Property.greystar_property_id == contract.greystar_property_id
    ).first()
    
    # Get recent property metrics (last 12 months)
    metrics_query = db.query(PropertyMetrics).filter(
        and_(
            PropertyMetrics.greystar_property_id == contract.greystar_property_id,
            PropertyMetrics.metric_date >= date.today() - timedelta(days=365)
        )
    ).order_by(PropertyMetrics.metric_date.desc())
    
    property_metrics = metrics_query.limit(12).all()
    
    # Get vendor performance data
    vendor_performance = db.query(VendorPerformance).filter(
        and_(
            VendorPerformance.vendor_id == contract.vendor_id,
            VendorPerformance.performance_date >= date.today() - timedelta(days=365)
        )
    ).order_by(VendorPerformance.performance_date.desc()).limit(12).all()
    
    # Get comparable contracts for benchmarking
    comparable_contracts = db.query(Contract).filter(
        and_(
            Contract.id != contract.id,
            Contract.status == ContractStatus.ACTIVE,
            Contract.container_size == contract.container_size,
            Contract.pickup_frequency == contract.pickup_frequency
        )
    ).limit(20).all()
    
    return {
        "property": _serialize_property(property_info) if property_info else None,
        "property_metrics": [_serialize_property_metrics(m) for m in property_metrics],
        "vendor_performance": [_serialize_vendor_performance(v) for v in vendor_performance],
        "comparable_contracts": [_serialize_contract_summary(c) for c in comparable_contracts],
        "analysis_date": date.today().isoformat(),
        "contract_age_days": (date.today() - contract.start_date).days,
        "days_until_expiry": contract.days_until_expiration
    }


def _analyze_costs(db, contract: Contract, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze contract costs and identify optimization opportunities.
    
    Args:
        db: Database session
        contract: Contract to analyze
        analysis_data: Gathered analysis data
        
    Returns:
        Dict with cost analysis results
    """
    # Calculate current metrics
    current_cost_per_door = float(contract.cost_per_door or 0)
    current_cost_per_yard = contract.cost_per_yard
    monthly_volume = contract.monthly_volume
    
    # Calculate benchmarks from comparable contracts
    comparable_contracts = analysis_data.get("comparable_contracts", [])
    if comparable_contracts:
        comparable_costs_per_door = [c["cost_per_door"] for c in comparable_contracts if c["cost_per_door"]]
        comparable_costs_per_yard = [c["cost_per_yard"] for c in comparable_contracts if c["cost_per_yard"]]
        
        benchmark_cost_per_door = sum(comparable_costs_per_door) / len(comparable_costs_per_door) if comparable_costs_per_door else current_cost_per_door
        benchmark_cost_per_yard = sum(comparable_costs_per_yard) / len(comparable_costs_per_yard) if comparable_costs_per_yard else current_cost_per_yard
    else:
        # Use property type targets as fallback
        property_info = analysis_data.get("property")
        if property_info:
            benchmark_cost_per_door = property_info.get("target_cost_per_door", current_cost_per_door)
        else:
            benchmark_cost_per_door = 18.0  # Industry average
        benchmark_cost_per_yard = 85.0  # Industry average
    
    # Calculate variances
    cost_variance_percent = ((current_cost_per_door - benchmark_cost_per_door) / benchmark_cost_per_door) * 100 if benchmark_cost_per_door > 0 else 0
    
    # Calculate potential savings
    if current_cost_per_door > benchmark_cost_per_door:
        monthly_savings = (current_cost_per_door - benchmark_cost_per_door) * (analysis_data.get("property", {}).get("unit_count", 200))
        annual_savings = monthly_savings * 12
    else:
        monthly_savings = 0
        annual_savings = 0
    
    # Analyze cost trends from property metrics
    property_metrics = analysis_data.get("property_metrics", [])
    cost_trend = "stable"
    if len(property_metrics) >= 3:
        recent_costs = [m["cost_per_door"] for m in property_metrics[:3] if m.get("cost_per_door")]
        older_costs = [m["cost_per_door"] for m in property_metrics[-3:] if m.get("cost_per_door")]
        
        if recent_costs and older_costs:
            recent_avg = sum(recent_costs) / len(recent_costs)
            older_avg = sum(older_costs) / len(older_costs)
            change_percent = ((recent_avg - older_avg) / older_avg) * 100 if older_avg > 0 else 0
            
            if change_percent > 5:
                cost_trend = "increasing"
            elif change_percent < -5:
                cost_trend = "decreasing"
    
    return {
        "current_cost_per_door": current_cost_per_door,
        "benchmark_cost_per_door": benchmark_cost_per_door,
        "cost_variance_percent": round(cost_variance_percent, 2),
        "current_cost_per_yard": current_cost_per_yard,
        "benchmark_cost_per_yard": benchmark_cost_per_yard,
        "monthly_volume": monthly_volume,
        "potential_monthly_savings": round(monthly_savings, 2),
        "potential_annual_savings": round(annual_savings, 2),
        "cost_trend": cost_trend,
        "cost_competitiveness": "above_market" if cost_variance_percent > 10 else "competitive" if abs(cost_variance_percent) <= 10 else "below_market"
    }


def _analyze_vendor_performance(db, contract: Contract, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze vendor performance metrics.
    
    Args:
        db: Database session
        contract: Contract to analyze
        analysis_data: Gathered analysis data
        
    Returns:
        Dict with vendor performance analysis
    """
    vendor_performance = analysis_data.get("vendor_performance", [])
    
    if not vendor_performance:
        return {
            "service_quality_score": 5.0,
            "reliability_score": 5.0,
            "communication_score": 5.0,
            "overall_score": 5.0,
            "performance_trend": "unknown",
            "incident_count": 0,
            "resolution_time": 24.0
        }
    
    # Calculate average scores from recent performance data
    recent_performance = vendor_performance[:6]  # Last 6 months
    
    service_scores = [p["service_score"] for p in recent_performance if p.get("service_score")]
    reliability_scores = [p["reliability_score"] for p in recent_performance if p.get("reliability_score")]
    communication_scores = [p["communication_score"] for p in recent_performance if p.get("communication_score")]
    overall_scores = [p["overall_score"] for p in recent_performance if p.get("overall_score")]
    
    avg_service = sum(service_scores) / len(service_scores) if service_scores else 5.0
    avg_reliability = sum(reliability_scores) / len(reliability_scores) if reliability_scores else 5.0
    avg_communication = sum(communication_scores) / len(communication_scores) if communication_scores else 5.0
    avg_overall = sum(overall_scores) / len(overall_scores) if overall_scores else 5.0
    
    # Calculate performance trend
    performance_trend = "stable"
    if len(overall_scores) >= 3:
        recent_avg = sum(overall_scores[:3]) / len(overall_scores[:3])
        older_scores = [p["overall_score"] for p in vendor_performance[-3:] if p.get("overall_score")]
        if older_scores:
            older_avg = sum(older_scores) / len(older_scores)
            change = recent_avg - older_avg
            
            if change > 0.5:
                performance_trend = "improving"
            elif change < -0.5:
                performance_trend = "declining"
    
    # Sum incidents and calculate average resolution time
    total_incidents = sum(p.get("customer_complaints", 0) + p.get("environmental_incidents", 0) for p in recent_performance)
    resolution_times = [p.get("average_resolution_time", 24) for p in recent_performance if p.get("average_resolution_time")]
    avg_resolution_time = sum(resolution_times) / len(resolution_times) if resolution_times else 24.0
    
    return {
        "service_quality_score": round(avg_service, 1),
        "reliability_score": round(avg_reliability, 1),
        "communication_score": round(avg_communication, 1),
        "overall_score": round(avg_overall, 1),
        "performance_trend": performance_trend,
        "incident_count": total_incidents,
        "resolution_time": round(avg_resolution_time, 1)
    }


def _generate_recommendation(contract: Contract, cost_analysis: Dict[str, Any], 
                           vendor_analysis: Dict[str, Any], analysis_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate renewal recommendation based on analysis results.
    
    Args:
        contract: Contract being analyzed
        cost_analysis: Cost analysis results
        vendor_analysis: Vendor performance analysis
        analysis_data: General analysis data
        
    Returns:
        Dict with recommendation data
    """
    # Scoring factors
    cost_score = 0
    vendor_score = 0
    risk_score = 0
    
    # Cost analysis scoring
    cost_variance = cost_analysis.get("cost_variance_percent", 0)
    if cost_variance <= -10:  # 10% below market
        cost_score = 10
    elif cost_variance <= 0:  # At or below market
        cost_score = 8
    elif cost_variance <= 10:  # Up to 10% above market
        cost_score = 6
    elif cost_variance <= 20:  # Up to 20% above market
        cost_score = 4
    else:  # More than 20% above market
        cost_score = 2
    
    # Vendor performance scoring
    overall_vendor_score = vendor_analysis.get("overall_score", 5.0)
    if overall_vendor_score >= 8.0:
        vendor_score = 10
    elif overall_vendor_score >= 7.0:
        vendor_score = 8
    elif overall_vendor_score >= 6.0:
        vendor_score = 6
    elif overall_vendor_score >= 5.0:
        vendor_score = 4
    else:
        vendor_score = 2
    
    # Risk assessment
    risk_factors = []
    
    if cost_analysis.get("cost_trend") == "increasing":
        risk_score += 2
        risk_factors.append("Cost trend increasing")
    
    if vendor_analysis.get("performance_trend") == "declining":
        risk_score += 3
        risk_factors.append("Vendor performance declining")
    
    if vendor_analysis.get("incident_count", 0) > 5:
        risk_score += 2
        risk_factors.append("High incident count")
    
    if analysis_data.get("days_until_expiry", 90) < 30:
        risk_score += 1
        risk_factors.append("Limited time for alternative sourcing")
    
    # Calculate overall score
    overall_score = (cost_score + vendor_score) - risk_score
    confidence_level = min(10, max(1, (overall_score + 10) / 2))  # Convert to 1-10 scale
    
    # Generate recommendation
    if overall_score >= 12:
        recommendation = "renew"
        reason = "Strong cost performance and vendor reliability support renewal"
    elif overall_score >= 8:
        recommendation = "renew"
        reason = "Acceptable performance justifies renewal with monitoring"
    elif overall_score >= 4:
        recommendation = "renegotiate"
        reason = "Performance issues warrant renegotiation of terms"
    else:
        recommendation = "terminate"
        reason = "Poor performance and high costs indicate need for new vendor"
    
    # Calculate potential savings
    potential_savings = cost_analysis.get("potential_annual_savings", 0)
    if recommendation == "terminate" and potential_savings == 0:
        # Estimate savings from market rates
        potential_savings = max(0, cost_analysis.get("current_cost_per_door", 0) - cost_analysis.get("benchmark_cost_per_door", 0)) * 12 * analysis_data.get("property", {}).get("unit_count", 200)
    
    return {
        "recommendation": recommendation,
        "confidence_level": round(confidence_level, 1),
        "overall_score": overall_score,
        "cost_score": cost_score,
        "vendor_score": vendor_score,
        "risk_score": risk_score,
        "potential_annual_savings": round(potential_savings, 2),
        "recommendation_reason": reason,
        "risk_factors": risk_factors,
        "key_findings": _generate_key_findings(cost_analysis, vendor_analysis),
        "action_items": _generate_action_items(recommendation, cost_analysis, vendor_analysis)
    }


def _generate_key_findings(cost_analysis: Dict[str, Any], vendor_analysis: Dict[str, Any]) -> List[str]:
    """Generate key findings from analysis."""
    findings = []
    
    # Cost findings
    cost_variance = cost_analysis.get("cost_variance_percent", 0)
    if abs(cost_variance) > 15:
        findings.append(f"Cost is {abs(cost_variance):.1f}% {'above' if cost_variance > 0 else 'below'} market benchmark")
    
    if cost_analysis.get("potential_annual_savings", 0) > 5000:
        findings.append(f"Potential annual savings of ${cost_analysis.get('potential_annual_savings'):,.0f} identified")
    
    # Vendor findings
    overall_score = vendor_analysis.get("overall_score", 5.0)
    if overall_score >= 8.0:
        findings.append("Vendor demonstrates excellent performance across all metrics")
    elif overall_score <= 4.0:
        findings.append("Vendor performance below acceptable standards")
    
    performance_trend = vendor_analysis.get("performance_trend", "stable")
    if performance_trend == "declining":
        findings.append("Vendor performance shows declining trend")
    elif performance_trend == "improving":
        findings.append("Vendor performance shows improving trend")
    
    return findings


def _generate_action_items(recommendation: str, cost_analysis: Dict[str, Any], vendor_analysis: Dict[str, Any]) -> List[str]:
    """Generate action items based on recommendation."""
    action_items = []
    
    if recommendation == "renew":
        action_items.extend([
            "Execute contract renewal with current vendor",
            "Monitor cost performance quarterly",
            "Schedule annual vendor performance review"
        ])
    elif recommendation == "renegotiate":
        action_items.extend([
            "Initiate renegotiation discussions with current vendor",
            "Request proposals from alternative vendors",
            "Focus negotiations on cost reduction and service improvements"
        ])
    elif recommendation == "terminate":
        action_items.extend([
            "Issue RFP to qualified vendors",
            "Provide proper termination notice to current vendor",
            "Plan transition timeline to minimize service disruption"
        ])
    
    # Add specific action items based on analysis
    if cost_analysis.get("cost_variance_percent", 0) > 15:
        action_items.append("Request detailed cost breakdown and justification")
    
    if vendor_analysis.get("incident_count", 0) > 5:
        action_items.append("Develop vendor improvement plan with performance metrics")
    
    return action_items


def _update_analysis_record(analysis: ContractAnalysis, cost_analysis: Dict[str, Any], 
                          vendor_analysis: Dict[str, Any], recommendation_data: Dict[str, Any], 
                          analysis_data: Dict[str, Any]):
    """Update analysis record with results."""
    
    # Update individual metrics
    analysis.current_cost_per_door = Decimal(str(cost_analysis.get("current_cost_per_door", 0)))
    analysis.benchmark_cost_per_door = Decimal(str(cost_analysis.get("benchmark_cost_per_door", 0)))
    analysis.cost_variance_percent = Decimal(str(cost_analysis.get("cost_variance_percent", 0)))
    
    analysis.current_cost_per_yard = Decimal(str(cost_analysis.get("current_cost_per_yard", 0)))
    analysis.benchmark_cost_per_yard = Decimal(str(cost_analysis.get("benchmark_cost_per_yard", 0)))
    
    analysis.service_quality_score = Decimal(str(vendor_analysis.get("service_quality_score", 5.0)))
    analysis.reliability_score = Decimal(str(vendor_analysis.get("reliability_score", 5.0)))
    analysis.communication_score = Decimal(str(vendor_analysis.get("communication_score", 5.0)))
    analysis.overall_vendor_score = Decimal(str(vendor_analysis.get("overall_score", 5.0)))
    
    analysis.recommendation = recommendation_data.get("recommendation", "renegotiate")
    analysis.confidence_level = Decimal(str(recommendation_data.get("confidence_level", 5.0)))
    analysis.potential_annual_savings = Decimal(str(recommendation_data.get("potential_annual_savings", 0)))
    
    # Store detailed analysis data
    analysis.analysis_data = {
        "cost_analysis": cost_analysis,
        "vendor_analysis": vendor_analysis,
        "recommendation_data": recommendation_data,
        "analysis_metadata": analysis_data
    }
    
    analysis.key_findings = recommendation_data.get("key_findings", [])
    analysis.action_items = recommendation_data.get("action_items", [])
    
    # Generate executive summary
    analysis.executive_summary = _generate_executive_summary(
        analysis, cost_analysis, vendor_analysis, recommendation_data
    )


def _generate_executive_summary(analysis: ContractAnalysis, cost_analysis: Dict[str, Any], 
                              vendor_analysis: Dict[str, Any], recommendation_data: Dict[str, Any]) -> str:
    """Generate executive summary of analysis."""
    
    contract = analysis.contract
    recommendation = recommendation_data.get("recommendation", "renegotiate")
    confidence = recommendation_data.get("confidence_level", 5.0)
    
    summary_parts = [
        f"Contract Analysis Summary for {contract.contract_number}",
        f"Vendor: {contract.vendor.vendor_name if contract.vendor else 'Unknown'}",
        f"Monthly Cost: ${contract.monthly_cost:,.2f} ({contract.cost_per_door or 0:.2f}/door)",
        f"Contract Expires: {contract.end_date.strftime('%B %d, %Y')} ({contract.days_until_expiration} days)",
        "",
        f"RECOMMENDATION: {recommendation.upper()} (Confidence: {confidence:.1f}/10)",
        "",
        "Key Metrics:",
        f"• Cost Performance: {cost_analysis.get('cost_competitiveness', 'unknown').replace('_', ' ').title()}",
        f"• Vendor Score: {vendor_analysis.get('overall_score', 5.0):.1f}/10",
        f"• Potential Annual Savings: ${recommendation_data.get('potential_annual_savings', 0):,.0f}",
        "",
        f"Rationale: {recommendation_data.get('recommendation_reason', 'Analysis completed')}"
    ]
    
    return "\n".join(summary_parts)


# Utility functions for serialization
def _serialize_property(property_obj: Property) -> Dict[str, Any]:
    """Serialize property object for analysis."""
    return {
        "greystar_property_id": property_obj.greystar_property_id,
        "property_name": property_obj.property_name,
        "unit_count": property_obj.unit_count,
        "property_type": property_obj.property_type,
        "city": property_obj.city,
        "state": property_obj.state,
        "target_cost_per_door": property_obj.target_cost_per_door
    }


def _serialize_property_metrics(metrics: PropertyMetrics) -> Dict[str, Any]:
    """Serialize property metrics for analysis."""
    return {
        "metric_date": metrics.metric_date.isoformat(),
        "cost_per_door": float(metrics.cost_per_door or 0),
        "volume_per_door": float(metrics.volume_per_door or 0),
        "cost_per_yard": float(metrics.cost_per_yard or 0),
        "service_performance_score": float(metrics.service_performance_score or 5.0)
    }


def _serialize_vendor_performance(performance: VendorPerformance) -> Dict[str, Any]:
    """Serialize vendor performance for analysis."""
    return {
        "performance_date": performance.performance_date.isoformat(),
        "service_score": float(performance.service_score or 5.0),
        "reliability_score": float(performance.reliability_score or 5.0),
        "communication_score": float(performance.communication_score or 5.0),
        "overall_score": float(performance.overall_score or 5.0),
        "customer_complaints": performance.customer_complaints or 0,
        "environmental_incidents": performance.environmental_incidents or 0,
        "average_resolution_time": float(performance.average_resolution_time or 24.0)
    }


def _serialize_contract_summary(contract: Contract) -> Dict[str, Any]:
    """Serialize contract summary for comparison."""
    return {
        "contract_number": contract.contract_number,
        "monthly_cost": float(contract.monthly_cost),
        "cost_per_door": float(contract.cost_per_door or 0),
        "cost_per_yard": contract.cost_per_yard,
        "container_size": float(contract.container_size),
        "pickup_frequency": contract.pickup_frequency,
        "property_type": contract.greystar_property_id  # Would need property join for actual type
    }


def _convert_analysis_to_dict(analysis: ContractAnalysis) -> Dict[str, Any]:
    """Convert analysis object to dictionary for task result."""
    return {
        "analysis_id": analysis.id,
        "contract_id": analysis.contract_id,
        "renewal_id": analysis.renewal_id,
        "recommendation": analysis.recommendation,
        "confidence_level": float(analysis.confidence_level or 0),
        "potential_annual_savings": float(analysis.potential_annual_savings or 0),
        "key_findings": analysis.key_findings or [],
        "action_items": analysis.action_items or [],
        "executive_summary": analysis.executive_summary,
        "analysis_date": analysis.analysis_date.isoformat() if analysis.analysis_date else None
    }