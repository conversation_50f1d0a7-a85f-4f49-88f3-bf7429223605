# Generate PRP (Product Requirements Prompt)

Research the codebase, analyze requirements, and generate a comprehensive PRP for implementing a new feature in the Advantage Waste Enterprise application.

## Arguments: $ARGUMENTS

The first argument should be a path to an INITIAL.md file or a feature description.

## Research Phase

1. **Read the feature request** from $ARGUMENTS
2. **Analyze the codebase** for existing patterns and implementations
3. **Search examples/** directory for relevant patterns to follow
4. **Review CLAUDE.md** for project rules and waste management context
5. **Research external documentation** mentioned in the request
6. **Identify integration points** with existing Advantage Waste systems

## Documentation Gathering

1. **Waste Management Standards**: Reference industry formulas, benchmarks, and calculations from CLAUDE.md
2. **API Documentation**: Gather relevant FastAPI, React, and database documentation
3. **Greystar Integration**: Review Advantage Waste portal integration requirements
4. **Security Requirements**: Enterprise security and compliance considerations
5. **Testing Patterns**: Identify test patterns from existing examples

## PRP Creation Process

1. **Goal Definition**: Clear, specific goal with business value
2. **Context Assembly**: All documentation, examples, and gotchas needed
3. **Implementation Blueprint**: Step-by-step technical implementation plan
4. **Validation Gates**: Executable tests and validation criteria
5. **Success Criteria**: Measurable outcomes and acceptance criteria

## PRP Template Structure

Use this structure for all PRPs:

```markdown
---
name: "Feature Name - Advantage Waste Enterprise"
description: |
  Brief description of what this PRP implements and why it's needed
  for Greystar's waste management operations.
---

# Goal
[Clear, specific statement of what needs to be built]

# Why  
[Business value and impact on Greystar's operations]

# What
[Detailed feature specification with user workflows]

## Success Criteria
- [ ] Measurable, testable outcomes
- [ ] Integration requirements met
- [ ] Performance benchmarks achieved

# All Needed Context

## Waste Management Domain Knowledge
[Reference specific industry knowledge from CLAUDE.md]

## Documentation & References  
- url: [relevant documentation URLs]
  why: [explanation of relevance]
- file: [local file references with paths]
  why: [what patterns/context they provide]

## Code Patterns to Follow
[Specific examples from examples/ directory to use as reference]

## Known Gotchas
# CRITICAL: [Important warnings and considerations]
# IMPORTANT: [Secondary considerations]
# NOTE: [Helpful reminders]

# Implementation Blueprint

## Phase 1: Foundation
[Initial setup and core structure]

## Phase 2: Core Logic  
[Business logic implementation]

## Phase 3: Integration
[API endpoints, database, frontend integration]

## Phase 4: Testing & Validation
[Comprehensive testing approach]

# Validation Loop

## Level 1: Syntax & Style
[Linting and code quality checks]

## Level 2: Unit Tests
[Specific unit test requirements]

## Level 3: Integration Tests
[API and database integration tests]

## Level 4: Business Logic Validation
[Waste management calculation validation]

## Level 5: User Acceptance
[End-to-end workflow testing]

# Technical Implementation Details

## Backend Changes
[FastAPI endpoints, models, services needed]

## Frontend Changes
[React components, pages, hooks needed]

## Database Changes
[Schema changes, migrations needed]

## External Integrations
[Third-party APIs, Greystar systems]

# Security & Compliance

[Data protection, access control, audit requirements]

# Performance Requirements

[Response time targets, scalability considerations]

# Deployment Considerations

[Docker, environment configs, CI/CD requirements]
```

## Quality Assurance

Before finalizing the PRP:

1. **Completeness Check**: Ensure all context needed for implementation is included
2. **Accuracy Validation**: Verify waste management calculations and industry standards
3. **Integration Review**: Confirm compatibility with existing Advantage Waste systems
4. **Security Review**: Address enterprise security and compliance requirements
5. **Testability**: Ensure all requirements can be validated with automated tests

## Confidence Scoring

Rate the PRP confidence level (1-10) based on:
- Completeness of context provided
- Clarity of implementation steps
- Availability of reference patterns
- Complexity vs. available examples
- Integration requirements clarity

Target score: 8+ for one-pass implementation success

## Output Location

Save the completed PRP to:
- `PRPs/[feature-name].md` for new features
- `PRPs/active/[feature-name].md` for active development
- Include timestamp and version in metadata

## Waste Management Context

Always consider these domain-specific factors:

### Industry Standards
- Cost per door benchmarks by property type
- Volume calculations (container size × quantity × frequency × 4.33)
- Contract term best practices (12-24 months)
- Price increase caps (CPI or 4% maximum)

### Greystar Requirements
- 3,850+ property scale operations
- Advantage Waste portal integration
- Financial audit and compliance needs
- Property manager workflow optimization

### Data Accuracy
- Use Decimal for financial calculations
- Validate container sizes against industry standards
- Ensure pickup frequency constraints (1-7 times/week)
- Property unit counts must be positive integers

## Example Usage

```
/generate-prp examples/contract-comparison-feature.md
```

This will research the request, analyze patterns, and create a comprehensive PRP ready for implementation with `/execute-prp`.

Remember: The goal is one-pass implementation success through comprehensive context engineering.
