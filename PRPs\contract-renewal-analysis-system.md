---
name: "Contract Renewal Analysis System - Advantage Waste Enterprise"
description: |
  Automated contract renewal analysis system that monitors contract expiration dates 
  across all Greystar properties, evaluates current contract performance, identifies 
  renewal opportunities, and generates negotiation recommendations 60-90 days before 
  expiration. Enables proactive contract management at enterprise scale.
---

# Goal
Build an automated contract renewal analysis system that proactively monitors 3,850+ Greystar properties for contract expirations, analyzes performance against industry benchmarks, and delivers actionable renewal recommendations to property managers and regional directors 60-90 days before contract expiration.

# Why  
**Business Impact for Greystar Operations:**
- **Prevents contract auto-renewals** at unfavorable terms across thousands of properties
- **Identifies cost savings opportunities** through systematic benchmark analysis before renewal
- **Standardizes negotiation approach** using Advantage Waste methodology across all regions
- **Reduces missed renewal deadlines** that result in expensive short-term contracts
- **Enables enterprise-wide contract optimization** with consistent data-driven decisions
- **Provides audit trail** for procurement compliance and cost justification

**Enterprise Value:**
- Estimated annual savings: $2-5M across portfolio (5-15% improvement on $50M+ annual spend)
- Reduced administrative overhead: 75% less manual contract tracking
- Improved negotiation outcomes: Standardized talking points and benchmark data
- Risk mitigation: Automated alerts prevent costly auto-renewals

# What
**Core Functionality:**
1. **Automated Monitoring Dashboard**
   - Real-time tracking of all active contracts with expiration alerts
   - 90-day, 60-day, and 30-day renewal notification system
   - Executive dashboard showing portfolio-wide renewal pipeline

2. **Performance Analysis Engine**
   - Automated contract performance evaluation using ContractAnalyzer patterns
   - Industry benchmark comparison for cost per door, service levels, and terms
   - Historical performance trending and vendor relationship scoring

3. **Renewal Recommendation Generator**
   - Data-driven negotiation talking points based on market analysis
   - Alternative vendor comparison with cost impact modeling
   - Renewal vs. rebid decision matrix with financial projections

4. **Automated Workflow Management**
   - Email notifications to property managers, regional directors, and procurement
   - Task assignment and tracking through renewal process
   - Integration with Greystar contract management and approval workflows

## Success Criteria
- [ ] Monitor 3,850+ properties with 99.9% uptime for renewal tracking
- [ ] Generate renewal notifications 90 days before expiration with <24 hour latency
- [ ] Achieve 95% on-time renewal analysis completion (within 60 days of expiration)
- [ ] Deliver measurable cost savings on 80% of renewed contracts
- [ ] Reduce missed renewal deadlines by 90% compared to manual process
- [ ] Integrate with existing Greystar authentication and approval systems
- [ ] Handle 500+ concurrent contract analyses during peak renewal seasons
- [ ] Maintain audit trail for all renewal decisions and cost savings calculations

# All Needed Context

## Waste Management Domain Knowledge
**Industry Standards (from CLAUDE.md):**
- **Cost per door**: $10-30 per unit monthly (varies by property type)
- **Contract length**: 12-24 months optimal for flexibility vs. 36+ month vendor preference
- **Price increases**: Capped at CPI or 4% maximum annually
- **Fuel surcharges**: Avoid or cap at 5% maximum
- **Early termination**: 3-4 months average charges maximum
- **Termination notice**: 90 days maximum, 30-60 days preferred

**Property Type Benchmarks:**
- **Garden-style apartments**: 2.0-2.25 yd³/door, higher costs per door
- **Mid-rise/Mixed-use**: ~1.5 yd³/door, moderate costs  
- **High-rise**: 1.0-1.5 yd³/door, lower costs per door

**Contract Analysis Formulas:**
- **Monthly volume**: Container Size × Quantity × Pickups/Week × 4.33
- **Cost per yard**: Total Monthly Cost ÷ Total Monthly Volume
- **Cost per door**: Total Monthly Cost ÷ Number of Units

## Documentation & References  
- **url**: https://fastapi.tiangolo.com/
  **why**: Background tasks, dependency injection, authentication patterns for renewal workflow
- **file**: examples/contract_analysis_example.py
  **why**: ContractAnalyzer class provides benchmark analysis and grading methodology
- **file**: backend/src/main.py  
  **why**: FastAPI application structure, middleware, and enterprise configuration patterns
- **file**: CLAUDE.md
  **why**: Waste management domain expertise, industry benchmarks, and validation rules

## Code Patterns to Follow
**ContractAnalyzer Class (examples/contract_analysis_example.py):**
- Use existing `ContractTerms` and `PropertyInfo` dataclasses
- Follow `analyze_contract()` method for performance evaluation
- Leverage `INDUSTRY_BENCHMARKS` for renewal recommendation logic
- Apply `_generate_recommendations()` patterns for negotiation talking points

**FastAPI Application Structure (backend/src/main.py):**
- Follow enterprise middleware patterns for logging and monitoring
- Use structured logging with contextual information
- Implement global exception handling for renewal workflow errors
- Apply CORS and security middleware for frontend integration

## Known Gotchas
**CRITICAL**: Contract renewal deadlines are legally binding - missed notifications can result in automatic renewals at unfavorable terms, potentially costing hundreds of thousands annually across the portfolio.

**CRITICAL**: Financial calculations must use Decimal for precision - rounding errors in cost savings projections undermine negotiation credibility and audit compliance.

**IMPORTANT**: Email notifications must handle high-volume periods (Q4 renewal season) - rate limiting and queue management prevent delivery failures during peak times.

**IMPORTANT**: Background task scheduling must be reliable across container restarts - use persistent job scheduling with proper failure recovery.

**NOTE**: Integration with Greystar procurement workflows requires approval thresholds - contracts over $50K may need additional executive approval routing.

**NOTE**: Vendor relationship management is critical - automated systems must balance cost optimization with vendor partnership considerations.

# Implementation Blueprint

## Phase 1: Foundation
**Database Schema & Models:**
- Extend ContractTerms model with renewal-specific fields (renewal_status, last_analysis_date, renewal_recommendation)
- Create RenewalAlert model for tracking notification history and responses
- Add RenewalAnalysis model for storing benchmark comparisons and savings projections
- Create NotificationPreference model for user-specific alert configurations

**Core Services Setup:**
- Implement RenewalMonitoringService for contract expiration tracking
- Create ContractAnalysisService extending patterns from examples/contract_analysis_example.py
- Build NotificationService for email delivery and tracking
- Set up TaskScheduler service for automated renewal analysis jobs

## Phase 2: Core Logic  
**Automated Monitoring Engine:**
- Daily background task scanning for contracts approaching expiration
- Configurable alert thresholds (90, 60, 30 days) per user preferences
- Contract performance evaluation using existing ContractAnalyzer patterns
- Historical trending analysis for vendor performance over time

**Analysis & Recommendation Engine:**
- Automated benchmark comparison using INDUSTRY_BENCHMARKS
- Market analysis integration for current pricing trends
- Alternative vendor suggestion based on regional availability and performance
- Financial impact modeling for renewal vs. rebid scenarios

## Phase 3: Integration
**API Endpoints:**
- `/api/renewals/upcoming` - List contracts approaching expiration
- `/api/renewals/{contract_id}/analyze` - Generate renewal analysis and recommendations
- `/api/renewals/{contract_id}/notification-history` - Track communication timeline
- `/api/renewals/dashboard` - Executive dashboard data aggregation
- `/api/renewals/settings` - User notification preferences management

**Frontend Components:**
- RenewalDashboard with filterable property list and expiration timeline
- ContractRenewalAnalysis detail view with benchmark comparisons and recommendations
- NotificationSettings for user preference management
- RenewalWorkflow task tracking and approval routing

**Email Integration:**
- Template-based renewal notifications with contract details and recommendations
- Escalation email routing for missed responses
- Executive summary reports for portfolio-wide renewal pipeline
- Integration with Greystar email systems and compliance requirements

## Phase 4: Testing & Validation
**Unit Testing:**
- RenewalMonitoringService logic for expiration detection accuracy
- ContractAnalysisService benchmark calculations using known test cases
- NotificationService email formatting and delivery tracking
- TaskScheduler reliability and failure recovery mechanisms

**Integration Testing:**
- End-to-end renewal workflow from detection to recommendation delivery
- Database transaction integrity for concurrent renewal processing
- Email delivery under high-volume conditions
- Frontend dashboard data accuracy and real-time updates

**Performance Testing:**
- 3,850+ property portfolio monitoring with realistic contract distribution
- Peak renewal season load testing (Q4 with 500+ concurrent analyses)
- Database query optimization for large-scale expiration scanning
- Email queue processing during high-notification periods

# Validation Loop

## Level 1: Syntax & Style
- **Backend**: `ruff check backend/src/` for code quality
- **Backend**: `mypy backend/src/` for type safety
- **Frontend**: `npm run lint` for TypeScript/React standards
- **Frontend**: `npm run type-check` for TypeScript validation

## Level 2: Unit Tests
- **Contract Analysis**: Validate renewal recommendations against known benchmark scenarios
- **Date Calculations**: Test expiration detection accuracy across time zones and contract terms
- **Email Templates**: Verify notification content formatting and recipient targeting
- **Financial Calculations**: Confirm cost savings projections using Decimal precision

## Level 3: Integration Tests
- **API Endpoints**: Test renewal workflow API with realistic contract data
- **Database Operations**: Validate contract updates and renewal history tracking
- **Email Delivery**: Test notification system with mock SMTP integration
- **Background Tasks**: Verify scheduled job execution and failure recovery

## Level 4: Business Logic Validation
- **Industry Benchmarks**: Confirm cost per door calculations match CLAUDE.md formulas
- **Contract Grading**: Validate renewal recommendations align with ContractAnalyzer patterns
- **Savings Projections**: Test financial impact modeling against historical renewal outcomes
- **Vendor Comparisons**: Verify alternative vendor suggestions follow Advantage Waste methodology

## Level 5: User Acceptance
- **Property Manager Workflow**: Test renewal notification to decision completion flow
- **Executive Dashboard**: Validate portfolio-wide renewal pipeline accuracy and filtering
- **Notification Preferences**: Confirm user settings properly control alert delivery
- **Audit Trail**: Verify all renewal decisions and cost savings are properly tracked

# Technical Implementation Details

## Backend Changes
**New API Modules:**
- `backend/src/api/renewals.py` - Renewal management endpoints
- `backend/src/api/notifications.py` - Notification preference management
- `backend/src/services/renewal_monitoring.py` - Contract expiration tracking service
- `backend/src/services/contract_analysis.py` - Extended analysis service with renewal focus
- `backend/src/services/notification.py` - Email delivery and tracking service
- `backend/src/tasks/renewal_scanner.py` - Background task for contract monitoring

**Database Models:**
- `backend/src/models/renewal.py` - RenewalAlert, RenewalAnalysis, NotificationPreference models
- `backend/src/models/contract.py` - Extended ContractTerms with renewal tracking fields
- `backend/src/migrations/` - Database schema updates for renewal functionality

**Scheduled Tasks:**
- Daily renewal scanning job with configurable execution time
- Weekly vendor performance analysis for renewal recommendations
- Monthly executive summary report generation

## Frontend Changes
**New Components:**
- `frontend/src/components/renewals/RenewalDashboard.tsx` - Main renewal monitoring interface
- `frontend/src/components/renewals/ContractRenewalAnalysis.tsx` - Detailed renewal analysis view
- `frontend/src/components/renewals/RenewalNotifications.tsx` - Notification history and settings
- `frontend/src/components/renewals/RenewalWorkflow.tsx` - Task tracking and approval flow

**New Pages:**
- `frontend/src/pages/Renewals.tsx` - Renewal management dashboard
- `frontend/src/pages/RenewalDetail.tsx` - Individual contract renewal analysis
- `frontend/src/pages/RenewalSettings.tsx` - User notification preferences

**State Management:**
- React Query integration for renewal data caching and real-time updates
- Zustand stores for notification preferences and workflow state
- Real-time updates via WebSocket for critical renewal alerts

## Database Changes
**New Tables:**
```sql
-- Renewal tracking and analysis storage
CREATE TABLE renewal_alerts (
    id UUID PRIMARY KEY,
    contract_id UUID REFERENCES contracts(id),
    alert_type VARCHAR(20), -- '90_day', '60_day', '30_day'
    alert_date DATE,
    notification_sent BOOLEAN DEFAULT FALSE,
    response_received BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE renewal_analyses (
    id UUID PRIMARY KEY,
    contract_id UUID REFERENCES contracts(id),
    analysis_date DATE,
    current_performance_grade VARCHAR(5),
    benchmark_comparison JSONB,
    savings_opportunity DECIMAL(10,2),
    recommendation TEXT,
    alternative_vendors JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE notification_preferences (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    renewal_90_day BOOLEAN DEFAULT TRUE,
    renewal_60_day BOOLEAN DEFAULT TRUE,
    renewal_30_day BOOLEAN DEFAULT TRUE,
    executive_summary BOOLEAN DEFAULT FALSE,
    email_address VARCHAR(255),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

**Indexes for Performance:**
```sql
CREATE INDEX idx_renewal_alerts_contract_alert_date ON renewal_alerts(contract_id, alert_date);
CREATE INDEX idx_contracts_expiration_date ON contracts(contract_end_date);
CREATE INDEX idx_renewal_analyses_contract_date ON renewal_analyses(contract_id, analysis_date);
```

## External Integrations
**Greystar Systems:**
- Integration with existing Greystar property management database for property and unit data
- SSO integration with Advantage Waste portal authentication
- Procurement system integration for contracts over $50K threshold
- Email integration with Greystar Exchange/Office 365 for notification delivery

**Vendor APIs:**
- Integration with major waste management company APIs for current market pricing
- Alternative vendor directory service for renewal recommendations
- Fuel price indexing service for surcharge analysis

# Security & Compliance

**Data Protection:**
- All financial data encrypted at rest using AES-256
- PII (contact information) encrypted with separate keys
- Audit logging for all renewal decision changes and financial calculations
- Role-based access control for renewal analysis and approval workflows

**Access Control:**
- Property Manager: View renewals for assigned properties, update preferences
- Regional Director: View all renewals in region, approve recommendations
- Procurement: View all renewals, manage vendor relationships and contracts
- Executive: Portfolio-wide dashboard, executive summary reports

**Audit Requirements:**
- Complete audit trail for all renewal decisions and cost savings calculations
- Retention of all renewal analyses and communications for 7 years
- SOX compliance for financial impact calculations and approval workflows
- Regular access reviews and permission updates

# Performance Requirements

**Response Time Targets:**
- Renewal dashboard loading: <3 seconds for 500+ properties
- Individual contract analysis: <5 seconds for full benchmark comparison
- Email notification delivery: <2 minutes from trigger event
- Background renewal scanning: Complete 3,850+ properties within 1 hour

**Scalability Considerations:**
- Handle 500+ concurrent renewal analyses during Q4 peak season
- Process 50,000+ email notifications during high-volume periods
- Support 100+ concurrent users during renewal season
- Database optimization for efficient queries across large contract datasets

**Availability Requirements:**
- 99.9% uptime for renewal monitoring (max 8 hours downtime annually)
- 99.5% email delivery success rate with retry mechanisms
- Graceful degradation during high-load periods
- Automatic failover for critical renewal deadline notifications

# Deployment Considerations

**Docker Configuration:**
- Separate containers for web app, background tasks, and email service
- Redis for task queue management and session storage
- PostgreSQL with read replicas for reporting queries
- Nginx reverse proxy with SSL termination

**Environment Configurations:**
- Development: Local containers with test data and mock email service
- Staging: Full integration with Greystar test systems and email relay
- Production: High availability with load balancing and monitoring

**CI/CD Pipeline:**
- Automated testing for all renewal logic and financial calculations
- Database migration validation in staging environment
- Gradual rollout with feature flags for new renewal functionality
- Monitoring and alerting for renewal system health and performance

**Monitoring & Alerting:**
- Application performance monitoring with Datadog/New Relic integration
- Business metric dashboards for renewal success rates and cost savings
- Alert escalation for failed renewal notifications or system downtime
- Regular audit reports for compliance and performance review