"""
Contract Models for Advantage Waste Enterprise
=============================================

Comprehensive database models for waste management contracts extending the
ContractTerms structure from examples/contract_analysis_example.py with
full database persistence, relationships, and enterprise-grade tracking.

Supports 3,850+ Greystar properties with ACID compliance for financial data.
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List, Dict, Any
from enum import Enum
from sqlalchemy import (
    Column, Integer, String, DateTime, Date, Boolean, Numeric, 
    Text, ForeignKey, Index, CheckConstraint, UniqueConstraint,
    event, text
)
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID, JSONB, ENUM
from sqlalchemy.sql import func
import uuid

from ..core.database import Base

# Enums for contract-related choices
class ContractStatus(str, Enum):
    """Contract lifecycle status"""
    DRAFT = "draft"
    ACTIVE = "active"
    PENDING_RENEWAL = "pending_renewal"
    RENEWED = "renewed"
    TERMINATED = "terminated"
    EXPIRED = "expired"
    CANCELLED = "cancelled"

class ContractGrade(str, Enum):
    """Contract quality grades based on industry benchmarks"""
    EXCELLENT = "A+"
    GOOD = "A"
    ACCEPTABLE = "B"
    NEEDS_IMPROVEMENT = "C"
    POOR = "D"

class ContainerType(str, Enum):
    """Standard waste container types"""
    FRONT_LOAD = "front-load"
    COMPACTOR = "compactor"
    ROLL_OFF = "roll-off"
    REAR_LOAD = "rear-load"
    SIDE_LOAD = "side-load"

class PropertyType(str, Enum):
    """Multifamily property types for waste calculations"""
    GARDEN_STYLE = "garden-style"
    MID_RISE = "mid-rise"
    HIGH_RISE = "high-rise"
    MIXED_USE = "mixed-use"
    SENIOR_LIVING = "senior-living"

class CpiIndex(str, Enum):
    """Consumer Price Index types for escalation"""
    CPI_U = "CPI-U"  # All Urban Consumers
    CPI_W = "CPI-W"  # Urban Wage Earners and Clerical Workers
    REGIONAL_CPI = "regional-cpi"
    CUSTOM = "custom"

# Core Models

class Property(Base):
    """
    Property information for contract analysis and waste management.
    Links contracts to specific Greystar properties with waste metrics.
    """
    __tablename__ = "properties"
    
    # Primary identification
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    greystar_property_id: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    
    # Property details
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    address: Mapped[str] = mapped_column(Text, nullable=False)
    city: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    state: Mapped[str] = mapped_column(String(2), nullable=False, index=True)
    zip_code: Mapped[str] = mapped_column(String(10), nullable=False)
    
    # Property characteristics for waste analysis
    unit_count: Mapped[int] = mapped_column(Integer, nullable=False)
    property_type: Mapped[PropertyType] = mapped_column(ENUM(PropertyType), nullable=False, index=True)
    square_footage: Mapped[Optional[int]] = mapped_column(Integer)
    occupied_units: Mapped[Optional[int]] = mapped_column(Integer)
    occupancy_rate: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 4))  # 0.0000 to 1.0000
    
    # Management information
    property_manager: Mapped[Optional[str]] = mapped_column(String(255))
    property_manager_email: Mapped[Optional[str]] = mapped_column(String(255))
    regional_manager: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Metadata
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), onupdate=func.now())
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
    
    # Relationships
    contracts: Mapped[List["Contract"]] = relationship("Contract", back_populates="property")
    
    __table_args__ = (
        CheckConstraint('unit_count > 0', name='check_unit_count_positive'),
        CheckConstraint('occupied_units >= 0', name='check_occupied_units_non_negative'),
        CheckConstraint('occupied_units <= unit_count', name='check_occupied_units_lte_total'),
        CheckConstraint('occupancy_rate >= 0.0', name='check_occupancy_rate_non_negative'),
        CheckConstraint('occupancy_rate <= 1.0', name='check_occupancy_rate_lte_one'),
        Index('ix_properties_location', 'city', 'state'),
        Index('ix_properties_type_units', 'property_type', 'unit_count'),
    )

class Vendor(Base):
    """
    Waste management vendor information with performance tracking.
    Supports major waste management companies serving Greystar.
    """
    __tablename__ = "vendors"
    
    # Primary identification
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # Vendor details
    name: Mapped[str] = mapped_column(String(255), nullable=False, unique=True, index=True)
    legal_name: Mapped[Optional[str]] = mapped_column(String(255))
    vendor_code: Mapped[Optional[str]] = mapped_column(String(50), unique=True, index=True)
    
    # Contact information
    primary_contact: Mapped[Optional[str]] = mapped_column(String(255))
    contact_email: Mapped[Optional[str]] = mapped_column(String(255))
    contact_phone: Mapped[Optional[str]] = mapped_column(String(20))
    
    # Business information
    website: Mapped[Optional[str]] = mapped_column(String(500))
    headquarters_city: Mapped[Optional[str]] = mapped_column(String(100))
    headquarters_state: Mapped[Optional[str]] = mapped_column(String(2))
    
    # Service areas and capabilities
    service_territories: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSONB)  # States/regions served
    service_types: Mapped[Optional[List[str]]] = mapped_column(JSONB)  # Services offered
    
    # Performance metrics
    average_rating: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 2))  # 0.00 to 5.00
    contract_count: Mapped[int] = mapped_column(Integer, default=0)
    
    # Metadata
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), onupdate=func.now())
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
    
    # Relationships
    contracts: Mapped[List["Contract"]] = relationship("Contract", back_populates="vendor")
    
    __table_args__ = (
        CheckConstraint('average_rating >= 0.0', name='check_rating_non_negative'),
        CheckConstraint('average_rating <= 5.0', name='check_rating_lte_five'),
        CheckConstraint('contract_count >= 0', name='check_contract_count_non_negative'),
    )

class Contract(Base):
    """
    Comprehensive waste management contract model extending ContractTerms
    from examples/contract_analysis_example.py with full database persistence.
    
    Supports enterprise-grade contract tracking with financial accuracy
    using Decimal types for all monetary calculations.
    """
    __tablename__ = "contracts"
    
    # Primary identification
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    contract_number: Mapped[str] = mapped_column(String(100), unique=True, index=True, nullable=False)
    
    # Foreign key relationships
    property_id: Mapped[int] = mapped_column(Integer, ForeignKey("properties.id"), nullable=False, index=True)
    vendor_id: Mapped[int] = mapped_column(Integer, ForeignKey("vendors.id"), nullable=False, index=True)
    
    # Contract status and lifecycle
    status: Mapped[ContractStatus] = mapped_column(ENUM(ContractStatus), nullable=False, index=True, default=ContractStatus.DRAFT)
    grade: Mapped[Optional[ContractGrade]] = mapped_column(ENUM(ContractGrade), index=True)
    
    # Vendor contact information (from ContractTerms)
    contact_name: Mapped[Optional[str]] = mapped_column(String(255))
    contact_email: Mapped[Optional[str]] = mapped_column(String(255))
    contact_phone: Mapped[Optional[str]] = mapped_column(String(20))
    quote_number: Mapped[Optional[str]] = mapped_column(String(100), index=True)
    
    # Contract terms and dates
    contract_length_months: Mapped[int] = mapped_column(Integer, nullable=False)
    effective_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    expiration_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    
    # Renewal terms
    automatic_renewal: Mapped[bool] = mapped_column(Boolean, default=False)
    renewal_term_months: Mapped[Optional[int]] = mapped_column(Integer)
    termination_notice_days: Mapped[int] = mapped_column(Integer, nullable=False, default=30)
    
    # Service configuration
    container_size_yards: Mapped[int] = mapped_column(Integer, nullable=False)
    container_type: Mapped[ContainerType] = mapped_column(ENUM(ContainerType), nullable=False, index=True)
    container_quantity: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    pickup_frequency_weekly: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    
    # Service details and special requirements
    service_description: Mapped[Optional[str]] = mapped_column(Text)
    special_requirements: Mapped[Optional[str]] = mapped_column(Text)
    recyclables_included: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Cost structure (using Decimal for financial accuracy)
    base_monthly_rate: Mapped[Decimal] = mapped_column(Numeric(12, 2), nullable=False)
    
    # Additional fees and charges
    fuel_surcharge_percent: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))  # 0.00 to 999.99%
    environmental_fee_percent: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    admin_fee: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2))
    container_rental_fee: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2))
    initial_delivery_charge: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2))
    extra_pickup_cost: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2))
    
    # Price increase terms
    cpi_increases: Mapped[bool] = mapped_column(Boolean, default=False)
    cpi_frequency: Mapped[Optional[str]] = mapped_column(String(20))  # "annual", "semi-annual"
    cpi_index: Mapped[Optional[CpiIndex]] = mapped_column(ENUM(CpiIndex))
    max_annual_increase_percent: Mapped[Optional[Decimal]] = mapped_column(Numeric(5, 2))
    
    # Contract provisions and terms
    early_termination_fee_calculation: Mapped[Optional[str]] = mapped_column(Text)
    payment_terms_days: Mapped[int] = mapped_column(Integer, default=30)
    right_of_first_refusal_days: Mapped[Optional[int]] = mapped_column(Integer)
    
    # Performance and compliance
    performance_standards: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSONB)
    compliance_requirements: Mapped[Optional[List[str]]] = mapped_column(JSONB)
    
    # Contract documents and attachments
    contract_document_url: Mapped[Optional[str]] = mapped_column(String(500))
    insurance_certificate_url: Mapped[Optional[str]] = mapped_column(String(500))
    
    # Analysis and metrics (calculated fields)
    last_analysis_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    calculated_monthly_cost: Mapped[Optional[Decimal]] = mapped_column(Numeric(12, 2))
    calculated_cost_per_door: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 2))
    calculated_yards_per_door: Mapped[Optional[Decimal]] = mapped_column(Numeric(6, 3))
    
    # Audit trail
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), onupdate=func.now())
    created_by: Mapped[Optional[str]] = mapped_column(String(255))  # User ID or email
    updated_by: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Relationships
    property: Mapped["Property"] = relationship("Property", back_populates="contracts")
    vendor: Mapped["Vendor"] = relationship("Vendor", back_populates="contracts")
    renewal_alerts: Mapped[List["RenewalAlert"]] = relationship("RenewalAlert", back_populates="contract")
    contract_analyses: Mapped[List["ContractAnalysis"]] = relationship("ContractAnalysis", back_populates="contract")
    
    # Table constraints and indexes
    __table_args__ = (
        # Financial constraints
        CheckConstraint('base_monthly_rate > 0', name='check_base_rate_positive'),
        CheckConstraint('fuel_surcharge_percent >= 0', name='check_fuel_surcharge_non_negative'),
        CheckConstraint('environmental_fee_percent >= 0', name='check_env_fee_non_negative'),
        CheckConstraint('admin_fee >= 0', name='check_admin_fee_non_negative'),
        CheckConstraint('container_rental_fee >= 0', name='check_rental_fee_non_negative'),
        CheckConstraint('max_annual_increase_percent >= 0', name='check_max_increase_non_negative'),
        CheckConstraint('max_annual_increase_percent <= 100', name='check_max_increase_reasonable'),
        
        # Service constraints
        CheckConstraint('contract_length_months > 0', name='check_contract_length_positive'),
        CheckConstraint('container_size_yards > 0', name='check_container_size_positive'),
        CheckConstraint('container_quantity > 0', name='check_container_quantity_positive'),
        CheckConstraint('pickup_frequency_weekly > 0', name='check_pickup_frequency_positive'),
        CheckConstraint('pickup_frequency_weekly <= 7', name='check_pickup_frequency_reasonable'),
        CheckConstraint('termination_notice_days >= 0', name='check_termination_notice_non_negative'),
        CheckConstraint('payment_terms_days > 0', name='check_payment_terms_positive'),
        
        # Date constraints
        CheckConstraint('expiration_date > effective_date', name='check_dates_logical'),
        
        # Performance indexes for common queries
        Index('ix_contracts_dates', 'effective_date', 'expiration_date'),
        Index('ix_contracts_status_expiration', 'status', 'expiration_date'),
        Index('ix_contracts_property_vendor', 'property_id', 'vendor_id'),
        Index('ix_contracts_renewal_monitoring', 'expiration_date', 'status', 'automatic_renewal'),
        Index('ix_contracts_cost_analysis', 'calculated_cost_per_door', 'property_id'),
        
        # Unique constraint to prevent duplicate active contracts
        UniqueConstraint('property_id', 'effective_date', 'status', 
                        name='uq_property_active_contract_date',
                        postgresql_where=text("status IN ('active', 'pending_renewal')")),
    )
    
    def calculate_total_monthly_cost(self) -> Decimal:
        """Calculate total monthly cost including all fees"""
        total = self.base_monthly_rate
        
        if self.fuel_surcharge_percent:
            total += self.base_monthly_rate * (self.fuel_surcharge_percent / 100)
        
        if self.environmental_fee_percent:
            total += self.base_monthly_rate * (self.environmental_fee_percent / 100)
        
        if self.admin_fee:
            total += self.admin_fee
        
        if self.container_rental_fee:
            total += self.container_rental_fee
        
        return total
    
    def calculate_monthly_volume(self) -> Decimal:
        """Calculate monthly waste volume in cubic yards"""
        weekly_volume = (
            self.container_size_yards * 
            self.container_quantity * 
            self.pickup_frequency_weekly
        )
        return Decimal(str(weekly_volume * 4.33))  # 4.33 weeks per month average
    
    def is_expiring_soon(self, days_ahead: int = 90) -> bool:
        """Check if contract is expiring within specified days"""
        from datetime import timedelta
        cutoff_date = date.today() + timedelta(days=days_ahead)
        return self.expiration_date <= cutoff_date
    
    def days_until_expiration(self) -> int:
        """Calculate days until contract expiration"""
        return (self.expiration_date - date.today()).days

# SQLAlchemy event listeners for automatic calculations
@event.listens_for(Contract.base_monthly_rate, 'set')
@event.listens_for(Contract.fuel_surcharge_percent, 'set')
@event.listens_for(Contract.environmental_fee_percent, 'set')
@event.listens_for(Contract.admin_fee, 'set')
@event.listens_for(Contract.container_rental_fee, 'set')
def update_calculated_monthly_cost(target, value, oldvalue, initiator):
    """Automatically update calculated monthly cost when cost components change"""
    if target.base_monthly_rate is not None:
        target.calculated_monthly_cost = target.calculate_total_monthly_cost()

@event.listens_for(Contract, 'before_insert')
@event.listens_for(Contract, 'before_update')
def calculate_contract_metrics(mapper, connection, target):
    """Calculate derived metrics before database operations"""
    if target.base_monthly_rate is not None:
        target.calculated_monthly_cost = target.calculate_total_monthly_cost()
        
        # Calculate per-door metrics if property is loaded
        if target.property and target.property.unit_count:
            target.calculated_cost_per_door = target.calculated_monthly_cost / target.property.unit_count
            monthly_volume = target.calculate_monthly_volume()
            target.calculated_yards_per_door = monthly_volume / target.property.unit_count