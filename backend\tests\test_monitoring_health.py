"""
Monitoring and Health Check Systems Tests
==========================================

Enterprise monitoring and health check systems for Advantage Waste Contract Renewal Analysis.
Tests system health monitoring, performance benchmarks, alerting, and operational metrics.

Monitoring Requirements:
- 99.9% uptime monitoring
- Performance benchmark tracking
- Resource utilization monitoring
- Error rate and exception tracking
- Contract analysis throughput monitoring
- Database performance monitoring
- API response time monitoring
- System resource alerts

Tests cover:
- Health check endpoint functionality
- Performance metric collection
- Alert threshold validation
- System resource monitoring
- Database health checks
- API performance monitoring
- Error tracking and logging
- Operational dashboard metrics
"""

import pytest
import time
import threading
import json
from typing import Dict, Any, List
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import asyncio

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


class SystemHealthMonitor:
    """System health monitoring implementation"""
    
    def __init__(self):
        self.metrics = {
            'uptime_start': time.time(),
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0,
            'analyses_completed': 0,
            'errors_logged': 0,
            'memory_usage_mb': 0.0,
            'cpu_usage_percent': 0.0
        }
        self.health_checks = {}
        self.alerts = []
        self.monitoring_active = False
    
    def start_monitoring(self):
        """Start health monitoring"""
        self.monitoring_active = True
        self.metrics['uptime_start'] = time.time()
    
    def stop_monitoring(self):
        """Stop health monitoring"""
        self.monitoring_active = False
    
    def record_request(self, success: bool, response_time: float):
        """Record API request metrics"""
        self.metrics['total_requests'] += 1
        if success:
            self.metrics['successful_requests'] += 1
        else:
            self.metrics['failed_requests'] += 1
        
        # Update average response time
        total_time = self.metrics['avg_response_time'] * (self.metrics['total_requests'] - 1)
        self.metrics['avg_response_time'] = (total_time + response_time) / self.metrics['total_requests']
    
    def record_analysis_completion(self):
        """Record contract analysis completion"""
        self.metrics['analyses_completed'] += 1
    
    def record_error(self, error_type: str, error_message: str):
        """Record system error"""
        self.metrics['errors_logged'] += 1
        self.alerts.append({
            'timestamp': time.time(),
            'type': 'error',
            'error_type': error_type,
            'message': error_message
        })
    
    def update_system_metrics(self, memory_mb: float, cpu_percent: float):
        """Update system resource metrics"""
        self.metrics['memory_usage_mb'] = memory_mb
        self.metrics['cpu_usage_percent'] = cpu_percent
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status"""
        uptime_seconds = time.time() - self.metrics['uptime_start']
        success_rate = (self.metrics['successful_requests'] / max(self.metrics['total_requests'], 1)) * 100
        
        # Determine overall health status
        health_status = "healthy"
        if success_rate < 95:
            health_status = "degraded"
        if success_rate < 90 or self.metrics['avg_response_time'] > 2000:
            health_status = "unhealthy"
        
        return {
            'status': health_status,
            'uptime_seconds': uptime_seconds,
            'uptime_hours': uptime_seconds / 3600,
            'success_rate_percent': success_rate,
            'total_requests': self.metrics['total_requests'],
            'avg_response_time_ms': self.metrics['avg_response_time'],
            'analyses_completed': self.metrics['analyses_completed'],
            'errors_count': self.metrics['errors_logged'],
            'memory_usage_mb': self.metrics['memory_usage_mb'],
            'cpu_usage_percent': self.metrics['cpu_usage_percent'],
            'alerts_count': len(self.alerts),
            'timestamp': time.time()
        }
    
    def get_performance_benchmarks(self) -> Dict[str, Any]:
        """Get performance benchmarks and SLA compliance"""
        health = self.get_health_status()
        
        benchmarks = {
            'sla_targets': {
                'uptime_percent': 99.9,
                'max_response_time_ms': 2000,
                'min_success_rate_percent': 99.0,
                'max_error_rate_percent': 1.0
            },
            'current_performance': {
                'uptime_percent': min(100, (health['uptime_seconds'] / max(health['uptime_seconds'], 1)) * 100),
                'avg_response_time_ms': health['avg_response_time_ms'],
                'success_rate_percent': health['success_rate_percent'],
                'error_rate_percent': (health['errors_count'] / max(health['total_requests'], 1)) * 100
            }
        }
        
        # Calculate SLA compliance
        benchmarks['sla_compliance'] = {
            'uptime_compliant': benchmarks['current_performance']['uptime_percent'] >= benchmarks['sla_targets']['uptime_percent'],
            'response_time_compliant': benchmarks['current_performance']['avg_response_time_ms'] <= benchmarks['sla_targets']['max_response_time_ms'],
            'success_rate_compliant': benchmarks['current_performance']['success_rate_percent'] >= benchmarks['sla_targets']['min_success_rate_percent'],
            'error_rate_compliant': benchmarks['current_performance']['error_rate_percent'] <= benchmarks['sla_targets']['max_error_rate_percent']
        }
        
        return benchmarks


class DatabaseHealthMonitor:
    """Database health monitoring implementation"""
    
    def __init__(self):
        self.connection_pool_size = 10
        self.active_connections = 0
        self.query_times = []
        self.failed_queries = 0
        self.successful_queries = 0
    
    async def check_database_health(self) -> Dict[str, Any]:
        """Check database health status"""
        # Simulate database health check
        start_time = time.time()
        
        try:
            # Simulate database query
            await asyncio.sleep(0.01)  # Simulate 10ms query
            query_time = (time.time() - start_time) * 1000
            
            self.query_times.append(query_time)
            self.successful_queries += 1
            
            # Keep only recent query times
            if len(self.query_times) > 100:
                self.query_times = self.query_times[-100:]
            
            avg_query_time = sum(self.query_times) / len(self.query_times)
            
            return {
                'status': 'healthy',
                'connection_pool_used': self.active_connections,
                'connection_pool_size': self.connection_pool_size,
                'avg_query_time_ms': avg_query_time,
                'successful_queries': self.successful_queries,
                'failed_queries': self.failed_queries,
                'last_check_time': time.time()
            }
            
        except Exception as e:
            self.failed_queries += 1
            return {
                'status': 'unhealthy',
                'error': str(e),
                'connection_pool_used': self.active_connections,
                'connection_pool_size': self.connection_pool_size,
                'successful_queries': self.successful_queries,
                'failed_queries': self.failed_queries,
                'last_check_time': time.time()
            }
    
    def simulate_connection_usage(self, connections_used: int):
        """Simulate database connection usage"""
        self.active_connections = min(connections_used, self.connection_pool_size)


@pytest.fixture
def health_monitor():
    """System health monitor fixture"""
    monitor = SystemHealthMonitor()
    yield monitor
    monitor.stop_monitoring()


@pytest.fixture
def db_health_monitor():
    """Database health monitor fixture"""
    return DatabaseHealthMonitor()


class TestHealthCheckEndpoint:
    """Test health check endpoint functionality"""
    
    def test_health_check_basic_response(self):
        """Test basic health check response structure"""
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        # Required fields
        required_fields = ["status", "service", "version", "timestamp"]
        for field in required_fields:
            assert field in data
            assert data[field] is not None
        
        # Validate field types and values
        assert isinstance(data["status"], str)
        assert data["status"] == "healthy"
        assert isinstance(data["service"], str)
        assert data["service"] == "advantage-waste-enterprise-api"
        assert isinstance(data["version"], str)
        assert isinstance(data["timestamp"], (int, float))
        
        # Timestamp should be recent (within 1 second)
        current_time = time.time()
        assert abs(data["timestamp"] - current_time) < 1.0
    
    def test_health_check_response_time(self):
        """Test health check response time performance"""
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        # Test multiple requests to get average
        response_times = []
        
        for _ in range(10):
            start_time = time.time()
            response = client.get("/health")
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # Convert to ms
            response_times.append(response_time)
            
            assert response.status_code == 200
        
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        print(f"Health Check Performance - Avg: {avg_response_time:.2f}ms, Max: {max_response_time:.2f}ms")
        
        # Health check should be very fast
        assert avg_response_time < 50  # Average under 50ms
        assert max_response_time < 100  # Maximum under 100ms
    
    def test_health_check_under_load(self):
        """Test health check endpoint under concurrent load"""
        from fastapi.testclient import TestClient
        from main import app
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        client = TestClient(app)
        
        def make_health_request():
            start_time = time.time()
            response = client.get("/health")
            end_time = time.time()
            return {
                'status_code': response.status_code,
                'response_time_ms': (end_time - start_time) * 1000
            }
        
        # Make 50 concurrent requests
        results = []
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(make_health_request) for _ in range(50)]
            for future in as_completed(futures):
                results.append(future.result())
        
        # Analyze results
        successful_requests = [r for r in results if r['status_code'] == 200]
        response_times = [r['response_time_ms'] for r in results]
        
        success_rate = len(successful_requests) / len(results) * 100
        avg_response_time = sum(response_times) / len(response_times)
        
        print(f"Health Check Under Load - Success Rate: {success_rate:.1f}%, Avg Response: {avg_response_time:.2f}ms")
        
        # Should handle concurrent load well
        assert success_rate == 100  # All requests should succeed
        assert avg_response_time < 200  # Should remain fast under load


class TestSystemHealthMonitoring:
    """Test system health monitoring functionality"""
    
    def test_health_monitor_initialization(self, health_monitor):
        """Test health monitor initialization"""
        health_monitor.start_monitoring()
        
        status = health_monitor.get_health_status()
        
        assert status['status'] == 'healthy'
        assert status['total_requests'] == 0
        assert status['success_rate_percent'] == 100  # No requests yet
        assert status['analyses_completed'] == 0
        assert status['errors_count'] == 0
        assert status['uptime_seconds'] >= 0
    
    def test_request_tracking(self, health_monitor):
        """Test request tracking functionality"""
        health_monitor.start_monitoring()
        
        # Record successful requests
        health_monitor.record_request(True, 100)  # 100ms
        health_monitor.record_request(True, 200)  # 200ms
        health_monitor.record_request(False, 500)  # Failed request
        
        status = health_monitor.get_health_status()
        
        assert status['total_requests'] == 3
        assert status['successful_requests'] == 2
        assert status['failed_requests'] == 1
        assert abs(status['success_rate_percent'] - 66.67) < 0.1
        assert abs(status['avg_response_time_ms'] - 266.67) < 0.1  # (100+200+500)/3
    
    def test_analysis_completion_tracking(self, health_monitor):
        """Test contract analysis completion tracking"""
        health_monitor.start_monitoring()
        
        # Record multiple analysis completions
        for _ in range(10):
            health_monitor.record_analysis_completion()
        
        status = health_monitor.get_health_status()
        
        assert status['analyses_completed'] == 10
    
    def test_error_recording(self, health_monitor):
        """Test error recording and alerting"""
        health_monitor.start_monitoring()
        
        # Record various errors
        health_monitor.record_error("ValidationError", "Invalid contract data")
        health_monitor.record_error("DatabaseError", "Connection timeout")
        health_monitor.record_error("APIError", "External service unavailable")
        
        status = health_monitor.get_health_status()
        
        assert status['errors_count'] == 3
        assert status['alerts_count'] == 3
        assert len(health_monitor.alerts) == 3
        
        # Check alert details
        for alert in health_monitor.alerts:
            assert 'timestamp' in alert
            assert 'type' in alert
            assert 'error_type' in alert
            assert 'message' in alert
    
    def test_health_status_degradation(self, health_monitor):
        """Test health status degradation based on metrics"""
        health_monitor.start_monitoring()
        
        # Initially healthy
        status = health_monitor.get_health_status()
        assert status['status'] == 'healthy'
        
        # Add failed requests to degrade success rate
        for _ in range(8):
            health_monitor.record_request(False, 1000)  # Failed requests
        for _ in range(2):
            health_monitor.record_request(True, 100)   # Some successful requests
        
        status = health_monitor.get_health_status()
        # Success rate is now 20%, should be unhealthy
        assert status['status'] in ['degraded', 'unhealthy']
        assert status['success_rate_percent'] == 20
    
    def test_system_resource_monitoring(self, health_monitor):
        """Test system resource monitoring"""
        health_monitor.start_monitoring()
        
        # Update system metrics
        health_monitor.update_system_metrics(256.5, 45.3)  # 256.5MB memory, 45.3% CPU
        
        status = health_monitor.get_health_status()
        
        assert status['memory_usage_mb'] == 256.5
        assert status['cpu_usage_percent'] == 45.3
    
    def test_performance_benchmarks(self, health_monitor):
        """Test performance benchmark calculation"""
        health_monitor.start_monitoring()
        
        # Record some activity
        health_monitor.record_request(True, 500)   # Within SLA
        health_monitor.record_request(True, 1500)  # Within SLA
        health_monitor.record_request(True, 800)   # Within SLA
        
        benchmarks = health_monitor.get_performance_benchmarks()
        
        # Check SLA targets
        sla_targets = benchmarks['sla_targets']
        assert sla_targets['uptime_percent'] == 99.9
        assert sla_targets['max_response_time_ms'] == 2000
        assert sla_targets['min_success_rate_percent'] == 99.0
        
        # Check current performance
        current = benchmarks['current_performance']
        assert current['success_rate_percent'] == 100
        assert current['avg_response_time_ms'] < 2000
        
        # Check SLA compliance
        compliance = benchmarks['sla_compliance']
        assert compliance['success_rate_compliant'] == True
        assert compliance['response_time_compliant'] == True


class TestDatabaseHealthMonitoring:
    """Test database health monitoring functionality"""
    
    @pytest.mark.asyncio
    async def test_database_health_check_success(self, db_health_monitor):
        """Test successful database health check"""
        health = await db_health_monitor.check_database_health()
        
        assert health['status'] == 'healthy'
        assert 'connection_pool_used' in health
        assert 'connection_pool_size' in health
        assert 'avg_query_time_ms' in health
        assert 'successful_queries' in health
        assert 'failed_queries' in health
        assert 'last_check_time' in health
        
        # Query time should be reasonable
        assert health['avg_query_time_ms'] > 0
        assert health['avg_query_time_ms'] < 100  # Should be fast
        
        # Should have recorded one successful query
        assert health['successful_queries'] >= 1
        assert health['failed_queries'] == 0
    
    @pytest.mark.asyncio
    async def test_database_health_check_multiple(self, db_health_monitor):
        """Test multiple database health checks"""
        # Perform multiple health checks
        for _ in range(5):
            health = await db_health_monitor.check_database_health()
            assert health['status'] == 'healthy'
        
        final_health = await db_health_monitor.check_database_health()
        
        # Should have multiple successful queries
        assert final_health['successful_queries'] >= 5
        assert final_health['failed_queries'] == 0
        
        # Average query time should be reasonable
        assert final_health['avg_query_time_ms'] > 0
        assert final_health['avg_query_time_ms'] < 50
    
    def test_connection_pool_monitoring(self, db_health_monitor):
        """Test database connection pool monitoring"""
        # Simulate different connection usage levels
        test_scenarios = [
            (0, "no connections"),
            (5, "half pool"),
            (9, "near capacity"),
            (10, "full capacity"),
            (15, "over capacity")  # Should be capped at pool size
        ]
        
        for connections, scenario in test_scenarios:
            db_health_monitor.simulate_connection_usage(connections)
            
            # Connection usage should be capped at pool size
            expected_usage = min(connections, db_health_monitor.connection_pool_size)
            assert db_health_monitor.active_connections == expected_usage


class TestPerformanceMonitoring:
    """Test performance monitoring and alerting"""
    
    def test_response_time_monitoring(self, health_monitor):
        """Test response time monitoring and thresholds"""
        health_monitor.start_monitoring()
        
        # Record requests with various response times
        response_times = [50, 100, 150, 500, 1000, 2500, 3000]  # Some exceed threshold
        
        for rt in response_times:
            health_monitor.record_request(True, rt)
        
        status = health_monitor.get_health_status()
        benchmarks = health_monitor.get_performance_benchmarks()
        
        # Average should be around 1057ms
        assert status['avg_response_time_ms'] > 1000
        
        # Should not be compliant with 2000ms SLA due to high average
        if status['avg_response_time_ms'] > 2000:
            assert benchmarks['sla_compliance']['response_time_compliant'] == False
    
    def test_throughput_monitoring(self, health_monitor):
        """Test throughput monitoring"""
        health_monitor.start_monitoring()
        
        start_time = time.time()
        
        # Simulate processing many analyses
        for _ in range(100):
            health_monitor.record_analysis_completion()
            health_monitor.record_request(True, 100)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        status = health_monitor.get_health_status()
        
        # Calculate throughput
        analyses_per_second = status['analyses_completed'] / elapsed_time
        requests_per_second = status['total_requests'] / elapsed_time
        
        print(f"Throughput - Analyses: {analyses_per_second:.1f}/sec, Requests: {requests_per_second:.1f}/sec")
        
        assert status['analyses_completed'] == 100
        assert status['total_requests'] == 100
        assert analyses_per_second > 0
        assert requests_per_second > 0
    
    def test_error_rate_monitoring(self, health_monitor):
        """Test error rate monitoring and alerting"""
        health_monitor.start_monitoring()
        
        # Create scenarios with different error rates
        scenarios = [
            (95, 5, "low error rate"),      # 5% error rate
            (90, 10, "medium error rate"),  # 10% error rate
            (80, 20, "high error rate"),    # 20% error rate
        ]
        
        for successful, failed, scenario_name in scenarios:
            # Reset monitor for each scenario
            health_monitor = SystemHealthMonitor()
            health_monitor.start_monitoring()
            
            # Record requests
            for _ in range(successful):
                health_monitor.record_request(True, 100)
            for _ in range(failed):
                health_monitor.record_request(False, 500)
            
            benchmarks = health_monitor.get_performance_benchmarks()
            error_rate = benchmarks['current_performance']['error_rate_percent']
            
            print(f"{scenario_name}: {error_rate:.1f}% error rate")
            
            # Validate error rate calculation
            expected_error_rate = (failed / (successful + failed)) * 100
            assert abs(error_rate - expected_error_rate) < 0.1
            
            # Check SLA compliance
            if error_rate > 1.0:  # SLA allows max 1% error rate
                assert benchmarks['sla_compliance']['error_rate_compliant'] == False


class TestAlertingSystem:
    """Test alerting and notification system"""
    
    def test_error_alerts(self, health_monitor):
        """Test error-based alerting"""
        health_monitor.start_monitoring()
        
        # Generate various types of errors
        error_scenarios = [
            ("ValidationError", "Invalid contract data provided"),
            ("DatabaseConnectionError", "Unable to connect to database"),
            ("ExternalAPIError", "Vendor API service unavailable"),
            ("AuthenticationError", "Invalid authentication token"),
            ("CalculationError", "Division by zero in cost calculation")
        ]
        
        for error_type, error_message in error_scenarios:
            health_monitor.record_error(error_type, error_message)
        
        # Check alerts were generated
        assert len(health_monitor.alerts) == len(error_scenarios)
        
        # Validate alert structure
        for i, alert in enumerate(health_monitor.alerts):
            expected_error_type, expected_message = error_scenarios[i]
            
            assert alert['type'] == 'error'
            assert alert['error_type'] == expected_error_type
            assert alert['message'] == expected_message
            assert 'timestamp' in alert
            assert isinstance(alert['timestamp'], (int, float))
    
    def test_performance_degradation_alerts(self, health_monitor):
        """Test performance degradation alerts"""
        health_monitor.start_monitoring()
        
        # Simulate performance degradation
        for _ in range(10):
            health_monitor.record_request(True, 3000)  # Slow requests
        
        benchmarks = health_monitor.get_performance_benchmarks()
        
        # Should detect SLA violation
        if benchmarks['current_performance']['avg_response_time_ms'] > 2000:
            assert benchmarks['sla_compliance']['response_time_compliant'] == False
            
            # In a real system, this would trigger an alert
            print("Alert: Response time SLA violation detected")
    
    def test_resource_utilization_alerts(self, health_monitor):
        """Test resource utilization alerting"""
        health_monitor.start_monitoring()
        
        # Test various resource usage scenarios
        resource_scenarios = [
            (100, 30, "normal usage"),      # 100MB, 30% CPU
            (400, 70, "high usage"),        # 400MB, 70% CPU  
            (600, 90, "critical usage"),    # 600MB, 90% CPU
        ]
        
        for memory_mb, cpu_percent, scenario in resource_scenarios:
            health_monitor.update_system_metrics(memory_mb, cpu_percent)
            status = health_monitor.get_health_status()
            
            print(f"{scenario}: {memory_mb}MB memory, {cpu_percent}% CPU")
            
            # In a real system, would check against thresholds and alert
            if memory_mb > 512:  # Memory threshold
                print(f"Alert: High memory usage detected: {memory_mb}MB")
            
            if cpu_percent > 80:  # CPU threshold
                print(f"Alert: High CPU usage detected: {cpu_percent}%")


class TestOperationalDashboard:
    """Test operational dashboard metrics"""
    
    def test_dashboard_metrics_collection(self, health_monitor):
        """Test collection of dashboard metrics"""
        health_monitor.start_monitoring()
        
        # Simulate realistic operational activity
        for i in range(50):
            # Mix of successful and failed requests
            success = i % 10 != 0  # 10% failure rate
            response_time = 100 + (i % 5) * 50  # 100-300ms range
            
            health_monitor.record_request(success, response_time)
            
            if success:
                health_monitor.record_analysis_completion()
        
        # Add some errors
        health_monitor.record_error("ValidationError", "Test error 1")
        health_monitor.record_error("DatabaseError", "Test error 2")
        
        # Update system resources
        health_monitor.update_system_metrics(256.0, 45.5)
        
        # Get comprehensive status
        status = health_monitor.get_health_status()
        benchmarks = health_monitor.get_performance_benchmarks()
        
        # Validate comprehensive metrics for dashboard
        dashboard_metrics = {
            'system_health': {
                'status': status['status'],
                'uptime_hours': status['uptime_hours'],
                'success_rate': status['success_rate_percent']
            },
            'performance': {
                'avg_response_time': status['avg_response_time_ms'],
                'total_requests': status['total_requests'],
                'analyses_completed': status['analyses_completed']
            },
            'resources': {
                'memory_usage_mb': status['memory_usage_mb'],
                'cpu_usage_percent': status['cpu_usage_percent']
            },
            'errors': {
                'error_count': status['errors_count'],
                'alert_count': status['alerts_count']
            },
            'sla_compliance': benchmarks['sla_compliance']
        }
        
        # Validate dashboard metrics structure
        assert 'system_health' in dashboard_metrics
        assert 'performance' in dashboard_metrics
        assert 'resources' in dashboard_metrics
        assert 'errors' in dashboard_metrics
        assert 'sla_compliance' in dashboard_metrics
        
        # Validate metric values
        assert dashboard_metrics['system_health']['success_rate'] == 90.0  # 45 success / 50 total
        assert dashboard_metrics['performance']['analyses_completed'] == 45  # Only successful analyses
        assert dashboard_metrics['errors']['error_count'] == 2
        
        print("Dashboard Metrics Summary:")
        print(f"  System Status: {dashboard_metrics['system_health']['status']}")
        print(f"  Success Rate: {dashboard_metrics['system_health']['success_rate']:.1f}%")
        print(f"  Avg Response Time: {dashboard_metrics['performance']['avg_response_time']:.1f}ms")
        print(f"  Analyses Completed: {dashboard_metrics['performance']['analyses_completed']}")
        print(f"  Memory Usage: {dashboard_metrics['resources']['memory_usage_mb']:.1f}MB")
        print(f"  CPU Usage: {dashboard_metrics['resources']['cpu_usage_percent']:.1f}%")
    
    def test_real_time_metrics_updates(self, health_monitor):
        """Test real-time metrics updates"""
        health_monitor.start_monitoring()
        
        # Capture initial state
        initial_status = health_monitor.get_health_status()
        initial_timestamp = initial_status['timestamp']
        
        # Wait and add activity
        time.sleep(0.1)
        health_monitor.record_request(True, 150)
        health_monitor.record_analysis_completion()
        
        # Get updated state
        updated_status = health_monitor.get_health_status()
        updated_timestamp = updated_status['timestamp']
        
        # Verify metrics updated
        assert updated_status['total_requests'] > initial_status['total_requests']
        assert updated_status['analyses_completed'] > initial_status['analyses_completed']
        assert updated_timestamp > initial_timestamp
        
        # Verify uptime increased
        assert updated_status['uptime_seconds'] > initial_status['uptime_seconds']


class TestMonitoringIntegration:
    """Test monitoring system integration"""
    
    @pytest.mark.asyncio
    async def test_comprehensive_health_check(self, health_monitor, db_health_monitor):
        """Test comprehensive health check across all components"""
        health_monitor.start_monitoring()
        
        # Simulate system activity
        health_monitor.record_request(True, 100)
        health_monitor.record_analysis_completion()
        health_monitor.update_system_metrics(200.5, 35.2)
        
        # Get database health
        db_health = await db_health_monitor.check_database_health()
        
        # Get system health
        system_health = health_monitor.get_health_status()
        benchmarks = health_monitor.get_performance_benchmarks()
        
        # Combine into comprehensive health report
        comprehensive_health = {
            'overall_status': 'healthy',  # Would be calculated based on all components
            'timestamp': time.time(),
            'components': {
                'system': {
                    'status': system_health['status'],
                    'uptime_seconds': system_health['uptime_seconds'],
                    'success_rate_percent': system_health['success_rate_percent'],
                    'avg_response_time_ms': system_health['avg_response_time_ms']
                },
                'database': {
                    'status': db_health['status'],
                    'avg_query_time_ms': db_health['avg_query_time_ms'],
                    'connection_pool_usage': f"{db_health['connection_pool_used']}/{db_health['connection_pool_size']}"
                }
            },
            'sla_compliance': benchmarks['sla_compliance'],
            'resource_usage': {
                'memory_mb': system_health['memory_usage_mb'],
                'cpu_percent': system_health['cpu_usage_percent']
            }
        }
        
        # Validate comprehensive health structure
        assert 'overall_status' in comprehensive_health
        assert 'components' in comprehensive_health
        assert 'system' in comprehensive_health['components']
        assert 'database' in comprehensive_health['components']
        
        # All components should be healthy
        assert comprehensive_health['components']['system']['status'] == 'healthy'
        assert comprehensive_health['components']['database']['status'] == 'healthy'
        
        print("Comprehensive Health Check:")
        print(f"  Overall Status: {comprehensive_health['overall_status']}")
        print(f"  System Status: {comprehensive_health['components']['system']['status']}")
        print(f"  Database Status: {comprehensive_health['components']['database']['status']}")
        print(f"  Memory Usage: {comprehensive_health['resource_usage']['memory_mb']}MB")
        print(f"  CPU Usage: {comprehensive_health['resource_usage']['cpu_percent']}%")