# Alembic configuration for Advantage Waste Enterprise
# Enterprise-grade database migration configuration

[alembic]
# Path to migration scripts
script_location = migrations

# Template used to generate migration files
file_template = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s

# Timezone for migration timestamps
timezone = UTC

# Max length of characters to apply to the environment name
truncate_slug_length = 40

# The output encoding used when revision files are written
output_encoding = utf-8

# Database URL placeholder - will be set via environment variable
sqlalchemy.url = postgresql://postgres:postgres@localhost:5432/advantage_waste

[post_write_hooks]
# Post-write hooks for code formatting
hooks = black
black.type = console_scripts
black.entrypoint = black
black.options = REVISION_SCRIPT_FILENAME

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S