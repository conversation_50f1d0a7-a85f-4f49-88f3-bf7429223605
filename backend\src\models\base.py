"""
Base model classes and mixins for Advantage Waste Enterprise.
Provides common functionality for all database models.
"""

from sqlalchemy import Column, DateTime, Integer, String, Text
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declared_attr
from datetime import datetime
from typing import Optional


class TimestampMixin:
    """
    Mixin to add created_at and updated_at timestamps to models.
    Automatically manages timestamp fields for audit trails.
    """
    
    @declared_attr
    def created_at(cls):
        return Column(
            DateTime(timezone=True),
            server_default=func.now(),
            nullable=False,
            comment="Record creation timestamp"
        )
    
    @declared_attr
    def updated_at(cls):
        return Column(
            DateTime(timezone=True),
            server_default=func.now(),
            onupdate=func.now(),
            nullable=False,
            comment="Record last update timestamp"
        )


class AuditMixin(TimestampMixin):
    """
    Extended mixin for audit trails including user tracking.
    Adds created_by and updated_by fields for compliance.
    """
    
    @declared_attr
    def created_by(cls):
        return Column(
            String(100),
            nullable=True,
            comment="User who created this record"
        )
    
    @declared_attr
    def updated_by(cls):
        return Column(
            String(100),
            nullable=True,
            comment="User who last updated this record"
        )
    
    @declared_attr
    def notes(cls):
        return Column(
            Text,
            nullable=True,
            comment="Additional notes or comments"
        )