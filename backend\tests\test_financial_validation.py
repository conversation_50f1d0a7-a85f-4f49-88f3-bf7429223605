"""
Financial Calculation Validation Tests
======================================

Comprehensive validation tests for financial calculations using known contract scenarios.
Ensures accuracy of all waste management financial formulas and industry benchmarks.

Validates:
- Monthly volume calculations (Container Size × Quantity × Frequency × 4.33)
- Cost per door calculations (Total Monthly Cost ÷ Number of Units)
- Cost per yard calculations (Total Monthly Cost ÷ Monthly Volume)
- Benchmark variance calculations
- Potential savings estimations
- Fee calculations (fuel surcharge, environmental fees)
- Annual cost projections
- Industry formula compliance

Uses real Greystar contract data and industry benchmarks for validation.
"""

import pytest
import math
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, Any, Tuple, List
from datetime import datetime

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from examples.contract_analysis_example import (
    ContractAnalyzer, ContractTerms, PropertyInfo
)


class FinancialCalculationValidator:
    """Utility class for validating financial calculations"""
    
    @staticmethod
    def calculate_monthly_volume(container_size: int, quantity: int, frequency: int) -> float:
        """Calculate monthly volume using industry formula"""
        # Industry standard: Container Size × Quantity × Frequency × 4.33 weeks/month
        return container_size * quantity * frequency * 4.33
    
    @staticmethod
    def calculate_total_monthly_cost(base_rate: Decimal, fuel_percent: float = None, 
                                   env_percent: float = None, admin_fee: Decimal = None,
                                   rental_fee: Decimal = None) -> Decimal:
        """Calculate total monthly cost with all fees"""
        total = base_rate
        
        if fuel_percent:
            total += base_rate * Decimal(str(fuel_percent / 100))
        
        if env_percent:
            total += base_rate * Decimal(str(env_percent / 100))
            
        if admin_fee:
            total += admin_fee
            
        if rental_fee:
            total += rental_fee
            
        return total
    
    @staticmethod
    def calculate_cost_per_door(total_monthly_cost: Decimal, unit_count: int) -> float:
        """Calculate cost per door"""
        return float(total_monthly_cost / unit_count)
    
    @staticmethod
    def calculate_cost_per_yard(total_monthly_cost: Decimal, monthly_volume: float) -> float:
        """Calculate cost per cubic yard"""
        if monthly_volume == 0:
            return 0.0
        return float(total_monthly_cost / Decimal(str(monthly_volume)))
    
    @staticmethod
    def calculate_benchmark_variance(value: float, benchmark_range: Tuple[float, float]) -> float:
        """Calculate percentage variance from benchmark range"""
        if value < benchmark_range[0]:
            return ((benchmark_range[0] - value) / benchmark_range[0]) * -100
        elif value > benchmark_range[1]:
            return ((value - benchmark_range[1]) / benchmark_range[1]) * 100
        else:
            return 0.0


@pytest.fixture
def financial_validator():
    """Financial calculation validator instance"""
    return FinancialCalculationValidator()


@pytest.fixture
def known_contract_scenarios():
    """Known contract scenarios with pre-calculated expected results"""
    return [
        {
            "name": "Columbia Square GFL Contract",
            "contract": ContractTerms(
                vendor_name="GFL Environmental",
                contact_name="John Smith",
                contact_info="<EMAIL>",
                quote_number="GFL-2024-001",
                contract_length_months=60,
                effective_date=datetime(2024, 1, 1),
                automatic_renewal=True,
                renewal_term_months=12,
                termination_notice_days=30,
                container_size_yards=34,
                container_type="compactor",
                container_quantity=1,
                pickup_frequency_weekly=1,
                base_monthly_rate=Decimal("663.91"),
                fuel_surcharge_percent=8.0,
                environmental_fee_percent=5.0,
                extra_pickup_cost=Decimal("246.75"),
                max_annual_increase_percent=6.0
            ),
            "property": PropertyInfo(
                name="Columbia Square Living",
                address="123 Main St, Dallas, TX",
                unit_count=252,
                property_type="garden-style"
            ),
            "expected_metrics": {
                "monthly_volume_yards": 147.22,  # 34 × 1 × 1 × 4.33
                "base_monthly_cost": 663.91,
                "fuel_surcharge_amount": 53.11,  # 663.91 × 0.08
                "environmental_fee_amount": 33.20,  # 663.91 × 0.05
                "total_monthly_cost": 750.22,  # 663.91 + 53.11 + 33.20
                "cost_per_door": 2.98,  # 750.22 ÷ 252
                "cost_per_yard": 5.10,  # 750.22 ÷ 147.22
                "yards_per_door": 0.58,  # 147.22 ÷ 252
                "annual_cost": 9002.64  # 750.22 × 12
            }
        },
        {
            "name": "Columbia Square WM Contract", 
            "contract": ContractTerms(
                vendor_name="Waste Management",
                contact_name="Jane Doe",
                contact_info="<EMAIL>",
                quote_number="WM-2024-002",
                contract_length_months=12,
                effective_date=datetime(2024, 1, 1),
                automatic_renewal=True,
                renewal_term_months=12,
                termination_notice_days=30,
                container_size_yards=34,
                container_type="compactor",
                container_quantity=1,
                pickup_frequency_weekly=1,
                base_monthly_rate=Decimal("745.00"),
                container_rental_fee=Decimal("650.00"),
                fuel_surcharge_percent=None,
                environmental_fee_percent=None,
                extra_pickup_cost=Decimal("195.00"),
                max_annual_increase_percent=4.0
            ),
            "property": PropertyInfo(
                name="Columbia Square Living",
                address="123 Main St, Dallas, TX",
                unit_count=252,
                property_type="garden-style"
            ),
            "expected_metrics": {
                "monthly_volume_yards": 147.22,  # 34 × 1 × 1 × 4.33
                "base_monthly_cost": 745.00,
                "fuel_surcharge_amount": 0.00,
                "environmental_fee_amount": 0.00,
                "total_monthly_cost": 1395.00,  # 745.00 + 650.00
                "cost_per_door": 5.54,  # 1395.00 ÷ 252
                "cost_per_yard": 9.47,  # 1395.00 ÷ 147.22
                "yards_per_door": 0.58,  # 147.22 ÷ 252
                "annual_cost": 16740.00  # 1395.00 × 12
            }
        },
        {
            "name": "High-Rise Multiple Container Setup",
            "contract": ContractTerms(
                vendor_name="Urban Waste Solutions",
                contact_name="Mike Johnson",
                contact_info="<EMAIL>",
                quote_number="UWS-2024-003",
                contract_length_months=24,
                effective_date=datetime(2024, 1, 1),
                automatic_renewal=True,
                renewal_term_months=12,
                termination_notice_days=60,
                container_size_yards=4,
                container_type="front-load",
                container_quantity=6,
                pickup_frequency_weekly=3,
                base_monthly_rate=Decimal("3200.00"),
                fuel_surcharge_percent=4.0,
                admin_fee=Decimal("75.00"),
                max_annual_increase_percent=3.5
            ),
            "property": PropertyInfo(
                name="Metropolitan Tower",
                address="789 Urban Ave, Atlanta, GA",
                unit_count=450,
                property_type="high-rise"
            ),
            "expected_metrics": {
                "monthly_volume_yards": 311.76,  # 4 × 6 × 3 × 4.33
                "base_monthly_cost": 3200.00,
                "fuel_surcharge_amount": 128.00,  # 3200.00 × 0.04
                "environmental_fee_amount": 0.00,
                "total_monthly_cost": 3403.00,  # 3200.00 + 128.00 + 75.00
                "cost_per_door": 7.56,  # 3403.00 ÷ 450
                "cost_per_yard": 10.92,  # 3403.00 ÷ 311.76
                "yards_per_door": 0.69,  # 311.76 ÷ 450
                "annual_cost": 40836.00  # 3403.00 × 12
            }
        }
    ]


class TestMonthlyVolumeCalculations:
    """Test monthly volume calculation accuracy"""
    
    def test_standard_compactor_volume(self, contract_analyzer, financial_validator):
        """Test standard 34-yard compactor volume calculation"""
        # Standard setup: 34-yard compactor, 1 container, 1 pickup/week
        container_size = 34
        quantity = 1
        frequency = 1
        
        expected_volume = financial_validator.calculate_monthly_volume(container_size, quantity, frequency)
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-001",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=container_size,
            container_type="compactor",
            container_quantity=quantity,
            pickup_frequency_weekly=frequency,
            base_monthly_rate=Decimal("1000.00")
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", 200, "garden-style")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        # Verify calculation accuracy
        assert abs(analysis['metrics']['monthly_volume_yards'] - expected_volume) < 0.01
        assert abs(analysis['metrics']['weekly_capacity'] - (container_size * quantity * frequency)) < 0.01
    
    def test_multiple_container_volume(self, contract_analyzer, financial_validator):
        """Test volume calculation with multiple containers"""
        # Multiple front-load containers: 4-yard containers, 6 containers, 3 pickups/week
        container_size = 4
        quantity = 6
        frequency = 3
        
        expected_volume = financial_validator.calculate_monthly_volume(container_size, quantity, frequency)
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-002",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=container_size,
            container_type="front-load",
            container_quantity=quantity,
            pickup_frequency_weekly=frequency,
            base_monthly_rate=Decimal("2000.00")
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", 300, "high-rise")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        # Verify calculation accuracy
        assert abs(analysis['metrics']['monthly_volume_yards'] - expected_volume) < 0.01
        # Weekly capacity should be: 4 × 6 × 3 = 72 cubic yards
        assert abs(analysis['metrics']['weekly_capacity'] - 72) < 0.01
    
    def test_high_frequency_pickup_volume(self, contract_analyzer, financial_validator):
        """Test volume calculation with high pickup frequency"""
        # Daily pickup: 2-yard containers, 4 containers, 7 pickups/week
        container_size = 2
        quantity = 4  
        frequency = 7
        
        expected_volume = financial_validator.calculate_monthly_volume(container_size, quantity, frequency)
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-003",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=container_size,
            container_type="front-load",
            container_quantity=quantity,
            pickup_frequency_weekly=frequency,
            base_monthly_rate=Decimal("4000.00")
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", 150, "mid-rise")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        # Verify calculation accuracy
        assert abs(analysis['metrics']['monthly_volume_yards'] - expected_volume) < 0.01
        # Weekly capacity should be: 2 × 4 × 7 = 56 cubic yards
        assert abs(analysis['metrics']['weekly_capacity'] - 56) < 0.01


class TestCostCalculations:
    """Test cost calculation accuracy"""
    
    def test_base_cost_only(self, contract_analyzer, financial_validator):
        """Test cost calculation with base rate only"""
        base_rate = Decimal("1500.00")
        unit_count = 200
        
        expected_total_cost = financial_validator.calculate_total_monthly_cost(base_rate)
        expected_cost_per_door = financial_validator.calculate_cost_per_door(expected_total_cost, unit_count)
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-004",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", unit_count, "garden-style")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        # Verify calculations
        assert abs(analysis['metrics']['total_monthly_cost'] - float(expected_total_cost)) < 0.01
        assert abs(analysis['metrics']['base_monthly_cost'] - float(base_rate)) < 0.01
        assert abs(analysis['metrics']['cost_per_door'] - expected_cost_per_door) < 0.01
        assert analysis['metrics']['annual_cost'] == analysis['metrics']['total_monthly_cost'] * 12
    
    def test_fuel_surcharge_calculation(self, contract_analyzer, financial_validator):
        """Test fuel surcharge calculation accuracy"""
        base_rate = Decimal("2000.00")
        fuel_percent = 7.5
        unit_count = 300
        
        expected_total_cost = financial_validator.calculate_total_monthly_cost(base_rate, fuel_percent=fuel_percent)
        expected_fuel_amount = base_rate * Decimal(str(fuel_percent / 100))
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-005",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate,
            fuel_surcharge_percent=fuel_percent
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", unit_count, "mid-rise")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        # Verify fuel surcharge calculation
        assert abs(analysis['metrics']['fuel_surcharge_amount'] - float(expected_fuel_amount)) < 0.01
        assert abs(analysis['metrics']['total_monthly_cost'] - float(expected_total_cost)) < 0.01
    
    def test_environmental_fee_calculation(self, contract_analyzer, financial_validator):
        """Test environmental fee calculation accuracy"""
        base_rate = Decimal("1800.00")
        env_percent = 4.5
        unit_count = 250
        
        expected_total_cost = financial_validator.calculate_total_monthly_cost(base_rate, env_percent=env_percent)
        expected_env_amount = base_rate * Decimal(str(env_percent / 100))
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-006",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate,
            environmental_fee_percent=env_percent
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", unit_count, "garden-style")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        # Verify environmental fee calculation
        assert abs(analysis['metrics']['environmental_fee_amount'] - float(expected_env_amount)) < 0.01
        assert abs(analysis['metrics']['total_monthly_cost'] - float(expected_total_cost)) < 0.01
    
    def test_all_fees_combined(self, contract_analyzer, financial_validator):
        """Test calculation with all fees combined"""
        base_rate = Decimal("2500.00")
        fuel_percent = 6.0
        env_percent = 3.5
        admin_fee = Decimal("125.00")
        rental_fee = Decimal("200.00")
        unit_count = 400
        
        expected_total_cost = financial_validator.calculate_total_monthly_cost(
            base_rate, fuel_percent, env_percent, admin_fee, rental_fee
        )
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-007",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate,
            fuel_surcharge_percent=fuel_percent,
            environmental_fee_percent=env_percent,
            admin_fee=admin_fee,
            container_rental_fee=rental_fee
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", unit_count, "high-rise")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        # Verify all fee calculations
        expected_fuel = float(base_rate * Decimal(str(fuel_percent / 100)))
        expected_env = float(base_rate * Decimal(str(env_percent / 100)))
        
        assert abs(analysis['metrics']['fuel_surcharge_amount'] - expected_fuel) < 0.01
        assert abs(analysis['metrics']['environmental_fee_amount'] - expected_env) < 0.01
        assert abs(analysis['metrics']['total_monthly_cost'] - float(expected_total_cost)) < 0.01


class TestKnownContractScenarios:
    """Test analysis of known contract scenarios with pre-calculated results"""
    
    def test_gfl_columbia_square_scenario(self, contract_analyzer, known_contract_scenarios):
        """Test GFL Columbia Square contract scenario"""
        scenario = known_contract_scenarios[0]  # GFL scenario
        
        analysis = contract_analyzer.analyze_contract(scenario['contract'], scenario['property'])
        metrics = analysis['metrics']
        expected = scenario['expected_metrics']
        
        # Verify all key metrics
        assert abs(metrics['monthly_volume_yards'] - expected['monthly_volume_yards']) < 0.01
        assert abs(metrics['base_monthly_cost'] - expected['base_monthly_cost']) < 0.01
        assert abs(metrics['fuel_surcharge_amount'] - expected['fuel_surcharge_amount']) < 0.01
        assert abs(metrics['environmental_fee_amount'] - expected['environmental_fee_amount']) < 0.01
        assert abs(metrics['total_monthly_cost'] - expected['total_monthly_cost']) < 0.01
        assert abs(metrics['cost_per_door'] - expected['cost_per_door']) < 0.01
        assert abs(metrics['cost_per_yard'] - expected['cost_per_yard']) < 0.01
        assert abs(metrics['yards_per_door'] - expected['yards_per_door']) < 0.01
        assert abs(metrics['annual_cost'] - expected['annual_cost']) < 0.01
    
    def test_wm_columbia_square_scenario(self, contract_analyzer, known_contract_scenarios):
        """Test Waste Management Columbia Square contract scenario"""
        scenario = known_contract_scenarios[1]  # WM scenario
        
        analysis = contract_analyzer.analyze_contract(scenario['contract'], scenario['property'])
        metrics = analysis['metrics']
        expected = scenario['expected_metrics']
        
        # Verify all key metrics
        assert abs(metrics['monthly_volume_yards'] - expected['monthly_volume_yards']) < 0.01
        assert abs(metrics['base_monthly_cost'] - expected['base_monthly_cost']) < 0.01
        assert abs(metrics['fuel_surcharge_amount'] - expected['fuel_surcharge_amount']) < 0.01
        assert abs(metrics['environmental_fee_amount'] - expected['environmental_fee_amount']) < 0.01
        assert abs(metrics['total_monthly_cost'] - expected['total_monthly_cost']) < 0.01
        assert abs(metrics['cost_per_door'] - expected['cost_per_door']) < 0.01
        assert abs(metrics['cost_per_yard'] - expected['cost_per_yard']) < 0.01
        assert abs(metrics['yards_per_door'] - expected['yards_per_door']) < 0.01
        assert abs(metrics['annual_cost'] - expected['annual_cost']) < 0.01
    
    def test_high_rise_multiple_container_scenario(self, contract_analyzer, known_contract_scenarios):
        """Test high-rise multiple container scenario"""
        scenario = known_contract_scenarios[2]  # High-rise scenario
        
        analysis = contract_analyzer.analyze_contract(scenario['contract'], scenario['property'])
        metrics = analysis['metrics']
        expected = scenario['expected_metrics']
        
        # Verify all key metrics
        assert abs(metrics['monthly_volume_yards'] - expected['monthly_volume_yards']) < 0.01
        assert abs(metrics['base_monthly_cost'] - expected['base_monthly_cost']) < 0.01
        assert abs(metrics['fuel_surcharge_amount'] - expected['fuel_surcharge_amount']) < 0.01
        assert abs(metrics['environmental_fee_amount'] - expected['environmental_fee_amount']) < 0.01
        assert abs(metrics['total_monthly_cost'] - expected['total_monthly_cost']) < 0.01
        assert abs(metrics['cost_per_door'] - expected['cost_per_door']) < 0.01
        assert abs(metrics['cost_per_yard'] - expected['cost_per_yard']) < 0.01
        assert abs(metrics['yards_per_door'] - expected['yards_per_door']) < 0.01
        assert abs(metrics['annual_cost'] - expected['annual_cost']) < 0.01
    
    def test_scenario_comparison_accuracy(self, contract_analyzer, known_contract_scenarios):
        """Test accuracy of scenario comparisons"""
        gfl_scenario = known_contract_scenarios[0]
        wm_scenario = known_contract_scenarios[1]
        
        # Both scenarios use same property
        assert gfl_scenario['property'].unit_count == wm_scenario['property'].unit_count
        
        gfl_analysis = contract_analyzer.analyze_contract(gfl_scenario['contract'], gfl_scenario['property'])
        wm_analysis = contract_analyzer.analyze_contract(wm_scenario['contract'], wm_scenario['property'])
        
        # Calculate expected savings
        gfl_annual = gfl_analysis['metrics']['annual_cost']
        wm_annual = wm_analysis['metrics']['annual_cost']
        
        expected_annual_savings = wm_annual - gfl_annual
        expected_savings_percent = (expected_annual_savings / wm_annual) * 100
        
        print(f"GFL Annual Cost: ${gfl_annual:,.2f}")
        print(f"WM Annual Cost: ${wm_annual:,.2f}")
        print(f"Annual Savings with GFL: ${expected_annual_savings:,.2f} ({expected_savings_percent:.1f}%)")
        
        # GFL should be less expensive in this scenario
        assert gfl_annual < wm_annual
        assert expected_annual_savings > 7000  # Significant savings


class TestBenchmarkVarianceCalculations:
    """Test benchmark variance calculation accuracy"""
    
    def test_variance_within_range(self, contract_analyzer, financial_validator):
        """Test variance calculation when value is within benchmark range"""
        # Create contract with cost per door within garden-style range ($20-30)
        target_cost_per_door = 25.0  # Middle of range
        unit_count = 200
        base_rate = Decimal(str(target_cost_per_door * unit_count))
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-008",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", unit_count, "garden-style")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        benchmark = analysis['benchmark_analysis']['cost_per_door']
        
        # Should be within range with minimal variance
        assert benchmark['status'] == 'within_range'
        assert abs(benchmark['variance_percent']) < 5.0  # Should be very close to 0
        assert benchmark['benchmark_range'] == (20, 30)
    
    def test_variance_above_range(self, contract_analyzer, financial_validator):
        """Test variance calculation when value is above benchmark range"""
        # Create contract with cost per door above high-rise range ($10-20)
        target_cost_per_door = 30.0  # 50% above upper limit of $20
        unit_count = 400
        base_rate = Decimal(str(target_cost_per_door * unit_count))
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-009",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", unit_count, "high-rise")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        benchmark = analysis['benchmark_analysis']['cost_per_door']
        expected_variance = financial_validator.calculate_benchmark_variance(target_cost_per_door, (10, 20))
        
        # Should be outside range with 50% positive variance
        assert benchmark['status'] == 'outside_range'
        assert abs(benchmark['variance_percent'] - expected_variance) < 1.0
        assert benchmark['variance_percent'] > 45.0  # Should be around 50%
    
    def test_variance_below_range(self, contract_analyzer, financial_validator):
        """Test variance calculation when value is below benchmark range"""
        # Create contract with cost per door below garden-style range ($20-30)
        target_cost_per_door = 15.0  # 25% below lower limit of $20
        unit_count = 250
        base_rate = Decimal(str(target_cost_per_door * unit_count))
        
        contract = ContractTerms(
            vendor_name="Test Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="TEST-010",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", unit_count, "garden-style")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        benchmark = analysis['benchmark_analysis']['cost_per_door']
        expected_variance = financial_validator.calculate_benchmark_variance(target_cost_per_door, (20, 30))
        
        # Should be outside range with negative variance
        assert benchmark['status'] == 'outside_range'
        assert abs(benchmark['variance_percent'] - expected_variance) < 1.0
        assert benchmark['variance_percent'] < -20.0  # Should be around -25%


class TestPotentialSavingsCalculations:
    """Test potential savings calculation accuracy"""
    
    def test_savings_calculation_above_benchmark(self, contract_analyzer):
        """Test potential savings calculation for contract above benchmark"""
        # Create expensive contract (above benchmark)
        unit_count = 300
        expensive_cost_per_door = 35.0  # Above mid-rise range of $15-25
        base_rate = Decimal(str(expensive_cost_per_door * unit_count))
        
        contract = ContractTerms(
            vendor_name="Expensive Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="EXP-001",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", unit_count, "mid-rise")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        # Should generate recommendations with savings estimate
        recommendations = analysis['recommendations']
        savings_recs = [r for r in recommendations if 'potential annual savings' in r.lower() or '$' in r]
        
        assert len(savings_recs) > 0
        
        # Extract savings amount from recommendation
        for rec in savings_recs:
            if '$' in rec and 'savings' in rec.lower():
                # Should estimate meaningful savings
                assert 'potential annual savings' in rec.lower()
    
    def test_no_savings_calculation_within_benchmark(self, contract_analyzer):
        """Test no savings calculation for contract within benchmark"""
        # Create contract within benchmark range
        unit_count = 300
        fair_cost_per_door = 20.0  # Within mid-rise range of $15-25
        base_rate = Decimal(str(fair_cost_per_door * unit_count))
        
        contract = ContractTerms(
            vendor_name="Fair Vendor",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="FAIR-001",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", unit_count, "mid-rise")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        # Should not generate savings recommendations for fair contract
        recommendations = analysis['recommendations']
        savings_recs = [r for r in recommendations if 'potential annual savings' in r.lower()]
        
        # Might have no savings recommendations or minimal savings
        if savings_recs:
            # If savings exist, they should be minimal
            for rec in savings_recs:
                assert 'potential annual savings' in rec.lower()


class TestDecimalPrecisionAndRounding:
    """Test decimal precision and rounding in financial calculations"""
    
    def test_currency_precision(self, contract_analyzer):
        """Test that currency values maintain proper precision"""
        # Use values that could cause floating point precision issues
        base_rate = Decimal("1234.567")  # More than 2 decimal places
        
        contract = ContractTerms(
            vendor_name="Precision Test",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="PREC-001",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=34,
            container_type="compactor",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=base_rate,
            fuel_surcharge_percent=7.333,  # Repeating decimal
            environmental_fee_percent=3.667  # Repeating decimal
        )
        
        property_info = PropertyInfo("Test Property", "123 Test St", 333, "garden-style")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        metrics = analysis['metrics']
        
        # All currency values should have reasonable precision
        assert isinstance(metrics['base_monthly_cost'], (int, float))
        assert isinstance(metrics['total_monthly_cost'], (int, float))
        assert isinstance(metrics['cost_per_door'], (int, float))
        assert isinstance(metrics['annual_cost'], (int, float))
        
        # Values should be reasonable (no overflow/underflow)
        assert metrics['total_monthly_cost'] > metrics['base_monthly_cost']
        assert metrics['cost_per_door'] > 0
        assert metrics['annual_cost'] == metrics['total_monthly_cost'] * 12
    
    def test_percentage_calculation_precision(self, contract_analyzer):
        """Test precision of percentage-based calculations"""
        base_rate = Decimal("1000.00")
        
        # Test various percentage values
        test_percentages = [0.1, 1.0, 5.5, 12.75, 33.333]
        
        for fuel_percent in test_percentages:
            contract = ContractTerms(
                vendor_name="Percentage Test",
                contact_name="Test Contact",
                contact_info="<EMAIL>",
                quote_number=f"PCT-{fuel_percent}",
                contract_length_months=12,
                effective_date=datetime(2024, 1, 1),
                automatic_renewal=True,
                renewal_term_months=12,
                termination_notice_days=30,
                container_size_yards=34,
                container_type="compactor",
                container_quantity=1,
                pickup_frequency_weekly=1,
                base_monthly_rate=base_rate,
                fuel_surcharge_percent=fuel_percent
            )
            
            property_info = PropertyInfo("Test Property", "123 Test St", 200, "garden-style")
            analysis = contract_analyzer.analyze_contract(contract, property_info)
            
            # Calculate expected fuel charge
            expected_fuel_charge = float(base_rate * Decimal(str(fuel_percent / 100)))
            actual_fuel_charge = analysis['metrics']['fuel_surcharge_amount']
            
            # Should be accurate to within 1 cent
            assert abs(actual_fuel_charge - expected_fuel_charge) < 0.01
    
    def test_large_number_calculations(self, contract_analyzer):
        """Test calculations with large property portfolios"""
        # Test with very large property
        unit_count = 5000  # Massive property
        base_rate = Decimal("50000.00")  # High monthly rate
        
        contract = ContractTerms(
            vendor_name="Large Scale Test",
            contact_name="Test Contact",
            contact_info="<EMAIL>",
            quote_number="LARGE-001",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=40,
            container_type="compactor",
            container_quantity=10,
            pickup_frequency_weekly=3,
            base_monthly_rate=base_rate
        )
        
        property_info = PropertyInfo("Massive Complex", "789 Huge Ave", unit_count, "high-rise")
        analysis = contract_analyzer.analyze_contract(contract, property_info)
        
        metrics = analysis['metrics']
        
        # Calculations should remain accurate at scale
        expected_cost_per_door = float(base_rate) / unit_count
        assert abs(metrics['cost_per_door'] - expected_cost_per_door) < 0.01
        
        # Annual cost should be exactly 12x monthly
        assert metrics['annual_cost'] == metrics['total_monthly_cost'] * 12
        
        # Volume calculations should be correct
        expected_monthly_volume = 40 * 10 * 3 * 4.33
        assert abs(metrics['monthly_volume_yards'] - expected_monthly_volume) < 0.01