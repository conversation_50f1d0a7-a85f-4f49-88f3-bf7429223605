"""
Email Service for Advantage Waste Enterprise Notifications
=========================================================

Template-based email notification system with Greystar branding, delivery tracking,
and enterprise-grade reliability for contract renewal communications.

Built by Email Integration Agent - Enterprise Development Force
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import jinja2
import json

from ..models.renewal import (
    Contract, RenewalAlert, RenewalAnalysis, RenewalNotification,
    Property, Vendor, NotificationStatus
)
from ..core.settings import get_settings

logger = logging.getLogger(__name__)

class EmailTemplate(str, Enum):
    """Email template types for different notifications"""
    RENEWAL_REMINDER = "renewal_reminder"
    ANALYSIS_COMPLETE = "analysis_complete"
    EXECUTIVE_ALERT = "executive_alert"
    VENDOR_PERFORMANCE = "vendor_performance"
    EXECUTIVE_SUMMARY = "executive_summary"
    TASK_FAILURE = "task_failure"
    COST_ALERT = "cost_alert"

@dataclass
class EmailRecipient:
    """Email recipient information"""
    email: str
    name: str
    role: str
    user_id: Optional[str] = None

@dataclass
class EmailContext:
    """Email template context data"""
    recipient: EmailRecipient
    contract: Optional[Contract] = None
    analysis: Optional[RenewalAnalysis] = None
    alert: Optional[RenewalAlert] = None
    custom_data: Optional[Dict] = None

class EmailService:
    """
    Enterprise email service with template management, delivery tracking,
    and integration with Greystar notification systems.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.template_env = self._setup_jinja_environment()
        self.smtp_config = self._setup_smtp_configuration()
        
    def _setup_jinja_environment(self) -> jinja2.Environment:
        """Setup Jinja2 environment for email templates"""
        
        template_loader = jinja2.FileSystemLoader('src/templates/emails')
        env = jinja2.Environment(
            loader=template_loader,
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )
        
        # Add custom filters for waste management formatting
        env.filters['currency'] = self._format_currency
        env.filters['percentage'] = self._format_percentage
        env.filters['date_format'] = self._format_date
        env.filters['urgency_color'] = self._get_urgency_color
        
        return env
    
    def _setup_smtp_configuration(self) -> Dict:
        """Setup SMTP configuration for email delivery"""
        
        return {
            'host': self.settings.SMTP_HOST,
            'port': self.settings.SMTP_PORT,
            'username': self.settings.SMTP_USERNAME,
            'password': self.settings.SMTP_PASSWORD,
            'use_tls': self.settings.SMTP_USE_TLS,
            'from_email': self.settings.FROM_EMAIL,
            'from_name': 'Advantage Waste Management System'
        }
    
    async def send_renewal_notification(
        self, 
        recipient: EmailRecipient, 
        contract: Contract, 
        alert: RenewalAlert
    ) -> bool:
        """Send contract renewal notification to property managers"""
        
        try:
            logger.info(f"Sending renewal notification to {recipient.email} for contract {contract.contract_number}")
            
            # Prepare template context
            context = EmailContext(
                recipient=recipient,
                contract=contract,
                alert=alert,
                custom_data={
                    'days_to_expiration': alert.days_before_expiration,
                    'property_name': contract.property.name,
                    'vendor_name': contract.vendor_name,
                    'annual_cost': float(contract.base_monthly_rate * 12),
                    'cost_per_door': float(contract.base_monthly_rate * 12 / contract.property.unit_count),
                    'urgency_level': alert.priority_level,
                    'contract_end_date': contract.contract_end_date.strftime('%B %d, %Y'),
                    'renewal_dashboard_url': f"{self.settings.FRONTEND_URL}/renewals",
                    'contract_detail_url': f"{self.settings.FRONTEND_URL}/renewals/{contract.id}"
                }
            )
            
            # Generate email content
            email_content = self._render_template(EmailTemplate.RENEWAL_REMINDER, context)
            
            # Send email
            success = await self._send_email(
                to_email=recipient.email,
                to_name=recipient.name,
                subject=email_content['subject'],
                html_body=email_content['html'],
                text_body=email_content['text']
            )
            
            # Track notification
            await self._track_notification(
                contract=contract,
                alert=alert,
                recipient=recipient,
                template=EmailTemplate.RENEWAL_REMINDER,
                status=NotificationStatus.SENT if success else NotificationStatus.FAILED
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending renewal notification: {e}", exc_info=True)
            return False
    
    async def send_analysis_completion_notification(
        self,
        recipient: EmailRecipient,
        contract: Contract,
        analysis: RenewalAnalysis
    ) -> bool:
        """Send notification when contract analysis is completed"""
        
        try:
            logger.info(f"Sending analysis completion notification to {recipient.email}")
            
            # Calculate key metrics for email
            savings_opportunity = float(analysis.savings_opportunity or 0)
            savings_percentage = (savings_opportunity / float(analysis.annual_cost)) * 100 if analysis.annual_cost else 0
            
            context = EmailContext(
                recipient=recipient,
                contract=contract,
                analysis=analysis,
                custom_data={
                    'property_name': contract.property.name,
                    'vendor_name': contract.vendor_name,
                    'performance_grade': analysis.current_performance_grade,
                    'recommendation': analysis.primary_recommendation,
                    'savings_opportunity': savings_opportunity,
                    'savings_percentage': savings_percentage,
                    'confidence_score': float(analysis.recommendation_confidence),
                    'analysis_date': analysis.analysis_date.strftime('%B %d, %Y'),
                    'contract_detail_url': f"{self.settings.FRONTEND_URL}/renewals/{contract.id}/analysis",
                    'talking_points': analysis.negotiation_talking_points[:3] if analysis.negotiation_talking_points else []
                }
            )
            
            email_content = self._render_template(EmailTemplate.ANALYSIS_COMPLETE, context)
            
            success = await self._send_email(
                to_email=recipient.email,
                to_name=recipient.name,
                subject=email_content['subject'],
                html_body=email_content['html'],
                text_body=email_content['text']
            )
            
            await self._track_notification(
                contract=contract,
                recipient=recipient,
                template=EmailTemplate.ANALYSIS_COMPLETE,
                status=NotificationStatus.SENT if success else NotificationStatus.FAILED,
                custom_data={'analysis_id': str(analysis.id)}
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending analysis completion notification: {e}", exc_info=True)
            return False
    
    async def send_executive_alert(
        self,
        recipient: EmailRecipient,
        contract: Contract,
        alert_type: str
    ) -> bool:
        """Send executive alerts for critical situations"""
        
        try:
            logger.info(f"Sending executive alert ({alert_type}) to {recipient.email}")
            
            # Determine alert context based on type
            if alert_type == "overdue_contract":
                days_overdue = (datetime.utcnow() - contract.contract_end_date).days
                alert_context = {
                    'alert_title': 'Contract Overdue - Immediate Action Required',
                    'alert_description': f'Contract expired {days_overdue} days ago',
                    'severity': 'CRITICAL',
                    'recommended_action': 'Contact property manager immediately'
                }
            elif alert_type == "high_value_opportunity":
                alert_context = {
                    'alert_title': 'High-Value Savings Opportunity Identified',
                    'alert_description': 'Significant cost reduction potential discovered',
                    'severity': 'HIGH',
                    'recommended_action': 'Review analysis and approve negotiation strategy'
                }
            else:
                alert_context = {
                    'alert_title': 'Contract Renewal Alert',
                    'alert_description': 'Executive attention required',
                    'severity': 'MEDIUM',
                    'recommended_action': 'Review contract details'
                }
            
            context = EmailContext(
                recipient=recipient,
                contract=contract,
                custom_data={
                    'property_name': contract.property.name,
                    'vendor_name': contract.vendor_name,
                    'annual_cost': float(contract.base_monthly_rate * 12),
                    'property_units': contract.property.unit_count,
                    'contract_end_date': contract.contract_end_date.strftime('%B %d, %Y'),
                    'executive_dashboard_url': f"{self.settings.FRONTEND_URL}/executive/dashboard",
                    **alert_context
                }
            )
            
            email_content = self._render_template(EmailTemplate.EXECUTIVE_ALERT, context)
            
            success = await self._send_email(
                to_email=recipient.email,
                to_name=recipient.name,
                subject=email_content['subject'],
                html_body=email_content['html'],
                text_body=email_content['text'],
                priority='high'
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending executive alert: {e}", exc_info=True)
            return False
    
    async def send_vendor_performance_report(
        self,
        vendor_reports: List[Dict],
        analysis_date: datetime
    ) -> bool:
        """Send weekly vendor performance report to stakeholders"""
        
        try:
            logger.info("Sending vendor performance report")
            
            # Prepare summary statistics
            total_vendors = len(vendor_reports)
            total_portfolio_value = sum(report['total_annual_value'] for report in vendor_reports)
            avg_performance_score = sum(report['average_performance_score'] for report in vendor_reports) / total_vendors if total_vendors else 0
            
            # Identify top and bottom performers
            sorted_vendors = sorted(vendor_reports, key=lambda x: x['average_performance_score'], reverse=True)
            top_performers = sorted_vendors[:3]
            bottom_performers = sorted_vendors[-3:] if len(sorted_vendors) > 3 else []
            
            context = EmailContext(
                recipient=EmailRecipient(
                    email="<EMAIL>",
                    name="Waste Management Team",
                    role="team"
                ),
                custom_data={
                    'analysis_date': analysis_date.strftime('%B %d, %Y'),
                    'total_vendors': total_vendors,
                    'total_portfolio_value': total_portfolio_value,
                    'avg_performance_score': avg_performance_score,
                    'top_performers': top_performers,
                    'bottom_performers': bottom_performers,
                    'vendor_reports': vendor_reports,
                    'report_url': f"{self.settings.FRONTEND_URL}/reports/vendor-performance"
                }
            )
            
            email_content = self._render_template(EmailTemplate.VENDOR_PERFORMANCE, context)
            
            # Send to multiple stakeholders
            stakeholders = [
                EmailRecipient("<EMAIL>", "Waste Management Director", "executive"),
                EmailRecipient("<EMAIL>", "Regional Operations", "management"),
                EmailRecipient("<EMAIL>", "Procurement Team", "procurement")
            ]
            
            success_count = 0
            for stakeholder in stakeholders:
                success = await self._send_email(
                    to_email=stakeholder.email,
                    to_name=stakeholder.name,
                    subject=email_content['subject'],
                    html_body=email_content['html'],
                    text_body=email_content['text']
                )
                if success:
                    success_count += 1
            
            return success_count == len(stakeholders)
            
        except Exception as e:
            logger.error(f"Error sending vendor performance report: {e}", exc_info=True)
            return False
    
    async def send_executive_summary(
        self,
        summary_data: Dict,
        report_date: datetime
    ) -> bool:
        """Send monthly executive summary report"""
        
        try:
            logger.info("Sending monthly executive summary")
            
            # Calculate key insights
            contracts_at_risk = summary_data.get('contracts_expiring_90_days', 0)
            portfolio_value = summary_data.get('total_portfolio_value', 0)
            potential_savings = summary_data.get('potential_annual_savings', 0)
            savings_percentage = (potential_savings / portfolio_value) * 100 if portfolio_value else 0
            
            context = EmailContext(
                recipient=EmailRecipient(
                    email="<EMAIL>",
                    name="Executive Team",
                    role="executive"
                ),
                custom_data={
                    'report_date': report_date.strftime('%B %Y'),
                    'total_contracts': summary_data.get('total_contracts', 0),
                    'contracts_at_risk': contracts_at_risk,
                    'portfolio_value': portfolio_value,
                    'potential_savings': potential_savings,
                    'savings_percentage': savings_percentage,
                    'avg_cost_per_door': summary_data.get('average_cost_per_door', 0),
                    'executive_dashboard_url': f"{self.settings.FRONTEND_URL}/executive/dashboard",
                    'detailed_report_url': f"{self.settings.FRONTEND_URL}/reports/executive-summary"
                }
            )
            
            email_content = self._render_template(EmailTemplate.EXECUTIVE_SUMMARY, context)
            
            # Executive distribution list
            executives = [
                EmailRecipient("<EMAIL>", "Chief Executive Officer", "ceo"),
                EmailRecipient("<EMAIL>", "Chief Operating Officer", "coo"),
                EmailRecipient("<EMAIL>", "Waste Management Director", "director"),
                EmailRecipient("<EMAIL>", "Regional Vice President", "vp")
            ]
            
            success_count = 0
            for executive in executives:
                success = await self._send_email(
                    to_email=executive.email,
                    to_name=executive.name,
                    subject=email_content['subject'],
                    html_body=email_content['html'],
                    text_body=email_content['text'],
                    priority='high'
                )
                if success:
                    success_count += 1
            
            return success_count == len(executives)
            
        except Exception as e:
            logger.error(f"Error sending executive summary: {e}", exc_info=True)
            return False
    
    async def send_task_failure_alert(
        self,
        task_name: str,
        task_id: str,
        error: str,
        traceback: str
    ) -> bool:
        """Send alert when background tasks fail"""
        
        try:
            logger.info(f"Sending task failure alert for {task_name}")
            
            context = EmailContext(
                recipient=EmailRecipient(
                    email="<EMAIL>",
                    name="IT Operations Team",
                    role="operations"
                ),
                custom_data={
                    'task_name': task_name,
                    'task_id': task_id,
                    'error_message': error,
                    'error_traceback': traceback,
                    'failure_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC'),
                    'monitoring_url': f"{self.settings.BACKEND_URL}/admin/tasks/{task_id}"
                }
            )
            
            email_content = self._render_template(EmailTemplate.TASK_FAILURE, context)
            
            success = await self._send_email(
                to_email="<EMAIL>",
                to_name="IT Operations Team",
                subject=email_content['subject'],
                html_body=email_content['html'],
                text_body=email_content['text'],
                priority='urgent'
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending task failure alert: {e}", exc_info=True)
            return False
    
    def _render_template(self, template_type: EmailTemplate, context: EmailContext) -> Dict[str, str]:
        """Render email template with context data"""
        
        try:
            # Load template files
            html_template = self.template_env.get_template(f"{template_type.value}.html")
            text_template = self.template_env.get_template(f"{template_type.value}.txt")
            subject_template = self.template_env.get_template(f"{template_type.value}_subject.txt")
            
            # Prepare template variables
            template_vars = {
                'recipient': context.recipient,
                'contract': context.contract,
                'analysis': context.analysis,
                'alert': context.alert,
                'greystar_logo_url': f"{self.settings.FRONTEND_URL}/assets/greystar-logo.png",
                'advantage_waste_logo_url': f"{self.settings.FRONTEND_URL}/assets/advantage-waste-logo.png",
                'current_date': datetime.utcnow().strftime('%B %d, %Y'),
                'support_email': '<EMAIL>',
                'support_phone': '1-800-GREYSTAR',
                **(context.custom_data or {})
            }
            
            # Render templates
            return {
                'subject': subject_template.render(**template_vars).strip(),
                'html': html_template.render(**template_vars),
                'text': text_template.render(**template_vars)
            }
            
        except Exception as e:
            logger.error(f"Error rendering template {template_type.value}: {e}")
            raise
    
    async def _send_email(
        self,
        to_email: str,
        to_name: str,
        subject: str,
        html_body: str,
        text_body: str,
        priority: str = 'normal',
        attachments: Optional[List] = None
    ) -> bool:
        """Send email via SMTP with delivery tracking"""
        
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.smtp_config['from_name']} <{self.smtp_config['from_email']}>"
            msg['To'] = f"{to_name} <{to_email}>"
            
            # Set priority headers
            if priority == 'urgent':
                msg['X-Priority'] = '1 (Highest)'
                msg['X-MSMail-Priority'] = 'High'
                msg['Importance'] = 'High'
            elif priority == 'high':
                msg['X-Priority'] = '2 (High)'
                msg['X-MSMail-Priority'] = 'High'
                msg['Importance'] = 'High'
            
            # Add Greystar branding headers
            msg['X-Mailer'] = 'Advantage Waste Management System'
            msg['Organization'] = 'Greystar Real Estate Partners'
            
            # Attach text and HTML versions
            text_part = MIMEText(text_body, 'plain')
            html_part = MIMEText(html_body, 'html')
            
            msg.attach(text_part)
            msg.attach(html_part)
            
            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)
            
            # Send email
            context = ssl.create_default_context()
            
            if self.smtp_config['use_tls']:
                with smtplib.SMTP(self.smtp_config['host'], self.smtp_config['port']) as server:
                    server.starttls(context=context)
                    server.login(self.smtp_config['username'], self.smtp_config['password'])
                    server.send_message(msg)
            else:
                with smtplib.SMTP_SSL(self.smtp_config['host'], self.smtp_config['port'], context=context) as server:
                    server.login(self.smtp_config['username'], self.smtp_config['password'])
                    server.send_message(msg)
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}", exc_info=True)
            return False
    
    async def _track_notification(
        self,
        contract: Contract,
        recipient: EmailRecipient,
        template: EmailTemplate,
        status: NotificationStatus,
        alert: Optional[RenewalAlert] = None,
        custom_data: Optional[Dict] = None
    ):
        """Track email notification in database for audit purposes"""
        
        try:
            # This would integrate with the database session
            # For now, just log the tracking information
            tracking_info = {
                'contract_id': str(contract.id),
                'recipient_email': recipient.email,
                'recipient_name': recipient.name,
                'template_type': template.value,
                'status': status.value,
                'alert_id': str(alert.id) if alert else None,
                'sent_at': datetime.utcnow().isoformat(),
                'custom_data': custom_data
            }
            
            logger.info(f"Notification tracked: {json.dumps(tracking_info)}")
            
        except Exception as e:
            logger.error(f"Error tracking notification: {e}")
    
    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict):
        """Add file attachment to email message"""
        
        try:
            with open(attachment['file_path'], 'rb') as f:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(f.read())
            
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {attachment["filename"]}'
            )
            
            msg.attach(part)
            
        except Exception as e:
            logger.error(f"Error adding attachment {attachment.get('filename', 'unknown')}: {e}")
    
    # Template filter functions
    def _format_currency(self, value: float) -> str:
        """Format currency values for email templates"""
        if value >= 1000000:
            return f"${value/1000000:.1f}M"
        elif value >= 1000:
            return f"${value/1000:.0f}K"
        else:
            return f"${value:,.2f}"
    
    def _format_percentage(self, value: float) -> str:
        """Format percentage values for email templates"""
        return f"{value:.1f}%"
    
    def _format_date(self, date_value: datetime) -> str:
        """Format dates for email templates"""
        return date_value.strftime('%B %d, %Y')
    
    def _get_urgency_color(self, urgency: str) -> str:
        """Get color code for urgency levels"""
        colors = {
            'critical': '#dc2626',  # Red
            'high': '#ea580c',      # Orange
            'medium': '#ca8a04',    # Yellow
            'low': '#16a34a'        # Green
        }
        return colors.get(urgency, '#6b7280')  # Gray default