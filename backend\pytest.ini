[tool:pytest]
# Pytest configuration for Advantage Waste Enterprise Testing Suite

minversion = 6.0
python_files = test_*.py
python_classes = Test*
python_functions = test_*
testpaths = tests

# Test discovery patterns
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=90
    --durations=10
    --maxfail=5

# Markers for test categorization
markers =
    unit: Unit tests for individual functions and classes
    integration: Integration tests for API endpoints and services
    performance: Performance and load testing
    financial: Financial calculation validation tests
    monitoring: Health check and monitoring system tests
    slow: Tests that take longer than 1 second
    enterprise: Tests that simulate enterprise-scale scenarios
    database: Tests that require database connectivity
    external: Tests that require external services
    smoke: Basic smoke tests for CI/CD

# Performance test configuration
timeout = 300  # 5 minutes timeout for long-running tests

# Asyncio configuration
asyncio_mode = auto

# Test filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:Using or importing the ABCs:DeprecationWarning

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage configuration
[coverage:run]
source = src
omit = 
    */tests/*
    */venv/*
    */venv_linux/*
    */__pycache__/*
    */migrations/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod