# Advantage Waste Enterprise - Feature Request Template

## FEATURE:
[Describe the specific waste management feature you want to build - be detailed about functionality, user workflow, and business requirements]

Example: 
"Contract comparison tool that analyzes multiple waste vendor proposals for a property, calculates cost savings, identifies optimal service configurations, and generates executive summary reports for decision-making."

## EXAMPLES:
[Reference specific files in the examples/ directory that show patterns Claude should follow]

Available examples:
- examples/contract_analysis_example.py - Shows complete contract analysis patterns, industry benchmarks, and calculation methods
- examples/waste_calculation_example.py - Demonstrates waste volume and cost calculations
- examples/property_optimization_example.py - Shows property-specific optimization workflows

Example:
"Follow the ContractAnalyzer class patterns in examples/contract_analysis_example.py for contract analysis logic and industry benchmark comparisons. Use the PropertyInfo structure for property context."

## DOCUMENTATION:
[Include links to relevant APIs, documentation, industry standards, or Greystar-specific resources]

Relevant documentation:
- FastAPI documentation: https://fastapi.tiangolo.com/
- Greystar Waste Reference Binder (attached document)
- Waste industry standards and conversion formulas
- Integration requirements with existing Advantage Waste portal

Example:
"Reference the Contract Data Template from the Greystar Waste Reference Binder for standard contract terms structure. Follow FastAPI patterns for RESTful API endpoints."

## OTHER CONSIDERATIONS:
[Mention specific requirements, gotchas, compliance needs, integration requirements, or technical constraints]

Important considerations:
- Must integrate with existing Greystar property management systems
- Follow Advantage Waste branding and design standards  
- Handle sensitive financial data with proper security
- Support enterprise-scale operations (3,850+ properties)
- Comply with corporate data retention and audit requirements
- Performance requirements for large data sets
- Multi-user role-based access (property managers, directors, executives)

Example:
"Must handle PDF contract parsing with OCR capability. Integration with existing Advantage Waste portal authentication. Performance requirement: analyze contracts for 200+ unit properties within 30 seconds. Support bulk analysis of multiple contracts simultaneously."

---

## Sample Feature Request

## FEATURE:
Automated contract renewal analysis system that monitors contract expiration dates across all Greystar properties, evaluates current contract performance, identifies renewal opportunities, and generates negotiation recommendations 60-90 days before expiration.

## EXAMPLES:
- examples/contract_analysis_example.py - Contract analysis patterns and benchmarking methods
- examples/renewal_workflow_example.py - Renewal timeline and notification patterns  
- examples/negotiation_report_example.py - Report generation for vendor negotiations

## DOCUMENTATION:
- Greystar Waste Reference Binder contract comparison methodology
- Advantage Waste portal API endpoints for property data
- FastAPI documentation for background task scheduling
- Email integration for automated notifications

## OTHER CONSIDERATIONS:
- Must integrate with Greystar's contract management database
- Automated email notifications to property managers and regional directors
- Generate negotiation talking points based on market analysis
- Track renewal success rates and cost savings achieved
- Support custom notification preferences by user role
- Handle contracts with varying terms (12, 24, 36 month cycles)
- Compliance with procurement approval workflows for contract values over $50K
