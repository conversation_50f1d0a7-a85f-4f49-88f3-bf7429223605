"""
Background tasks for Advantage Waste Enterprise.
Contains Celery tasks for automated contract monitoring and business processes.
"""

from .renewal_scanner import scan_expiring_contracts, check_contract_renewal_status
from .analysis_scheduler import schedule_pending_analyses, run_contract_analysis
from .email_notifications import (
    send_email,
    send_renewal_notification,
    process_notification_queue,
    check_escalations,
    retry_failed_notifications
)
from .vendor_performance import analyze_vendor_performance, weekly_performance_analysis
from .executive_reports import generate_monthly_summary, generate_executive_dashboard
from .system_monitoring import health_check, cleanup_old_tasks, monitor_task_performance

__all__ = [
    "scan_expiring_contracts",
    "check_contract_renewal_status", 
    "schedule_pending_analyses",
    "run_contract_analysis",
    "send_email",
    "send_renewal_notification",
    "process_notification_queue",
    "check_escalations",
    "retry_failed_notifications",
    "analyze_vendor_performance",
    "weekly_performance_analysis",
    "generate_monthly_summary",
    "generate_executive_dashboard",
    "health_check",
    "cleanup_old_tasks",
    "monitor_task_performance",
]