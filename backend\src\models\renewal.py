"""
Renewal Management Models for Advantage Waste Enterprise
=======================================================

Database models for contract renewal tracking, analysis, and notification management.
Designed for enterprise scale with 3,850+ Greystar properties.

Built by Database Infrastructure Agent - Enterprise Development Force
"""

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, Numeric, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from datetime import datetime, date
from decimal import Decimal
from typing import Optional, Dict, Any
from enum import Enum
import uuid

Base = declarative_base()

class RenewalStatus(str, Enum):
    """Contract renewal status tracking"""
    PENDING = "pending"
    NOTIFIED = "notified"
    IN_ANALYSIS = "in_analysis"
    REQUIRES_APPROVAL = "requires_approval"
    APPROVED = "approved"
    RENEWED = "renewed"
    TERMINATED = "terminated"
    EXPIRED = "expired"

class AlertType(str, Enum):
    """Renewal alert types with configurable timing"""
    NINETY_DAY = "90_day"
    SIXTY_DAY = "60_day"
    THIRTY_DAY = "30_day"
    FIFTEEN_DAY = "15_day"
    OVERDUE = "overdue"

class RecommendationType(str, Enum):
    """Renewal recommendation categories"""
    RENEW = "renew"
    RENEGOTIATE = "renegotiate"
    SWITCH_VENDOR = "switch_vendor"
    TERMINATE = "terminate"
    REQUIRES_REVIEW = "requires_review"

class NotificationStatus(str, Enum):
    """Notification delivery status tracking"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    BOUNCED = "bounced"

class Contract(Base):
    """
    Extended contract model with renewal tracking capabilities.
    Integrates with existing ContractTerms structure from examples.
    """
    __tablename__ = "contracts"
    
    # Primary identification
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    property_id = Column(UUID(as_uuid=True), ForeignKey("properties.id"), nullable=False)
    vendor_id = Column(UUID(as_uuid=True), ForeignKey("vendors.id"), nullable=False)
    
    # Contract basic information
    contract_number = Column(String(100), unique=True, nullable=False)
    vendor_name = Column(String(255), nullable=False)
    contact_name = Column(String(255))
    contact_email = Column(String(255))
    contact_phone = Column(String(50))
    quote_number = Column(String(100))
    
    # Contract terms and dates
    contract_start_date = Column(DateTime, nullable=False)
    contract_end_date = Column(DateTime, nullable=False)
    contract_length_months = Column(Integer, nullable=False)
    automatic_renewal = Column(Boolean, default=False)
    renewal_term_months = Column(Integer)
    termination_notice_days = Column(Integer, default=30)
    
    # Service configuration
    container_size_yards = Column(Integer, nullable=False)
    container_type = Column(String(50), nullable=False)  # front-load, compactor, roll-off
    container_quantity = Column(Integer, nullable=False)
    pickup_frequency_weekly = Column(Integer, nullable=False)
    
    # Financial terms (using Decimal for precision)
    base_monthly_rate = Column(Numeric(10, 2), nullable=False)
    fuel_surcharge_percent = Column(Numeric(5, 2))
    environmental_fee_percent = Column(Numeric(5, 2))
    admin_fee = Column(Numeric(10, 2))
    container_rental_fee = Column(Numeric(10, 2))
    initial_delivery_charge = Column(Numeric(10, 2))
    extra_pickup_cost = Column(Numeric(10, 2))
    
    # Price increase protection
    cpi_increases = Column(Boolean, default=False)
    cpi_frequency = Column(String(20))  # annually, semi-annually
    cpi_index = Column(String(50))  # CPI-U, regional index
    max_annual_increase_percent = Column(Numeric(5, 2))
    
    # Contract provisions
    early_termination_fee_calculation = Column(Text)
    payment_terms_days = Column(Integer, default=30)
    right_of_first_refusal_days = Column(Integer)
    
    # Renewal tracking fields
    renewal_status = Column(String(20), default=RenewalStatus.PENDING)
    next_renewal_date = Column(DateTime)
    renewal_notice_sent = Column(Boolean, default=False)
    renewal_analysis_completed = Column(Boolean, default=False)
    last_performance_review = Column(DateTime)
    
    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    
    # Relationships
    property = relationship("Property", back_populates="contracts")
    vendor = relationship("Vendor", back_populates="contracts")
    renewal_alerts = relationship("RenewalAlert", back_populates="contract", cascade="all, delete-orphan")
    renewal_analyses = relationship("RenewalAnalysis", back_populates="contract", cascade="all, delete-orphan")
    notifications = relationship("RenewalNotification", back_populates="contract")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_contracts_end_date', 'contract_end_date'),
        Index('idx_contracts_property_vendor', 'property_id', 'vendor_id'),
        Index('idx_contracts_renewal_status', 'renewal_status'),
        Index('idx_contracts_next_renewal', 'next_renewal_date'),
    )

class RenewalAlert(Base):
    """
    Tracks renewal alerts and notifications for contract expiration management.
    Supports configurable alert timing and escalation workflows.
    """
    __tablename__ = "renewal_alerts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    contract_id = Column(UUID(as_uuid=True), ForeignKey("contracts.id"), nullable=False)
    
    # Alert configuration
    alert_type = Column(String(20), nullable=False)  # AlertType enum
    alert_date = Column(DateTime, nullable=False)
    days_before_expiration = Column(Integer, nullable=False)
    
    # Notification tracking
    notification_sent = Column(Boolean, default=False)
    notification_sent_at = Column(DateTime)
    response_received = Column(Boolean, default=False)
    response_received_at = Column(DateTime)
    response_action = Column(String(50))  # renew, renegotiate, terminate, etc.
    
    # Escalation management
    escalation_level = Column(Integer, default=0)
    escalated_to = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    escalation_date = Column(DateTime)
    
    # Additional context
    priority_level = Column(String(20), default="medium")  # low, medium, high, critical
    notes = Column(Text)
    
    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    contract = relationship("Contract", back_populates="renewal_alerts")
    escalated_user = relationship("User", foreign_keys=[escalated_to])
    notifications = relationship("RenewalNotification", back_populates="renewal_alert")
    
    # Indexes for efficient querying
    __table_args__ = (
        Index('idx_renewal_alerts_contract_date', 'contract_id', 'alert_date'),
        Index('idx_renewal_alerts_type_sent', 'alert_type', 'notification_sent'),
        Index('idx_renewal_alerts_escalation', 'escalation_level', 'escalation_date'),
    )

class RenewalAnalysis(Base):
    """
    Stores comprehensive contract renewal analysis results including 
    benchmark comparisons, cost analysis, and recommendations.
    """
    __tablename__ = "renewal_analyses"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    contract_id = Column(UUID(as_uuid=True), ForeignKey("contracts.id"), nullable=False)
    
    # Analysis metadata
    analysis_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    analyst_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    analysis_version = Column(Integer, default=1)
    
    # Performance evaluation
    current_performance_grade = Column(String(5))  # A+, A, B, C, D
    performance_score = Column(Numeric(5, 2))  # 0-100 score
    cost_per_door = Column(Numeric(10, 2))
    cost_per_yard = Column(Numeric(10, 2))
    yards_per_door = Column(Numeric(8, 2))
    monthly_volume_yards = Column(Numeric(10, 2))
    annual_cost = Column(Numeric(12, 2))
    
    # Benchmark comparison results
    benchmark_comparison = Column(JSONB)  # Detailed benchmark analysis
    cost_variance_percent = Column(Numeric(8, 2))  # +/- vs benchmark
    volume_variance_percent = Column(Numeric(8, 2))
    terms_quality_score = Column(Numeric(5, 2))
    
    # Savings opportunities
    savings_opportunity = Column(Numeric(12, 2))
    savings_confidence = Column(Numeric(5, 2))  # 0-100% confidence
    optimization_areas = Column(JSONB)  # Areas for improvement
    
    # Recommendations
    primary_recommendation = Column(String(50))  # RecommendationType enum
    recommendation_confidence = Column(Numeric(5, 2))
    recommendation_rationale = Column(Text)
    negotiation_talking_points = Column(JSONB)
    
    # Alternative vendor analysis
    alternative_vendors = Column(JSONB)  # Vendor comparison data
    switch_cost_estimate = Column(Numeric(12, 2))
    switch_risk_level = Column(String(20))  # low, medium, high
    
    # Market context
    market_pricing_trend = Column(String(20))  # increasing, stable, decreasing
    competitive_position = Column(String(20))  # favorable, average, unfavorable
    renewal_urgency = Column(String(20))  # low, medium, high, critical
    
    # Financial projections
    projected_annual_cost_current = Column(Numeric(12, 2))
    projected_annual_cost_optimized = Column(Numeric(12, 2))
    three_year_savings_estimate = Column(Numeric(12, 2))
    roi_analysis = Column(JSONB)
    
    # Quality metrics
    analysis_completeness = Column(Numeric(5, 2))  # 0-100% complete
    data_quality_score = Column(Numeric(5, 2))  # 0-100% data quality
    
    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    contract = relationship("Contract", back_populates="renewal_analyses")
    analyst = relationship("User", foreign_keys=[analyst_id])
    
    # Indexes for analysis queries
    __table_args__ = (
        Index('idx_renewal_analyses_contract_date', 'contract_id', 'analysis_date'),
        Index('idx_renewal_analyses_grade_score', 'current_performance_grade', 'performance_score'),
        Index('idx_renewal_analyses_recommendation', 'primary_recommendation'),
        Index('idx_renewal_analyses_savings', 'savings_opportunity'),
    )

class RenewalNotification(Base):
    """
    Tracks all notifications sent for contract renewals including 
    email delivery status, escalations, and response tracking.
    """
    __tablename__ = "renewal_notifications"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    contract_id = Column(UUID(as_uuid=True), ForeignKey("contracts.id"), nullable=False)
    renewal_alert_id = Column(UUID(as_uuid=True), ForeignKey("renewal_alerts.id"))
    
    # Notification details
    notification_type = Column(String(50), nullable=False)  # renewal_reminder, analysis_complete, etc.
    recipient_email = Column(String(255), nullable=False)
    recipient_name = Column(String(255))
    recipient_role = Column(String(50))  # property_manager, regional_director, etc.
    
    # Message content
    subject = Column(String(500))
    message_template = Column(String(100))
    message_variables = Column(JSONB)  # Template variables
    
    # Delivery tracking
    status = Column(String(20), default=NotificationStatus.PENDING)
    sent_at = Column(DateTime)
    delivered_at = Column(DateTime)
    opened_at = Column(DateTime)
    clicked_at = Column(DateTime)
    
    # External system tracking
    email_provider_id = Column(String(255))  # SendGrid, SES, etc. message ID
    bounce_reason = Column(Text)
    failure_reason = Column(Text)
    retry_count = Column(Integer, default=0)
    last_retry_at = Column(DateTime)
    
    # Response tracking
    response_received = Column(Boolean, default=False)
    response_date = Column(DateTime)
    response_action = Column(String(50))
    response_notes = Column(Text)
    
    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    contract = relationship("Contract", back_populates="notifications")
    renewal_alert = relationship("RenewalAlert", back_populates="notifications")
    
    # Indexes for notification queries
    __table_args__ = (
        Index('idx_notifications_contract_type', 'contract_id', 'notification_type'),
        Index('idx_notifications_status_sent', 'status', 'sent_at'),
        Index('idx_notifications_recipient', 'recipient_email'),
        Index('idx_notifications_retry', 'retry_count', 'last_retry_at'),
    )

class NotificationPreference(Base):
    """
    User-specific notification preferences for contract renewal alerts.
    Supports role-based defaults with individual customization.
    """
    __tablename__ = "notification_preferences"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, unique=True)
    
    # Alert timing preferences
    renewal_90_day = Column(Boolean, default=True)
    renewal_60_day = Column(Boolean, default=True)
    renewal_30_day = Column(Boolean, default=True)
    renewal_15_day = Column(Boolean, default=True)
    overdue_alerts = Column(Boolean, default=True)
    
    # Report preferences
    weekly_summary = Column(Boolean, default=False)
    monthly_executive_summary = Column(Boolean, default=False)
    quarterly_portfolio_review = Column(Boolean, default=False)
    
    # Cost analysis alerts
    cost_increase_threshold = Column(Numeric(5, 2), default=10.0)  # Alert on >10% increases
    savings_opportunity_threshold = Column(Numeric(10, 2), default=5000.0)  # Alert on $5K+ savings
    
    # Delivery preferences
    email_primary = Column(String(255))
    email_secondary = Column(String(255))
    sms_number = Column(String(20))
    sms_enabled = Column(Boolean, default=False)
    
    # Timing preferences
    preferred_time_zone = Column(String(50), default="America/New_York")
    preferred_delivery_hour = Column(Integer, default=9)  # 9 AM
    weekend_delivery = Column(Boolean, default=False)
    
    # Escalation preferences
    escalation_enabled = Column(Boolean, default=True)
    escalation_delay_hours = Column(Integer, default=48)
    escalation_recipient = Column(String(255))
    
    # Audit fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="notification_preferences")
    
    # Indexes
    __table_args__ = (
        Index('idx_notification_preferences_user', 'user_id'),
    )

# Supporting models for complete system

class Property(Base):
    """Property information for contract context"""
    __tablename__ = "properties"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    address = Column(String(500))
    city = Column(String(100))
    state = Column(String(50))
    zip_code = Column(String(20))
    unit_count = Column(Integer, nullable=False)
    property_type = Column(String(50), nullable=False)  # garden-style, mid-rise, high-rise
    
    # Relationships
    contracts = relationship("Contract", back_populates="property")

class Vendor(Base):
    """Vendor information for contract management"""
    __tablename__ = "vendors"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    contact_email = Column(String(255))
    contact_phone = Column(String(50))
    service_areas = Column(JSONB)  # Geographic coverage
    
    # Relationships
    contracts = relationship("Contract", back_populates="vendor")

class User(Base):
    """User information for authentication and preferences"""
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False)
    name = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False)  # property_manager, regional_director, executive
    
    # Relationships
    notification_preferences = relationship("NotificationPreference", back_populates="user", uselist=False)