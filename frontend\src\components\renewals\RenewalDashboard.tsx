/**
 * Renewal Dashboard Component for Advantage Waste Enterprise
 * =========================================================
 * 
 * Main renewal monitoring interface for property managers and executives.
 * Provides filterable property list, expiration timeline, and priority-based views.
 * 
 * Built by Frontend Development Agent - Enterprise Development Force
 */

import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Search, 
  Filter, 
  AlertTriangle, 
  Clock, 
  DollarSign, 
  TrendingUp,
  Calendar,
  Building,
  Users,
  Settings
} from 'lucide-react';
import { format, differenceInDays } from 'date-fns';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

// Types
interface Contract {
  id: string;
  contractNumber: string;
  vendorName: string;
  propertyName: string;
  propertyType: 'garden-style' | 'mid-rise' | 'high-rise';
  unitCount: number;
  contractEndDate: string;
  renewalStatus: 'pending' | 'notified' | 'in_analysis' | 'requires_approval' | 'approved';
  costPerDoor: number;
  annualCost: number;
  lastAnalysisGrade?: string;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  savingsOpportunity?: number;
  daysToExpiration: number;
}

interface DashboardStats {
  totalContracts: number;
  expiringNext30Days: number;
  expiringNext90Days: number;
  totalAnnualValue: number;
  averageCostPerDoor: number;
  potentialSavings: number;
}

interface FilterState {
  search: string;
  urgency: string[];
  propertyType: string[];
  renewalStatus: string[];
  expirationRange: string;
}

// API hooks
const useRenewalDashboard = () => {
  return useQuery({
    queryKey: ['renewals', 'dashboard'],
    queryFn: async (): Promise<{ contracts: Contract[]; stats: DashboardStats }> => {
      const response = await fetch('/api/renewals/dashboard');
      if (!response.ok) throw new Error('Failed to fetch renewal dashboard');
      return response.json();
    },
    refetchInterval: 300000, // Refresh every 5 minutes
  });
};

const RenewalDashboard: React.FC = () => {
  const { data, isLoading, error } = useRenewalDashboard();
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    urgency: [],
    propertyType: [],
    renewalStatus: [],
    expirationRange: 'all'
  });
  const [sortBy, setSortBy] = useState<'expiration' | 'savings' | 'cost'>('expiration');
  const [viewMode, setViewMode] = useState<'table' | 'cards' | 'timeline'>('table');

  // Filter and sort contracts
  const filteredContracts = useMemo(() => {
    if (!data?.contracts) return [];

    let filtered = data.contracts.filter(contract => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        if (!contract.propertyName.toLowerCase().includes(searchLower) &&
            !contract.vendorName.toLowerCase().includes(searchLower) &&
            !contract.contractNumber.toLowerCase().includes(searchLower)) {
          return false;
        }
      }

      // Urgency filter
      if (filters.urgency.length > 0 && !filters.urgency.includes(contract.urgencyLevel)) {
        return false;
      }

      // Property type filter
      if (filters.propertyType.length > 0 && !filters.propertyType.includes(contract.propertyType)) {
        return false;
      }

      // Renewal status filter
      if (filters.renewalStatus.length > 0 && !filters.renewalStatus.includes(contract.renewalStatus)) {
        return false;
      }

      // Expiration range filter
      if (filters.expirationRange !== 'all') {
        const days = contract.daysToExpiration;
        switch (filters.expirationRange) {
          case '30': return days <= 30;
          case '60': return days <= 60;
          case '90': return days <= 90;
          case 'overdue': return days < 0;
          default: return true;
        }
      }

      return true;
    });

    // Sort contracts
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'expiration':
          return a.daysToExpiration - b.daysToExpiration;
        case 'savings':
          return (b.savingsOpportunity || 0) - (a.savingsOpportunity || 0);
        case 'cost':
          return b.annualCost - a.annualCost;
        default:
          return 0;
      }
    });

    return filtered;
  }, [data?.contracts, filters, sortBy]);

  // Dashboard statistics
  const dashboardStats = useMemo(() => {
    if (!data?.stats) return null;
    
    const urgencyDistribution = filteredContracts.reduce((acc, contract) => {
      acc[contract.urgencyLevel] = (acc[contract.urgencyLevel] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const propertyTypeDistribution = filteredContracts.reduce((acc, contract) => {
      acc[contract.propertyType] = (acc[contract.propertyType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      ...data.stats,
      urgencyDistribution,
      propertyTypeDistribution,
      filteredCount: filteredContracts.length
    };
  }, [data?.stats, filteredContracts]);

  // Urgency color mapping
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'bg-red-500 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-black';
      case 'low': return 'bg-green-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getUrgencyBorder = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'border-red-500';
      case 'high': return 'border-orange-500';
      case 'medium': return 'border-yellow-500';
      case 'low': return 'border-green-500';
      default: return 'border-gray-500';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <AlertTriangle className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading renewal dashboard</h3>
            <p className="mt-1 text-sm text-red-700">Please try refreshing the page or contact support.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Contract Renewals</h1>
          <p className="text-gray-600">Monitor upcoming contract expirations and renewal opportunities</p>
        </div>
        <div className="flex space-x-3">
          <select
            value={viewMode}
            onChange={(e) => setViewMode(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="table">Table View</option>
            <option value="cards">Card View</option>
            <option value="timeline">Timeline View</option>
          </select>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
            <Settings className="h-4 w-4 inline mr-2" />
            Settings
          </button>
        </div>
      </div>

      {/* Key Statistics */}
      {dashboardStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardStats.expiringNext90Days}</p>
                <p className="text-xs text-gray-500">Next 90 days</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Annual Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${(dashboardStats.totalAnnualValue / 1000000).toFixed(1)}M
                </p>
                <p className="text-xs text-gray-500">{dashboardStats.totalContracts} contracts</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Savings Opportunity</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${(dashboardStats.potentialSavings / 1000).toFixed(0)}K
                </p>
                <p className="text-xs text-gray-500">Identified opportunities</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <Building className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Cost/Door</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${dashboardStats.averageCostPerDoor.toFixed(0)}
                </p>
                <p className="text-xs text-gray-500">Monthly average</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      <div className="bg-white p-6 rounded-lg shadow border">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search properties, vendors..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-sm"
            />
          </div>

          {/* Urgency Filter */}
          <select
            multiple
            value={filters.urgency}
            onChange={(e) => setFilters({ 
              ...filters, 
              urgency: Array.from(e.target.selectedOptions, option => option.value)
            })}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="critical">Critical</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>

          {/* Property Type Filter */}
          <select
            multiple
            value={filters.propertyType}
            onChange={(e) => setFilters({ 
              ...filters, 
              propertyType: Array.from(e.target.selectedOptions, option => option.value)
            })}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="garden-style">Garden Style</option>
            <option value="mid-rise">Mid-Rise</option>
            <option value="high-rise">High-Rise</option>
          </select>

          {/* Expiration Range */}
          <select
            value={filters.expirationRange}
            onChange={(e) => setFilters({ ...filters, expirationRange: e.target.value })}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Contracts</option>
            <option value="overdue">Overdue</option>
            <option value="30">Next 30 Days</option>
            <option value="60">Next 60 Days</option>
            <option value="90">Next 90 Days</option>
          </select>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="expiration">Sort by Expiration</option>
            <option value="savings">Sort by Savings</option>
            <option value="cost">Sort by Cost</option>
          </select>
        </div>
      </div>

      {/* Results Count */}
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-600">
          Showing {filteredContracts.length} of {data?.contracts?.length || 0} contracts
        </p>
        <div className="flex space-x-2">
          {filters.urgency.map(urgency => (
            <span
              key={urgency}
              className={`px-2 py-1 rounded text-xs font-medium ${getUrgencyColor(urgency)}`}
            >
              {urgency}
            </span>
          ))}
        </div>
      </div>

      {/* Contract List */}
      {viewMode === 'table' && (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Property
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vendor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expiration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Urgency
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Annual Cost
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Savings Opp.
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredContracts.map((contract) => (
                <tr 
                  key={contract.id} 
                  className={`hover:bg-gray-50 border-l-4 ${getUrgencyBorder(contract.urgencyLevel)}`}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{contract.propertyName}</div>
                      <div className="text-sm text-gray-500">
                        {contract.propertyType} • {contract.unitCount} units
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{contract.vendorName}</div>
                    <div className="text-sm text-gray-500">{contract.contractNumber}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {format(new Date(contract.contractEndDate), 'MMM dd, yyyy')}
                    </div>
                    <div className="text-sm text-gray-500">
                      {contract.daysToExpiration > 0 
                        ? `${contract.daysToExpiration} days remaining`
                        : `${Math.abs(contract.daysToExpiration)} days overdue`
                      }
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getUrgencyColor(contract.urgencyLevel)}`}>
                      {contract.urgencyLevel}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">${contract.annualCost.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">${contract.costPerDoor}/door/mo</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {contract.savingsOpportunity ? (
                      <div className="text-sm text-green-600 font-medium">
                        ${contract.savingsOpportunity.toLocaleString()}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500">TBD</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      Analyze
                    </button>
                    <button className="text-gray-600 hover:text-gray-900">
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Card View */}
      {viewMode === 'cards' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredContracts.map((contract) => (
            <div 
              key={contract.id} 
              className={`bg-white rounded-lg shadow border-l-4 ${getUrgencyBorder(contract.urgencyLevel)} p-6`}
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{contract.propertyName}</h3>
                  <p className="text-sm text-gray-600">{contract.vendorName}</p>
                </div>
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getUrgencyColor(contract.urgencyLevel)}`}>
                  {contract.urgencyLevel}
                </span>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Expires:</span>
                  <span className="text-sm font-medium">
                    {format(new Date(contract.contractEndDate), 'MMM dd, yyyy')}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Annual Cost:</span>
                  <span className="text-sm font-medium">${contract.annualCost.toLocaleString()}</span>
                </div>
                
                {contract.savingsOpportunity && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Savings Opp:</span>
                    <span className="text-sm font-medium text-green-600">
                      ${contract.savingsOpportunity.toLocaleString()}
                    </span>
                  </div>
                )}
              </div>
              
              <div className="mt-4 flex space-x-2">
                <button className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-blue-700">
                  Analyze
                </button>
                <button className="flex-1 border border-gray-300 text-gray-700 px-3 py-2 rounded text-sm font-medium hover:bg-gray-50">
                  Details
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {filteredContracts.length === 0 && (
        <div className="text-center py-12">
          <Clock className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No contracts found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your filters or search criteria.
          </p>
        </div>
      )}
    </div>
  );
};

export default RenewalDashboard;