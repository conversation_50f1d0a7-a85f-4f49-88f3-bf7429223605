"""
Notification-related database models for Advantage Waste Enterprise.
Handles email templates, notification preferences, and delivery tracking.
"""

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime, Date,
    JSON, Index, ForeignKey, Enum
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from datetime import datetime, date
from typing import Optional, Dict, Any, List

from ..core.database import Base
from .base import TimestampMixin


class NotificationType(PyEnum):
    """Notification type enumeration"""
    CONTRACT_RENEWAL = "contract_renewal"
    ANALYSIS_COMPLETE = "analysis_complete"
    ESCALATION = "escalation"
    EXECUTIVE_SUMMARY = "executive_summary"
    SYSTEM_ALERT = "system_alert"
    VENDOR_PERFORMANCE = "vendor_performance"
    COST_ALERT = "cost_alert"


class NotificationPriority(PyEnum):
    """Notification priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class DeliveryStatus(PyEnum):
    """Email delivery status"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    BOUNCED = "bounced"
    SPAM = "spam"


class NotificationTemplate(Base, TimestampMixin):
    """
    Email notification template model.
    Stores Jinja2 templates for various notification types.
    """
    __tablename__ = "notification_templates"

    id = Column(Integer, primary_key=True, index=True)
    
    # Template identification
    template_name = Column(String(100), unique=True, nullable=False, index=True)
    template_type = Column(Enum(NotificationType), nullable=False, index=True)
    version = Column(String(10), default="1.0", nullable=False)
    
    # Template content
    subject_template = Column(Text, nullable=False)
    html_template = Column(Text, nullable=False)
    text_template = Column(Text, nullable=True)
    
    # Template metadata
    description = Column(Text, nullable=True)
    variables = Column(JSON, nullable=True)  # Available template variables
    required_variables = Column(JSON, nullable=True)  # Required variables
    
    # Branding and styling
    use_greystar_branding = Column(Boolean, default=True)
    custom_css = Column(Text, nullable=True)
    header_image_url = Column(String(500), nullable=True)
    footer_template = Column(Text, nullable=True)
    
    # Template status
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False)
    approval_required = Column(Boolean, default=False)
    approved_by = Column(String(100), nullable=True)
    approved_date = Column(DateTime, nullable=True)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime, nullable=True)
    
    # Testing and preview
    test_data = Column(JSON, nullable=True)  # Sample data for testing
    preview_html = Column(Text, nullable=True)  # Rendered preview
    
    # Relationships
    notification_logs = relationship("NotificationLog", back_populates="template")
    
    # Indexes
    __table_args__ = (
        Index("idx_template_type_active", "template_type", "is_active"),
        Index("idx_template_default", "is_default"),
    )
    
    def render_subject(self, context: Dict[str, Any]) -> str:
        """Render subject template with context variables"""
        from jinja2 import Template
        template = Template(self.subject_template)
        return template.render(**context)
    
    def render_html(self, context: Dict[str, Any]) -> str:
        """Render HTML template with context variables"""
        from jinja2 import Template
        template = Template(self.html_template)
        return template.render(**context)
    
    def render_text(self, context: Dict[str, Any]) -> str:
        """Render text template with context variables"""
        if not self.text_template:
            return ""
        from jinja2 import Template
        template = Template(self.text_template)
        return template.render(**context)


class NotificationPreference(Base, TimestampMixin):
    """
    User notification preferences model.
    Manages how users want to receive different types of notifications.
    """
    __tablename__ = "notification_preferences"

    id = Column(Integer, primary_key=True, index=True)
    
    # User identification
    user_email = Column(String(200), nullable=False, index=True)
    user_name = Column(String(100), nullable=True)
    user_role = Column(String(50), nullable=True)  # property_manager, director, admin
    
    # Preference settings by notification type
    contract_renewal_enabled = Column(Boolean, default=True)
    analysis_complete_enabled = Column(Boolean, default=True)
    escalation_enabled = Column(Boolean, default=True)
    executive_summary_enabled = Column(Boolean, default=False)
    system_alert_enabled = Column(Boolean, default=True)
    vendor_performance_enabled = Column(Boolean, default=False)
    cost_alert_enabled = Column(Boolean, default=True)
    
    # Delivery preferences
    email_enabled = Column(Boolean, default=True)
    sms_enabled = Column(Boolean, default=False)
    sms_number = Column(String(20), nullable=True)
    
    # Timing preferences
    digest_frequency = Column(String(20), default="daily")  # immediate, daily, weekly
    quiet_hours_start = Column(String(5), nullable=True)  # HH:MM format
    quiet_hours_end = Column(String(5), nullable=True)
    timezone = Column(String(50), default="America/New_York")
    
    # Property and vendor filters
    property_filters = Column(JSON, nullable=True)  # Array of property IDs
    vendor_filters = Column(JSON, nullable=True)  # Array of vendor IDs
    cost_threshold = Column(Integer, nullable=True)  # Minimum cost for alerts
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    verified_email = Column(Boolean, default=False)
    last_login = Column(DateTime, nullable=True)
    
    # Indexes
    __table_args__ = (
        Index("idx_preference_user_active", "user_email", "is_active"),
        Index("idx_preference_role", "user_role"),
    )


class NotificationLog(Base, TimestampMixin):
    """
    Notification delivery log model.
    Tracks all sent notifications for audit and monitoring.
    """
    __tablename__ = "notification_logs"

    id = Column(Integer, primary_key=True, index=True)
    
    # Notification details
    template_id = Column(Integer, ForeignKey("notification_templates.id"), nullable=True)
    notification_type = Column(Enum(NotificationType), nullable=False, index=True)
    priority = Column(Enum(NotificationPriority), default=NotificationPriority.MEDIUM)
    
    # Recipient information
    recipient_email = Column(String(200), nullable=False, index=True)
    recipient_name = Column(String(100), nullable=True)
    cc_emails = Column(JSON, nullable=True)  # Array of CC emails
    bcc_emails = Column(JSON, nullable=True)  # Array of BCC emails
    
    # Message content
    subject = Column(Text, nullable=False)
    html_content = Column(Text, nullable=True)
    text_content = Column(Text, nullable=True)
    
    # Context and trigger information
    context_data = Column(JSON, nullable=True)  # Template context variables
    trigger_entity_type = Column(String(50), nullable=True)  # contract, property, vendor
    trigger_entity_id = Column(String(50), nullable=True)
    
    # Delivery tracking
    status = Column(Enum(DeliveryStatus), default=DeliveryStatus.PENDING, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    failed_at = Column(DateTime, nullable=True)
    failure_reason = Column(Text, nullable=True)
    
    # Email service provider details
    message_id = Column(String(200), nullable=True)  # ESP message ID
    esp_response = Column(JSON, nullable=True)  # Full ESP response
    bounce_type = Column(String(50), nullable=True)  # hard, soft, complaint
    
    # Retry tracking
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    next_retry_at = Column(DateTime, nullable=True)
    
    # Analytics
    opened_at = Column(DateTime, nullable=True)
    clicked_at = Column(DateTime, nullable=True)
    unsubscribed_at = Column(DateTime, nullable=True)
    
    # Relationships
    template = relationship("NotificationTemplate", back_populates="notification_logs")
    
    # Indexes for performance
    __table_args__ = (
        Index("idx_notification_recipient_date", "recipient_email", "created_at"),
        Index("idx_notification_status", "status"),
        Index("idx_notification_type_date", "notification_type", "created_at"),
        Index("idx_notification_retry", "status", "next_retry_at"),
    )
    
    @property
    def delivery_time(self) -> Optional[float]:
        """Calculate delivery time in seconds"""
        if self.sent_at and self.delivered_at:
            return (self.delivered_at - self.sent_at).total_seconds()
        return None
    
    @property
    def is_delivered(self) -> bool:
        """Check if notification was successfully delivered"""
        return self.status == DeliveryStatus.DELIVERED
    
    @property
    def requires_retry(self) -> bool:
        """Check if notification should be retried"""
        return (
            self.status == DeliveryStatus.FAILED and
            self.retry_count < self.max_retries and
            self.next_retry_at and
            self.next_retry_at <= datetime.utcnow()
        )