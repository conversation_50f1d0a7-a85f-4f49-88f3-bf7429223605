"""
Waste Management Industry Calculation Utilities
==============================================

Core calculation functions for waste management industry formulas based on
Greystar's Advantage Waste methodology and industry best practices.

All financial calculations use Decimal precision for accuracy.
"""

from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, Tuple, Optional, Union
from dataclasses import dataclass
import math


@dataclass
class VolumeMetrics:
    """Container volume and capacity metrics"""
    monthly_volume_yards: Decimal
    weekly_volume_yards: Decimal
    theoretical_max_volume: Decimal
    utilization_rate: Decimal
    pickup_frequency_weekly: int
    container_size_yards: int
    container_quantity: int


@dataclass
class CostMetrics:
    """Cost analysis metrics"""
    base_monthly_cost: Decimal
    total_monthly_cost: Decimal
    annual_cost: Decimal
    cost_per_yard: Decimal
    cost_per_door: Decimal
    cost_per_pickup: Decimal
    fees_amount: Decimal
    fees_percentage: Decimal


@dataclass
class EfficiencyMetrics:
    """Service efficiency metrics"""
    yards_per_door: Decimal
    cost_efficiency_score: int
    container_utilization: Decimal
    service_density: Decimal
    waste_generation_rate: Decimal


class WasteCalculations:
    """
    Industry-standard waste management calculations following Greystar methodology.
    
    Key formulas implemented:
    - Monthly volume: Container Size × Quantity × Pickups/Week × 4.33
    - Cost per yard: Total Monthly Cost ÷ Total Monthly Volume
    - Cost per door: Total Monthly Cost ÷ Number of Units
    - Compacted volume: (Tons × 2000) ÷ (Density × Compaction Ratio)
    """
    
    # Industry constants
    WEEKS_PER_MONTH = Decimal("4.33")  # Standard industry calculation
    POUNDS_PER_TON = 2000
    
    # Waste density factors (lbs per cubic yard)
    WASTE_DENSITY = {
        "trash_loose": 225,
        "trash_compacted": 750,  # 3:1 compaction ratio
        "mixed_recycling": 100,
        "cardboard": 100,
        "food_waste": 450,
        "paper": 150,
        "plastic": 65,
        "glass": 400,
        "metal": 165
    }
    
    # Standard compaction ratios by container type
    COMPACTION_RATIOS = {
        "front_load": Decimal("1.0"),  # No compaction
        "compactor": Decimal("3.5"),   # Average 3.5:1
        "self_contained": Decimal("4.0"),  # Higher compaction
        "roll_off": Decimal("1.0")     # No compaction
    }
    
    # Container efficiency factors
    CONTAINER_EFFICIENCY = {
        "front_load": Decimal("0.85"),  # 85% typical fill
        "compactor": Decimal("0.90"),   # 90% typical fill
        "roll_off": Decimal("0.80")     # 80% typical fill
    }

    @staticmethod
    def calculate_monthly_volume(
        container_size_yards: Union[int, Decimal],
        container_quantity: Union[int, Decimal],
        pickup_frequency_weekly: Union[int, Decimal],
        efficiency_factor: Optional[Decimal] = None
    ) -> Decimal:
        """
        Calculate monthly waste volume using industry standard formula.
        
        Formula: Container Size × Quantity × Pickups/Week × 4.33 weeks/month
        
        Args:
            container_size_yards: Size of containers in cubic yards
            container_quantity: Number of containers
            pickup_frequency_weekly: Pickups per week
            efficiency_factor: Optional efficiency factor (default 0.85)
            
        Returns:
            Monthly volume in cubic yards
        """
        size = Decimal(str(container_size_yards))
        quantity = Decimal(str(container_quantity))
        frequency = Decimal(str(pickup_frequency_weekly))
        efficiency = efficiency_factor or Decimal("0.85")
        
        volume = size * quantity * frequency * WasteCalculations.WEEKS_PER_MONTH * efficiency
        return volume.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_cost_per_yard(
        total_monthly_cost: Union[Decimal, float],
        monthly_volume_yards: Union[Decimal, float]
    ) -> Decimal:
        """
        Calculate cost per cubic yard.
        
        Formula: Total Monthly Cost ÷ Total Monthly Volume
        
        Args:
            total_monthly_cost: Total monthly service cost
            monthly_volume_yards: Monthly volume in cubic yards
            
        Returns:
            Cost per cubic yard
        """
        cost = Decimal(str(total_monthly_cost))
        volume = Decimal(str(monthly_volume_yards))
        
        if volume == 0:
            return Decimal("0")
            
        cost_per_yard = cost / volume
        return cost_per_yard.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_cost_per_door(
        total_monthly_cost: Union[Decimal, float],
        unit_count: int
    ) -> Decimal:
        """
        Calculate cost per door (unit).
        
        Formula: Total Monthly Cost ÷ Number of Units
        
        Args:
            total_monthly_cost: Total monthly service cost
            unit_count: Number of residential units
            
        Returns:
            Cost per door (unit)
        """
        cost = Decimal(str(total_monthly_cost))
        units = Decimal(str(unit_count))
        
        if units == 0:
            return Decimal("0")
            
        cost_per_door = cost / units
        return cost_per_door.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_yards_per_door(
        monthly_volume_yards: Union[Decimal, float],
        unit_count: int
    ) -> Decimal:
        """
        Calculate cubic yards per door (unit).
        
        Formula: Monthly Volume ÷ Number of Units
        
        Args:
            monthly_volume_yards: Monthly volume in cubic yards
            unit_count: Number of residential units
            
        Returns:
            Cubic yards per door
        """
        volume = Decimal(str(monthly_volume_yards))
        units = Decimal(str(unit_count))
        
        if units == 0:
            return Decimal("0")
            
        yards_per_door = volume / units
        return yards_per_door.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_compacted_volume(
        tons: Union[Decimal, float],
        waste_density: Union[Decimal, float],
        compaction_ratio: Union[Decimal, float] = Decimal("3.5")
    ) -> Decimal:
        """
        Calculate compacted volume from weight.
        
        Formula: (Tons × 2000) ÷ (Density × Compaction Ratio)
        
        Args:
            tons: Weight in tons
            waste_density: Density in lbs per cubic yard
            compaction_ratio: Compaction ratio (default 3.5:1)
            
        Returns:
            Compacted volume in cubic yards
        """
        weight_tons = Decimal(str(tons))
        density = Decimal(str(waste_density))
        ratio = Decimal(str(compaction_ratio))
        
        if density == 0 or ratio == 0:
            return Decimal("0")
            
        weight_lbs = weight_tons * WasteCalculations.POUNDS_PER_TON
        compacted_volume = weight_lbs / (density * ratio)
        return compacted_volume.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_total_monthly_cost(
        base_monthly_rate: Union[Decimal, float],
        fuel_surcharge_percent: Optional[float] = None,
        environmental_fee_percent: Optional[float] = None,
        admin_fee: Optional[Union[Decimal, float]] = None,
        container_rental_fee: Optional[Union[Decimal, float]] = None,
        other_fees: Optional[Union[Decimal, float]] = None
    ) -> Tuple[Decimal, Dict[str, Decimal]]:
        """
        Calculate total monthly cost including all fees.
        
        Args:
            base_monthly_rate: Base service rate
            fuel_surcharge_percent: Fuel surcharge percentage
            environmental_fee_percent: Environmental fee percentage
            admin_fee: Fixed admin fee
            container_rental_fee: Container rental fee
            other_fees: Other miscellaneous fees
            
        Returns:
            Tuple of (total_cost, fee_breakdown)
        """
        base_rate = Decimal(str(base_monthly_rate))
        total_cost = base_rate
        fee_breakdown = {"base_rate": base_rate}
        
        # Calculate percentage-based fees
        if fuel_surcharge_percent:
            fuel_fee = base_rate * Decimal(str(fuel_surcharge_percent / 100))
            total_cost += fuel_fee
            fee_breakdown["fuel_surcharge"] = fuel_fee
        
        if environmental_fee_percent:
            env_fee = base_rate * Decimal(str(environmental_fee_percent / 100))
            total_cost += env_fee
            fee_breakdown["environmental_fee"] = env_fee
        
        # Add fixed fees
        if admin_fee:
            admin = Decimal(str(admin_fee))
            total_cost += admin
            fee_breakdown["admin_fee"] = admin
            
        if container_rental_fee:
            rental = Decimal(str(container_rental_fee))
            total_cost += rental
            fee_breakdown["container_rental"] = rental
            
        if other_fees:
            other = Decimal(str(other_fees))
            total_cost += other
            fee_breakdown["other_fees"] = other
        
        fee_breakdown["total"] = total_cost
        return total_cost.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP), fee_breakdown

    @staticmethod
    def calculate_container_utilization(
        actual_volume: Union[Decimal, float],
        container_size_yards: Union[int, Decimal],
        pickup_frequency_weekly: Union[int, Decimal],
        container_quantity: Union[int, Decimal] = 1
    ) -> Decimal:
        """
        Calculate container utilization percentage.
        
        Args:
            actual_volume: Actual monthly volume
            container_size_yards: Container size in cubic yards
            pickup_frequency_weekly: Pickups per week
            container_quantity: Number of containers
            
        Returns:
            Utilization percentage (0.0 to 1.0)
        """
        actual = Decimal(str(actual_volume))
        size = Decimal(str(container_size_yards))
        frequency = Decimal(str(pickup_frequency_weekly))
        quantity = Decimal(str(container_quantity))
        
        theoretical_max = size * frequency * quantity * WasteCalculations.WEEKS_PER_MONTH
        
        if theoretical_max == 0:
            return Decimal("0")
            
        utilization = actual / theoretical_max
        # Cap at 100% utilization
        return min(utilization, Decimal("1.0")).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_annual_cost_projection(
        monthly_cost: Union[Decimal, float],
        annual_increase_percent: Optional[float] = None,
        months_remaining: int = 12
    ) -> Dict[str, Decimal]:
        """
        Calculate annual cost projections with potential increases.
        
        Args:
            monthly_cost: Current monthly cost
            annual_increase_percent: Expected annual increase percentage
            months_remaining: Months remaining in contract
            
        Returns:
            Dictionary with cost projections
        """
        current_monthly = Decimal(str(monthly_cost))
        increase_rate = Decimal(str(annual_increase_percent / 100)) if annual_increase_percent else Decimal("0")
        
        # Current year cost
        current_annual = current_monthly * 12
        
        # Projected cost with increase
        if increase_rate > 0:
            increased_monthly = current_monthly * (Decimal("1") + increase_rate)
            projected_annual = increased_monthly * 12
            annual_increase_amount = projected_annual - current_annual
        else:
            increased_monthly = current_monthly
            projected_annual = current_annual
            annual_increase_amount = Decimal("0")
        
        return {
            "current_monthly": current_monthly.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            "current_annual": current_annual.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            "projected_monthly": increased_monthly.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            "projected_annual": projected_annual.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            "annual_increase_amount": annual_increase_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            "increase_percentage": increase_rate * 100
        }

    @staticmethod
    def calculate_volume_metrics(
        container_size_yards: Union[int, Decimal],
        container_quantity: Union[int, Decimal],
        pickup_frequency_weekly: Union[int, Decimal],
        container_type: str = "front_load"
    ) -> VolumeMetrics:
        """
        Calculate comprehensive volume metrics.
        
        Args:
            container_size_yards: Container size in cubic yards
            container_quantity: Number of containers
            pickup_frequency_weekly: Pickups per week
            container_type: Type of container for efficiency calculation
            
        Returns:
            VolumeMetrics dataclass with all volume calculations
        """
        size = Decimal(str(container_size_yards))
        quantity = Decimal(str(container_quantity))
        frequency = Decimal(str(pickup_frequency_weekly))
        
        efficiency = WasteCalculations.CONTAINER_EFFICIENCY.get(
            container_type, Decimal("0.85")
        )
        
        # Weekly volume
        weekly_volume = size * quantity * frequency
        
        # Monthly volume with efficiency factor
        monthly_volume = weekly_volume * WasteCalculations.WEEKS_PER_MONTH * efficiency
        
        # Theoretical maximum (100% utilization)
        theoretical_max = size * quantity * frequency * WasteCalculations.WEEKS_PER_MONTH
        
        # Utilization rate
        utilization_rate = monthly_volume / theoretical_max if theoretical_max > 0 else Decimal("0")
        
        return VolumeMetrics(
            monthly_volume_yards=monthly_volume.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            weekly_volume_yards=weekly_volume.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            theoretical_max_volume=theoretical_max.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            utilization_rate=utilization_rate.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            pickup_frequency_weekly=int(frequency),
            container_size_yards=int(size),
            container_quantity=int(quantity)
        )

    @staticmethod
    def calculate_cost_metrics(
        base_monthly_rate: Union[Decimal, float],
        monthly_volume_yards: Union[Decimal, float],
        unit_count: int,
        pickup_frequency_weekly: Union[int, Decimal],
        fees: Optional[Dict[str, Union[Decimal, float]]] = None
    ) -> CostMetrics:
        """
        Calculate comprehensive cost metrics.
        
        Args:
            base_monthly_rate: Base monthly service rate
            monthly_volume_yards: Monthly volume in cubic yards
            unit_count: Number of residential units
            pickup_frequency_weekly: Pickups per week
            fees: Optional dictionary of additional fees
            
        Returns:
            CostMetrics dataclass with all cost calculations
        """
        base_cost = Decimal(str(base_monthly_rate))
        volume = Decimal(str(monthly_volume_yards))
        units = Decimal(str(unit_count))
        frequency = Decimal(str(pickup_frequency_weekly))
        
        # Calculate total cost including fees
        total_cost = base_cost
        fees_amount = Decimal("0")
        
        if fees:
            for fee_value in fees.values():
                fee_decimal = Decimal(str(fee_value))
                total_cost += fee_decimal
                fees_amount += fee_decimal
        
        # Calculate metrics
        annual_cost = total_cost * 12
        cost_per_yard = total_cost / volume if volume > 0 else Decimal("0")
        cost_per_door = total_cost / units if units > 0 else Decimal("0")
        
        # Cost per pickup
        pickups_per_month = frequency * WasteCalculations.WEEKS_PER_MONTH
        cost_per_pickup = total_cost / pickups_per_month if pickups_per_month > 0 else Decimal("0")
        
        # Fees percentage
        fees_percentage = (fees_amount / total_cost * 100) if total_cost > 0 else Decimal("0")
        
        return CostMetrics(
            base_monthly_cost=base_cost.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            total_monthly_cost=total_cost.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            annual_cost=annual_cost.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            cost_per_yard=cost_per_yard.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            cost_per_door=cost_per_door.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            cost_per_pickup=cost_per_pickup.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            fees_amount=fees_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            fees_percentage=fees_percentage.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
        )

    @staticmethod
    def calculate_efficiency_metrics(
        cost_metrics: CostMetrics,
        volume_metrics: VolumeMetrics,
        unit_count: int,
        property_type: str = "mid-rise"
    ) -> EfficiencyMetrics:
        """
        Calculate service efficiency metrics.
        
        Args:
            cost_metrics: Cost metrics from calculate_cost_metrics
            volume_metrics: Volume metrics from calculate_volume_metrics
            unit_count: Number of residential units
            property_type: Type of property for benchmarking
            
        Returns:
            EfficiencyMetrics dataclass with efficiency calculations
        """
        units = Decimal(str(unit_count))
        
        # Yards per door
        yards_per_door = volume_metrics.monthly_volume_yards / units if units > 0 else Decimal("0")
        
        # Efficiency score (0-100)
        efficiency_score = 0
        
        # Score based on utilization (0-40 points)
        utilization_score = int(volume_metrics.utilization_rate * 40)
        efficiency_score += utilization_score
        
        # Score based on cost efficiency (0-30 points)
        # Lower cost per yard is better
        if cost_metrics.cost_per_yard <= Decimal("50"):
            cost_score = 30
        elif cost_metrics.cost_per_yard <= Decimal("75"):
            cost_score = 20
        elif cost_metrics.cost_per_yard <= Decimal("100"):
            cost_score = 10
        else:
            cost_score = 0
        efficiency_score += cost_score
        
        # Score based on fees ratio (0-30 points)
        if cost_metrics.fees_percentage <= Decimal("10"):
            fee_score = 30
        elif cost_metrics.fees_percentage <= Decimal("20"):
            fee_score = 20
        elif cost_metrics.fees_percentage <= Decimal("30"):
            fee_score = 10
        else:
            fee_score = 0
        efficiency_score += fee_score
        
        # Service density (pickups per container per week)
        service_density = Decimal(str(volume_metrics.pickup_frequency_weekly)) / Decimal(str(volume_metrics.container_quantity))
        
        # Waste generation rate (yards per door)
        waste_generation_rate = yards_per_door
        
        return EfficiencyMetrics(
            yards_per_door=yards_per_door.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            cost_efficiency_score=efficiency_score,
            container_utilization=volume_metrics.utilization_rate,
            service_density=service_density.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
            waste_generation_rate=waste_generation_rate.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
        )


def calculate_savings_potential(
    current_cost_per_door: Union[Decimal, float],
    benchmark_cost_per_door: Union[Decimal, float],
    unit_count: int
) -> Dict[str, Decimal]:
    """
    Calculate potential savings compared to benchmark.
    
    Args:
        current_cost_per_door: Current cost per door
        benchmark_cost_per_door: Target benchmark cost per door
        unit_count: Number of units
        
    Returns:
        Dictionary with savings calculations
    """
    current = Decimal(str(current_cost_per_door))
    benchmark = Decimal(str(benchmark_cost_per_door))
    units = Decimal(str(unit_count))
    
    monthly_savings_per_door = max(Decimal("0"), current - benchmark)
    monthly_savings_total = monthly_savings_per_door * units
    annual_savings = monthly_savings_total * 12
    
    savings_percentage = Decimal("0")
    if current > 0:
        savings_percentage = (monthly_savings_per_door / current) * 100
    
    return {
        "monthly_savings_per_door": monthly_savings_per_door.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
        "monthly_savings_total": monthly_savings_total.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
        "annual_savings": annual_savings.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP),
        "savings_percentage": savings_percentage.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
    }