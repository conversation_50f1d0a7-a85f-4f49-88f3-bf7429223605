# Advantage Waste Enterprise - Development Environment Setup
# Run this script to set up Claude Code CLI, Cursor IDE integration, and MCP tools

Write-Host "Setting up Advantage Waste Enterprise Development Environment..." -ForegroundColor Green

# Check if we're in the right directory
$projectRoot = "C:\Users\<USER>\Documents\Claude Code\advantage-waste-enterprise"
if (!(Test-Path $projectRoot)) {
    Write-Host "Error: Project directory not found at $projectRoot" -ForegroundColor Red
    exit 1
}

Set-Location $projectRoot

# 1. Install/Update Claude Code (if not already installed)
Write-Host "`n1. Checking Claude Code installation..." -ForegroundColor Yellow

# Check if Claude Code is available via WSL
try {
    $claudeVersion = wsl --distribution Ubuntu --exec bash -c "export PATH=~/.npm-global/bin:/usr/bin:`$PATH && claude --version 2>/dev/null || echo 'not-installed'"
    if ($claudeVersion -eq "not-installed") {
        Write-Host "Installing Claude Code via WSL..." -ForegroundColor Yellow
        wsl --distribution Ubuntu --exec bash -c "
            curl -sSL https://raw.githubusercontent.com/Anthropic/claude-code/main/install.sh | bash
            export PATH=~/.npm-global/bin:/usr/bin:`$PATH
            echo 'export PATH=~/.npm-global/bin:/usr/bin:`$PATH' >> ~/.bashrc
        "
    } else {
        Write-Host "Claude Code found: $claudeVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "WSL not available. Please install Claude Code manually or set up WSL." -ForegroundColor Red
}

# 2. Set up Cursor IDE integration
Write-Host "`n2. Setting up Cursor IDE integration..." -ForegroundColor Yellow

# Create Cursor settings for this project
$cursorDir = ".\.vscode"
if (!(Test-Path $cursorDir)) {
    New-Item -ItemType Directory -Path $cursorDir -Force | Out-Null
}

# Cursor/VSCode settings optimized for waste management development
$cursorSettings = @"
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll": true
  },
  "python.defaultInterpreterPath": "./backend/venv/Scripts/python.exe",
  "python.terminal.activateEnvironment": true,
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": ["backend/tests"],
  "python.linting.enabled": true,
  "python.linting.ruffEnabled": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "files.associations": {
    "*.md": "markdown",
    "CLAUDE.md": "markdown",
    "INITIAL.md": "markdown",
    "*.prp.md": "markdown"
  },
  "files.exclude": {
    "**/__pycache__": true,
    "**/node_modules": true,
    "**/.git": true,
    "**/venv": true,
    "**/.pytest_cache": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/venv": true,
    "**/.git": true
  },
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "tailwindCSS.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "claude.contextFiles": [
    "CLAUDE.md",
    "examples/**/*.py",
    "PRPs/templates/**/*.md",
    "README.md"
  ]
}
"@

$cursorSettings | Out-File -FilePath "$cursorDir\settings.json" -Encoding UTF8

# Create tasks for development workflow
$cursorTasks = @"
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Backend Development Server",
      "type": "shell",
      "command": ".\backend\venv\Scripts\activate && uvicorn src.main:app --reload --host 0.0.0.0 --port 8000",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new",
        "showReuseMessage": true,
        "clear": false
      },
      "options": {
        "cwd": ".\backend"
      },
      "problemMatcher": []
    },
    {
      "label": "Start Frontend Development Server",
      "type": "shell",
      "command": "npm run dev",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      },
      "options": {
        "cwd": ".\frontend"
      },
      "problemMatcher": []
    },
    {
      "label": "Run Backend Tests",
      "type": "shell",
      "command": ".\venv\Scripts\activate && pytest tests/ -v --cov=src",
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": true,
        "panel": "new"
      },
      "options": {
        "cwd": ".\backend"
      }
    },
    {
      "label": "Run Frontend Tests", 
      "type": "shell",
      "command": "npm test",
      "group": "test",
      "options": {
        "cwd": ".\frontend"
      }
    },
    {
      "label": "Analyze Contract (Claude Code)",
      "type": "shell",
      "command": "wsl --distribution Ubuntu --exec bash -c \"export PATH=~/.npm-global/bin:/usr/bin:`$PATH && cd '/mnt/c/Users/<USER>/Documents/Claude Code/advantage-waste-enterprise' && claude /analyze-contract `${input:contractFile}\"",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": true,
        "panel": "new"
      }
    },
    {
      "label": "Generate PRP (Claude Code)",
      "type": "shell", 
      "command": "wsl --distribution Ubuntu --exec bash -c \"export PATH=~/.npm-global/bin:/usr/bin:`$PATH && cd '/mnt/c/Users/<USER>/Documents/Claude Code/advantage-waste-enterprise' && claude /generate-prp INITIAL.md\"",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": true,
        "panel": "new"
      }
    }
  ],
  "inputs": [
    {
      "id": "contractFile",
      "description": "Contract file to analyze",
      "default": "examples/sample_contract.pdf",
      "type": "promptString"
    }
  ]
}
"@

$cursorTasks | Out-File -FilePath "$cursorDir\tasks.json" -Encoding UTF8

# Create launch configurations for debugging
$cursorLaunch = @"
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug FastAPI Backend",
      "type": "python",
      "request": "launch",
      "program": "`${workspaceFolder}/backend/venv/Scripts/uvicorn",
      "args": ["src.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
      "cwd": "`${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "`${workspaceFolder}/backend/src"
      },
      "console": "integratedTerminal"
    },
    {
      "name": "Debug Contract Analysis",
      "type": "python", 
      "request": "launch",
      "program": "`${workspaceFolder}/examples/contract_analysis_example.py",
      "cwd": "`${workspaceFolder}",
      "console": "integratedTerminal"
    }
  ]
}
"@

$cursorLaunch | Out-File -FilePath "$cursorDir\launch.json" -Encoding UTF8

Write-Host "Cursor IDE configuration created" -ForegroundColor Green

# 3. Set up project-specific MCP servers
Write-Host "`n3. Setting up project-specific MCP servers..." -ForegroundColor Yellow

# Create project-specific Claude Desktop config
$projectClaudeConfig = @"
{
  "mcpServers": {
    "advantage-waste-filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "C:\\Users\\<USER>\\Documents\\Claude Code\\advantage-waste-enterprise"
      ]
    },
    "advantage-waste-github": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"
      }
    },
    "desktop-commander": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@wonderwhy-er/desktop-commander"
      ]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@mcp/server-sequential-thinking"
      ]
    },
    "n8n-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@mcp/server-n8n"
      ],
      "env": {
        "N8N_BASE_URL": "http://localhost:5678",
        "N8N_API_KEY": "your-n8n-api-key-here"
      }
    }
  }
}
"@

$projectClaudeConfig | Out-File -FilePath ".\claude-desktop-config.json" -Encoding UTF8

Write-Host "Project-specific MCP servers configured" -ForegroundColor Green

# 4. Install Python dependencies
Write-Host "`n4. Setting up Python environment..." -ForegroundColor Yellow

if (!(Test-Path ".\backend")) {
    New-Item -ItemType Directory -Path ".\backend" -Force | Out-Null
}

Set-Location ".\backend"

# Check if virtual environment exists
if (!(Test-Path ".\venv")) {
    Write-Host "Creating Python virtual environment..." -ForegroundColor Yellow
    python -m venv venv
}

# Activate virtual environment and install dependencies
& ".\venv\Scripts\Activate.ps1"

# Create requirements.txt if it doesn't exist
if (!(Test-Path ".\requirements.txt")) {
    $requirements = @"
# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Data processing and analysis
pandas==2.1.4
numpy==1.25.2
openpyxl==3.1.2
xlsxwriter==3.1.9

# PDF processing
PyPDF2==3.0.1
pdfplumber==0.10.3

# HTTP client
httpx==0.25.2
requests==2.31.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Code quality
ruff==0.1.6
mypy==1.7.1
black==23.11.0

# Development
python-dotenv==1.0.0
rich==13.7.0

# Waste management specific
decimal==1.70
"@
    $requirements | Out-File -FilePath ".\requirements.txt" -Encoding UTF8
}

Write-Host "Installing Python dependencies..." -ForegroundColor Yellow
pip install -r requirements.txt

Set-Location ..

# 5. Set up Node.js frontend dependencies
Write-Host "`n5. Setting up Node.js environment..." -ForegroundColor Yellow

if (!(Test-Path ".\frontend")) {
    New-Item -ItemType Directory -Path ".\frontend" -Force | Out-Null
}

Set-Location ".\frontend"

# Create package.json if it doesn't exist
if (!(Test-Path ".\package.json")) {
    $packageJson = @"
{
  "name": "advantage-waste-frontend",
  "version": "1.0.0",
  "description": "Enterprise waste management frontend for Greystar Advantage Waste",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint src --ext ts,tsx --fix",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.18.0",
    "@tanstack/react-query": "^5.8.0",
    "axios": "^1.6.0",
    "date-fns": "^2.30.0",
    "recharts": "^2.8.0",
    "react-hook-form": "^7.47.0",
    "@hookform/resolvers": "^3.3.2",
    "zod": "^3.22.4",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.37",
    "@types/react-dom": "^18.2.15",
    "@typescript-eslint/eslint-plugin": "^6.10.0",
    "@typescript-eslint/parser": "^6.10.0",
    "@vitejs/plugin-react": "^4.1.0",
    "eslint": "^8.53.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.4",
    "typescript": "^5.2.2",
    "vite": "^4.5.0",
    "tailwindcss": "^3.3.5",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.31",
    "@types/jest": "^29.5.8",
    "jest": "^29.7.0",
    "jest-environment-jsdom": "^29.7.0"
  }
}
"@
    $packageJson | Out-File -FilePath ".\package.json" -Encoding UTF8
}

if (Get-Command npm -ErrorAction SilentlyContinue) {
    Write-Host "Installing Node.js dependencies..." -ForegroundColor Yellow
    npm install
} else {
    Write-Host "Node.js not found. Please install Node.js 18+ and run 'npm install' in the frontend directory." -ForegroundColor Yellow
}

Set-Location ..

# 6. Create development startup scripts
Write-Host "`n6. Creating development startup scripts..." -ForegroundColor Yellow

# Create a development startup script
$devStartScript = @"
# Advantage Waste Enterprise - Development Startup Script
# This script starts all development services

Write-Host "Starting Advantage Waste Enterprise Development Environment..." -ForegroundColor Green

# Start backend in new terminal
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'C:\Users\<USER>\Documents\Claude Code\advantage-waste-enterprise\backend'; .\venv\Scripts\Activate.ps1; uvicorn src.main:app --reload --host 0.0.0.0 --port 8000"

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start frontend in new terminal
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'C:\Users\<USER>\Documents\Claude Code\advantage-waste-enterprise\frontend'; npm run dev"

Write-Host "Development servers starting..." -ForegroundColor Green
Write-Host "Backend API: http://localhost:8000" -ForegroundColor Yellow
Write-Host "Frontend: http://localhost:3000" -ForegroundColor Yellow
Write-Host "API Docs: http://localhost:8000/docs" -ForegroundColor Yellow

# Open Cursor IDE
if (Get-Command cursor -ErrorAction SilentlyContinue) {
    Write-Host "Opening Cursor IDE..." -ForegroundColor Yellow
    Start-Process cursor -ArgumentList "."
} else {
    Write-Host "Cursor IDE not found. Please install Cursor and run 'cursor .' in this directory." -ForegroundColor Yellow
}
"@

$devStartScript | Out-File -FilePath ".\start-dev.ps1" -Encoding UTF8

# Create Claude Code startup script
$claudeStartScript = @"
#!/bin/bash
# Claude Code startup script for Advantage Waste Enterprise

echo "Starting Claude Code for Advantage Waste Enterprise..."

# Set the project directory
PROJECT_DIR="/mnt/c/Users/<USER>/Documents/Claude Code/advantage-waste-enterprise"

# Change to project directory
cd "`$PROJECT_DIR"

# Set environment variables for waste management context
export CLAUDE_PROJECT="advantage-waste-enterprise"
export CLAUDE_CONTEXT_FILES="CLAUDE.md,examples/contract_analysis_example.py,PRPs/templates/prp_base.md"

# Start Claude Code with project context
claude --project-root "`$PROJECT_DIR" --context-files "`$CLAUDE_CONTEXT_FILES"
"@

$claudeStartScript | Out-File -FilePath ".\start-claude.sh" -Encoding UTF8

# Create Windows batch file for Claude Code
$claudeBatchScript = @"
@echo off
REM Claude Code startup for Advantage Waste Enterprise

echo Starting Claude Code for Advantage Waste Enterprise...

REM Change to project directory  
cd "C:\Users\<USER>\Documents\Claude Code\advantage-waste-enterprise"

REM Start Claude Code via WSL
wsl --distribution Ubuntu --exec bash -c "cd '/mnt/c/Users/<USER>/Documents/Claude Code/advantage-waste-enterprise' && export CLAUDE_PROJECT=advantage-waste-enterprise && export CLAUDE_CONTEXT_FILES=CLAUDE.md,examples/contract_analysis_example.py,PRPs/templates/prp_base.md && export PATH=~/.npm-global/bin:/usr/bin:`$PATH && claude"
"@

$claudeBatchScript | Out-File -FilePath ".\start-claude.bat" -Encoding UTF8

Write-Host "Development scripts created" -ForegroundColor Green

# 7. Set up Git repository
Write-Host "`n7. Initializing Git repository..." -ForegroundColor Yellow

if (!(Test-Path ".\.git")) {
    git init
    git add .
    git commit -m "Initial project setup with Claude Code and enterprise waste management structure"
    Write-Host "Git repository initialized" -ForegroundColor Green
} else {
    Write-Host "Git repository already exists" -ForegroundColor Yellow
}

Write-Host "`n✅ Development Environment Setup Complete!" -ForegroundColor Green
Write-Host "`nNext Steps:" -ForegroundColor Yellow
Write-Host "1. Run .\start-dev.ps1 to start development servers" -ForegroundColor White
Write-Host "2. Run .\start-claude.bat to open Claude Code with project context" -ForegroundColor White  
Write-Host "3. Use 'cursor .' to open in Cursor IDE" -ForegroundColor White
Write-Host "4. Edit INITIAL.md and run '/generate-prp INITIAL.md' in Claude Code" -ForegroundColor White

Write-Host "`nAvailable Commands in Claude Code:" -ForegroundColor Yellow
Write-Host "  /analyze-contract [file] - Analyze waste management contracts" -ForegroundColor White
Write-Host "  /generate-prp [feature] - Create Product Requirements Prompts" -ForegroundColor White
Write-Host "  /execute-prp [prp-file] - Implement features from PRPs" -ForegroundColor White

Write-Host "`nProject URLs:" -ForegroundColor Yellow
Write-Host "  Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "  API Documentation: http://localhost:8000/docs" -ForegroundColor White
Write-Host "  Frontend: http://localhost:3000" -ForegroundColor White
