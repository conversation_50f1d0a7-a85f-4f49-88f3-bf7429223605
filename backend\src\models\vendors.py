"""
Vendor-related database models for Advantage Waste Enterprise.
Handles waste management vendor data and performance tracking.
"""

from sqlalchemy import (
    Column, Integer, String, Text, Numeric, Boolean, Date, DateTime,
    JSON, Index, ForeignKey
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from datetime import date
from typing import Optional, Dict, Any

from ..core.database import Base
from .base import TimestampMixin, AuditMixin


class VendorType(PyEnum):
    """Vendor type enumeration"""
    NATIONAL = "national"
    REGIONAL = "regional"
    LOCAL = "local"
    BROKER = "broker"


class ServiceType(PyEnum):
    """Service type enumeration"""
    COLLECTION = "collection"
    DISPOSAL = "disposal"
    RECYCLING = "recycling"
    COMPACTOR = "compactor"
    HAULING = "hauling"
    FULL_SERVICE = "full_service"


class Vendor(Base, AuditMixin):
    """
    Waste management vendor model.
    Represents companies providing waste services to Greystar properties.
    """
    __tablename__ = "vendors"

    id = Column(Integer, primary_key=True, index=True)
    
    # Vendor identification
    vendor_name = Column(String(200), nullable=False, index=True)
    vendor_code = Column(String(20), nullable=True, unique=True)
    legal_name = Column(String(200), nullable=True)
    doing_business_as = Column(String(200), nullable=True)
    
    # Vendor classification
    vendor_type = Column(String(20), nullable=False, index=True)
    primary_service_types = Column(JSON, nullable=True)  # Array of service types
    
    # Contact information
    primary_contact_name = Column(String(100), nullable=True)
    primary_contact_email = Column(String(200), nullable=True)
    primary_contact_phone = Column(String(20), nullable=True)
    
    # Address information
    address = Column(Text, nullable=True)
    city = Column(String(100), nullable=True, index=True)
    state = Column(String(2), nullable=True, index=True)
    zip_code = Column(String(10), nullable=True)
    
    # Business details
    license_number = Column(String(50), nullable=True)
    insurance_provider = Column(String(100), nullable=True)
    insurance_expiry = Column(Date, nullable=True)
    bonding_amount = Column(Numeric(12, 2), nullable=True)
    
    # Service areas
    service_states = Column(JSON, nullable=True)  # Array of state codes
    service_markets = Column(JSON, nullable=True)  # Array of market names
    service_radius = Column(Integer, nullable=True)  # Miles from headquarters
    
    # Business status
    is_active = Column(Boolean, default=True, nullable=False)
    is_preferred = Column(Boolean, default=False)
    is_approved = Column(Boolean, default=False)
    approval_date = Column(Date, nullable=True)
    
    # Pricing structure
    pricing_model = Column(String(50), nullable=True)  # flat_rate, volume_based, etc.
    fuel_surcharge_policy = Column(Text, nullable=True)
    environmental_fee_policy = Column(Text, nullable=True)
    
    # Performance tracking
    average_response_time = Column(Numeric(4, 1), nullable=True)  # Hours
    service_quality_rating = Column(Numeric(3, 1), nullable=True)  # 1-10 scale
    reliability_rating = Column(Numeric(3, 1), nullable=True)
    cost_competitiveness = Column(Numeric(3, 1), nullable=True)
    
    # Contract terms preferences
    preferred_contract_length = Column(Integer, nullable=True)  # Months
    early_termination_policy = Column(Text, nullable=True)
    auto_renewal_policy = Column(Text, nullable=True)
    
    # Additional data
    vendor_metadata = Column(JSON, nullable=True)
    integration_data = Column(JSON, nullable=True)  # API keys, system IDs, etc.
    
    # Relationships
    contracts = relationship("Contract", back_populates="vendor")
    performance_records = relationship("VendorPerformance", back_populates="vendor", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index("idx_vendor_type_state", "vendor_type", "state"),
        Index("idx_vendor_active_preferred", "is_active", "is_preferred"),
        Index("idx_vendor_service_rating", "service_quality_rating"),
    )
    
    @property
    def overall_rating(self) -> float:
        """Calculate overall vendor rating from individual metrics"""
        ratings = [
            self.service_quality_rating,
            self.reliability_rating,
            self.cost_competitiveness
        ]
        valid_ratings = [r for r in ratings if r is not None]
        
        if valid_ratings:
            return sum(valid_ratings) / len(valid_ratings)
        return 0.0
    
    @property
    def contract_count(self) -> int:
        """Get current active contract count"""
        return len([c for c in self.contracts if c.status == "active"])


class VendorPerformance(Base, TimestampMixin):
    """
    Vendor performance tracking model.
    Records monthly performance metrics for vendor evaluation.
    """
    __tablename__ = "vendor_performance"

    id = Column(Integer, primary_key=True, index=True)
    
    # Vendor and time period
    vendor_id = Column(Integer, ForeignKey("vendors.id"), nullable=False)
    performance_date = Column(Date, nullable=False, index=True)
    
    # Contract metrics
    active_contracts = Column(Integer, nullable=True)
    total_monthly_revenue = Column(Numeric(12, 2), nullable=True)
    properties_served = Column(Integer, nullable=True)
    
    # Service quality metrics
    service_requests = Column(Integer, default=0)
    service_requests_resolved = Column(Integer, default=0)
    average_resolution_time = Column(Numeric(5, 1), nullable=True)  # Hours
    
    missed_pickups = Column(Integer, default=0)
    late_pickups = Column(Integer, default=0)
    total_scheduled_pickups = Column(Integer, nullable=True)
    
    # Customer satisfaction
    customer_complaints = Column(Integer, default=0)
    customer_compliments = Column(Integer, default=0)
    customer_satisfaction_score = Column(Numeric(3, 1), nullable=True)  # 1-10 scale
    
    # Cost performance
    cost_increases_requested = Column(Integer, default=0)
    cost_increases_approved = Column(Integer, default=0)
    emergency_service_charges = Column(Numeric(10, 2), default=0)
    
    # Environmental performance
    recycling_compliance = Column(Numeric(5, 2), nullable=True)  # Percentage
    waste_diversion_rate = Column(Numeric(5, 2), nullable=True)
    environmental_incidents = Column(Integer, default=0)
    
    # Communication metrics
    response_time_hours = Column(Numeric(4, 1), nullable=True)
    communication_quality = Column(Numeric(3, 1), nullable=True)  # 1-10 scale
    proactive_communications = Column(Integer, default=0)
    
    # Performance scores
    service_score = Column(Numeric(3, 1), nullable=True)  # 1-10 scale
    reliability_score = Column(Numeric(3, 1), nullable=True)
    cost_score = Column(Numeric(3, 1), nullable=True)
    communication_score = Column(Numeric(3, 1), nullable=True)
    overall_score = Column(Numeric(3, 1), nullable=True)
    
    # Benchmark comparisons
    performance_vs_peers = Column(Numeric(6, 2), nullable=True)  # Percentage variance
    market_ranking = Column(Integer, nullable=True)
    
    # Performance data details
    performance_data = Column(JSON, nullable=True)
    incidents = Column(JSON, nullable=True)
    achievements = Column(JSON, nullable=True)
    
    # Notes and comments
    performance_notes = Column(Text, nullable=True)
    improvement_areas = Column(JSON, nullable=True)
    action_items = Column(JSON, nullable=True)
    
    # Data sources
    data_source = Column(String(50), nullable=True)
    verified = Column(Boolean, default=False)
    
    # Relationships
    vendor = relationship("Vendor", back_populates="performance_records")
    
    # Indexes for performance
    __table_args__ = (
        Index("idx_performance_vendor_date", "vendor_id", "performance_date"),
        Index("idx_performance_overall_score", "overall_score"),
        Index("idx_performance_date", "performance_date"),
    )
    
    @property
    def pickup_success_rate(self) -> float:
        """Calculate successful pickup percentage"""
        if self.total_scheduled_pickups and self.total_scheduled_pickups > 0:
            successful = self.total_scheduled_pickups - self.missed_pickups - self.late_pickups
            return (successful / self.total_scheduled_pickups) * 100
        return 0.0
    
    @property
    def service_resolution_rate(self) -> float:
        """Calculate service request resolution percentage"""
        if self.service_requests and self.service_requests > 0:
            return (self.service_requests_resolved / self.service_requests) * 100
        return 0.0
    
    @property
    def customer_sentiment_ratio(self) -> float:
        """Calculate ratio of compliments to complaints"""
        if self.customer_complaints > 0:
            return self.customer_compliments / self.customer_complaints
        return float(self.customer_compliments) if self.customer_compliments > 0 else 0.0