"""
Performance and Load Testing Suite
===================================

Enterprise-scale performance tests for the Advantage Waste Contract Renewal Analysis System.
Tests system performance under realistic loads including 500+ concurrent users and 3,850+ properties.

Performance Requirements:
- Handle 3,850+ Greystar properties
- Support 500+ concurrent users during peak renewal season
- Contract analysis: <500ms per analysis
- Batch analysis: 100+ contracts per second
- Memory usage: <512MB under normal load
- Database queries: <100ms average response time
- API endpoints: <2s response time (95th percentile)

Tests cover:
- Single contract analysis performance
- Batch processing performance  
- Concurrent user simulation
- Database performance under load
- Memory usage and cleanup
- API endpoint response times
- System resource monitoring
"""

import pytest
import asyncio
import time
import statistics
import threading
import psutil
import json
from typing import List, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from decimal import Decimal
from datetime import datetime

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from examples.contract_analysis_example import (
    ContractAnalyzer, ContractTerms, PropertyInfo
)


class PerformanceMonitor:
    """Monitor system performance during tests"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.measurements = []
        self.monitoring = False
    
    def start_monitoring(self):
        """Start performance monitoring"""
        self.monitoring = True
        self.measurements = []
        
        def monitor():
            while self.monitoring:
                measurement = {
                    'timestamp': time.time(),
                    'cpu_percent': self.process.cpu_percent(),
                    'memory_mb': self.process.memory_info().rss / 1024 / 1024,
                    'memory_percent': self.process.memory_percent(),
                    'threads': self.process.num_threads()
                }
                self.measurements.append(measurement)
                time.sleep(0.1)  # Sample every 100ms
        
        self.monitor_thread = threading.Thread(target=monitor, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self) -> Dict[str, Any]:
        """Stop monitoring and return performance summary"""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=1.0)
        
        if not self.measurements:
            return {}
        
        cpu_values = [m['cpu_percent'] for m in self.measurements]
        memory_values = [m['memory_mb'] for m in self.measurements]
        
        return {
            'duration_seconds': self.measurements[-1]['timestamp'] - self.measurements[0]['timestamp'],
            'cpu_avg': statistics.mean(cpu_values),
            'cpu_max': max(cpu_values),
            'memory_avg_mb': statistics.mean(memory_values),
            'memory_max_mb': max(memory_values),
            'memory_peak_percent': max(m['memory_percent'] for m in self.measurements),
            'samples': len(self.measurements)
        }


@pytest.fixture
def performance_monitor():
    """Performance monitoring fixture"""
    monitor = PerformanceMonitor()
    yield monitor
    if monitor.monitoring:
        monitor.stop_monitoring()


@pytest.fixture
def performance_benchmarks():
    """Performance benchmark thresholds"""
    return {
        'single_analysis_max_ms': 500,
        'batch_analysis_min_per_second': 100,
        'concurrent_users_max': 500,
        'memory_limit_mb': 512,
        'cpu_limit_percent': 80,
        'api_response_95th_percentile_ms': 2000,
        'database_query_avg_ms': 100
    }


class TestSingleAnalysisPerformance:
    """Test performance of individual contract analysis operations"""
    
    def test_basic_analysis_performance(self, contract_analyzer, realistic_gfl_contract, sample_property_garden_style, performance_benchmarks):
        """Test basic contract analysis performance"""
        # Warm up
        for _ in range(5):
            contract_analyzer.analyze_contract(realistic_gfl_contract, sample_property_garden_style)
        
        # Performance test
        times = []
        for _ in range(100):
            start_time = time.perf_counter()
            analysis = contract_analyzer.analyze_contract(realistic_gfl_contract, sample_property_garden_style)
            end_time = time.perf_counter()
            
            analysis_time = (end_time - start_time) * 1000  # Convert to milliseconds
            times.append(analysis_time)
            
            # Verify analysis completeness
            assert 'overall_grade' in analysis
            assert 'metrics' in analysis
            assert 'recommendations' in analysis
        
        # Performance statistics
        avg_time = statistics.mean(times)
        max_time = max(times)
        p95_time = statistics.quantiles(times, n=20)[18]  # 95th percentile
        
        print(f"Analysis Performance - Avg: {avg_time:.2f}ms, Max: {max_time:.2f}ms, 95th: {p95_time:.2f}ms")
        
        # Performance assertions
        assert avg_time < performance_benchmarks['single_analysis_max_ms']
        assert p95_time < performance_benchmarks['single_analysis_max_ms'] * 1.5
        assert max_time < performance_benchmarks['single_analysis_max_ms'] * 2
    
    def test_complex_contract_analysis_performance(self, contract_analyzer, poor_contract, sample_property_garden_style, performance_benchmarks):
        """Test performance with complex contracts that generate many recommendations"""
        times = []
        recommendation_counts = []
        
        for _ in range(50):
            start_time = time.perf_counter()
            analysis = contract_analyzer.analyze_contract(poor_contract, sample_property_garden_style)
            end_time = time.perf_counter()
            
            analysis_time = (end_time - start_time) * 1000
            times.append(analysis_time)
            recommendation_counts.append(len(analysis['recommendations']))
        
        avg_time = statistics.mean(times)
        avg_recommendations = statistics.mean(recommendation_counts)
        
        print(f"Complex Analysis - Avg: {avg_time:.2f}ms, Avg Recommendations: {avg_recommendations:.1f}")
        
        # Complex analysis should still be fast
        assert avg_time < performance_benchmarks['single_analysis_max_ms']
        # Should generate multiple recommendations for poor contract
        assert avg_recommendations >= 3
    
    def test_large_property_analysis_performance(self, contract_analyzer, realistic_gfl_contract, performance_benchmarks):
        """Test performance with very large properties"""
        # Create large property (1200 units)
        large_property = PropertyInfo(
            name="Mega Complex",
            address="789 Large Ave, Big City, TX",
            unit_count=1200,
            property_type="high-rise"
        )
        
        times = []
        
        for _ in range(30):
            start_time = time.perf_counter()
            analysis = contract_analyzer.analyze_contract(realistic_gfl_contract, large_property)
            end_time = time.perf_counter()
            
            analysis_time = (end_time - start_time) * 1000
            times.append(analysis_time)
            
            # Verify calculations scale correctly
            assert analysis['metrics']['cost_per_door'] > 0
            assert analysis['property_info']['units'] == 1200
        
        avg_time = statistics.mean(times)
        print(f"Large Property Analysis - Avg: {avg_time:.2f}ms for {large_property.unit_count} units")
        
        # Should not be significantly slower for large properties
        assert avg_time < performance_benchmarks['single_analysis_max_ms']


class TestBatchProcessingPerformance:
    """Test performance of batch contract analysis operations"""
    
    def test_batch_analysis_throughput(self, contract_analyzer, enterprise_scale_contracts, performance_test_properties, performance_benchmarks):
        """Test batch processing throughput"""
        # Use subset for performance testing
        test_contracts = enterprise_scale_contracts[:20]
        test_properties = performance_test_properties[:10]
        
        start_time = time.perf_counter()
        analyses_completed = 0
        
        for contract in test_contracts:
            for property_info in test_properties:
                analysis = contract_analyzer.analyze_contract(contract, property_info)
                analyses_completed += 1
                
                # Verify analysis quality
                assert 'overall_grade' in analysis
                assert analysis['metrics']['cost_per_door'] > 0
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        analyses_per_second = analyses_completed / total_time
        
        print(f"Batch Analysis - {analyses_completed} analyses in {total_time:.2f}s = {analyses_per_second:.1f} analyses/sec")
        
        # Performance requirements
        assert analyses_per_second >= performance_benchmarks['batch_analysis_min_per_second']
        assert total_time < 10.0  # Should complete reasonable batch in under 10 seconds
    
    def test_memory_usage_during_batch_processing(self, contract_analyzer, enterprise_scale_contracts, performance_test_properties, performance_monitor, performance_benchmarks):
        """Test memory usage during extended batch processing"""
        performance_monitor.start_monitoring()
        
        # Process larger batch to test memory management
        test_contracts = enterprise_scale_contracts[:30]
        test_properties = performance_test_properties[:15]
        
        analyses = []
        
        for i, contract in enumerate(test_contracts):
            for j, property_info in enumerate(test_properties):
                analysis = contract_analyzer.analyze_contract(contract, property_info)
                analyses.append(analysis)
                
                # Periodic cleanup simulation
                if len(analyses) > 100:
                    analyses = analyses[-50:]  # Keep only recent analyses
        
        performance_stats = performance_monitor.stop_monitoring()
        
        print(f"Memory Usage - Avg: {performance_stats['memory_avg_mb']:.1f}MB, Peak: {performance_stats['memory_max_mb']:.1f}MB")
        
        # Memory should stay within limits
        assert performance_stats['memory_max_mb'] < performance_benchmarks['memory_limit_mb']
        assert performance_stats['memory_peak_percent'] < 50  # Should not consume more than 50% of system memory
    
    def test_sequential_vs_parallel_processing(self, contract_analyzer, enterprise_scale_contracts, performance_test_properties):
        """Compare sequential vs parallel processing performance"""
        test_contracts = enterprise_scale_contracts[:10]
        test_properties = performance_test_properties[:5]
        
        # Sequential processing
        start_time = time.perf_counter()
        sequential_results = []
        for contract in test_contracts:
            for property_info in test_properties:
                analysis = contract_analyzer.analyze_contract(contract, property_info)
                sequential_results.append(analysis)
        sequential_time = time.perf_counter() - start_time
        
        # Parallel processing simulation (using ThreadPoolExecutor)
        start_time = time.perf_counter()
        parallel_results = []
        
        def analyze_contract_wrapper(args):
            contract, property_info = args
            return contract_analyzer.analyze_contract(contract, property_info)
        
        tasks = [(contract, prop) for contract in test_contracts for prop in test_properties]
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(analyze_contract_wrapper, task) for task in tasks]
            for future in as_completed(futures):
                parallel_results.append(future.result())
        
        parallel_time = time.perf_counter() - start_time
        
        print(f"Sequential: {sequential_time:.2f}s, Parallel: {parallel_time:.2f}s")
        print(f"Speedup: {sequential_time/parallel_time:.2f}x")
        
        # Verify same number of results
        assert len(sequential_results) == len(parallel_results)
        # Parallel should be faster (though results may vary based on system)
        assert parallel_time <= sequential_time * 1.2  # Allow some variance


class TestConcurrentUserSimulation:
    """Test performance under concurrent user load"""
    
    def test_concurrent_analysis_requests(self, contract_analyzer, realistic_gfl_contract, sample_property_garden_style, performance_benchmarks):
        """Simulate concurrent users performing contract analysis"""
        num_users = 20  # Scaled down for unit testing
        requests_per_user = 5
        
        def user_simulation(user_id: int) -> List[float]:
            """Simulate a user performing multiple analyses"""
            user_times = []
            for request_num in range(requests_per_user):
                start_time = time.perf_counter()
                analysis = contract_analyzer.analyze_contract(realistic_gfl_contract, sample_property_garden_style)
                end_time = time.perf_counter()
                
                request_time = (end_time - start_time) * 1000
                user_times.append(request_time)
                
                # Verify analysis quality under load
                assert 'overall_grade' in analysis
                assert analysis['metrics']['cost_per_door'] > 0
                
                # Small delay to simulate user think time
                time.sleep(0.01)
            
            return user_times
        
        # Run concurrent user simulation
        start_time = time.perf_counter()
        all_times = []
        
        with ThreadPoolExecutor(max_workers=num_users) as executor:
            futures = [executor.submit(user_simulation, i) for i in range(num_users)]
            for future in as_completed(futures):
                user_times = future.result()
                all_times.extend(user_times)
        
        total_time = time.perf_counter() - start_time
        
        # Performance statistics
        avg_response_time = statistics.mean(all_times)
        p95_response_time = statistics.quantiles(all_times, n=20)[18]
        total_requests = len(all_times)
        requests_per_second = total_requests / total_time
        
        print(f"Concurrent Load - {num_users} users, {total_requests} requests")
        print(f"Avg Response: {avg_response_time:.2f}ms, 95th: {p95_response_time:.2f}ms")
        print(f"Throughput: {requests_per_second:.1f} requests/sec")
        
        # Performance assertions
        assert avg_response_time < performance_benchmarks['single_analysis_max_ms']
        assert p95_response_time < performance_benchmarks['single_analysis_max_ms'] * 2
        assert requests_per_second >= 50  # Should handle at least 50 requests/sec
    
    def test_resource_usage_under_load(self, contract_analyzer, enterprise_scale_contracts, performance_test_properties, performance_monitor, performance_benchmarks):
        """Test resource usage under sustained load"""
        performance_monitor.start_monitoring()
        
        def load_generator():
            """Generate sustained load"""
            contract = enterprise_scale_contracts[0]
            property_info = performance_test_properties[0]
            
            for _ in range(50):
                analysis = contract_analyzer.analyze_contract(contract, property_info)
                assert 'overall_grade' in analysis
                time.sleep(0.01)  # Small delay
        
        # Run multiple load generators concurrently
        num_generators = 10
        
        with ThreadPoolExecutor(max_workers=num_generators) as executor:
            futures = [executor.submit(load_generator) for _ in range(num_generators)]
            for future in as_completed(futures):
                future.result()
        
        performance_stats = performance_monitor.stop_monitoring()
        
        print(f"Resource Usage - CPU Avg: {performance_stats['cpu_avg']:.1f}%, Memory: {performance_stats['memory_max_mb']:.1f}MB")
        
        # Resource limits
        assert performance_stats['cpu_avg'] < performance_benchmarks['cpu_limit_percent']
        assert performance_stats['memory_max_mb'] < performance_benchmarks['memory_limit_mb']


class TestScalabilityLimits:
    """Test system scalability limits and breaking points"""
    
    def test_maximum_property_size(self, contract_analyzer, realistic_gfl_contract, performance_benchmarks):
        """Test analysis with extremely large properties"""
        # Test with progressively larger properties
        property_sizes = [500, 1000, 2000, 5000]
        
        for size in property_sizes:
            large_property = PropertyInfo(
                name=f"Massive Complex {size}",
                address=f"123 Massive St, Big City, TX",
                unit_count=size,
                property_type="high-rise"
            )
            
            start_time = time.perf_counter()
            analysis = contract_analyzer.analyze_contract(realistic_gfl_contract, large_property)
            end_time = time.perf_counter()
            
            analysis_time = (end_time - start_time) * 1000
            
            print(f"Property Size {size} units: {analysis_time:.2f}ms")
            
            # Verify calculations are correct
            assert analysis['property_info']['units'] == size
            assert analysis['metrics']['cost_per_door'] > 0
            
            # Performance should scale linearly (not exponentially)
            assert analysis_time < performance_benchmarks['single_analysis_max_ms']
    
    def test_maximum_concurrent_analyses(self, contract_analyzer, realistic_gfl_contract, sample_property_garden_style):
        """Test maximum number of concurrent analyses"""
        # Test with increasing concurrency levels
        concurrency_levels = [10, 25, 50, 100]
        
        for concurrency in concurrency_levels:
            def single_analysis():
                analysis = contract_analyzer.analyze_contract(realistic_gfl_contract, sample_property_garden_style)
                assert 'overall_grade' in analysis
                return time.perf_counter()
            
            start_time = time.perf_counter()
            
            with ThreadPoolExecutor(max_workers=concurrency) as executor:
                futures = [executor.submit(single_analysis) for _ in range(concurrency)]
                completion_times = [future.result() for future in as_completed(futures)]
            
            total_time = time.perf_counter() - start_time
            analyses_per_second = concurrency / total_time
            
            print(f"Concurrency {concurrency}: {analyses_per_second:.1f} analyses/sec")
            
            # Should handle reasonable concurrency levels
            assert analyses_per_second >= 10  # Minimum throughput
    
    def test_memory_leak_detection(self, contract_analyzer, enterprise_scale_contracts, performance_test_properties, performance_monitor):
        """Test for memory leaks during extended operation"""
        performance_monitor.start_monitoring()
        
        # Run extended analysis session
        contract = enterprise_scale_contracts[0]
        property_info = performance_test_properties[0]
        
        # Track memory usage over time
        memory_samples = []
        
        for i in range(1000):  # Run 1000 analyses
            analysis = contract_analyzer.analyze_contract(contract, property_info)
            assert 'overall_grade' in analysis
            
            # Sample memory every 100 analyses
            if i % 100 == 0:
                current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                memory_samples.append(current_memory)
                print(f"Analysis {i}: Memory {current_memory:.1f}MB")
        
        performance_stats = performance_monitor.stop_monitoring()
        
        # Check for memory leak (significant memory growth over time)
        if len(memory_samples) > 2:
            memory_growth = memory_samples[-1] - memory_samples[0]
            memory_growth_percent = (memory_growth / memory_samples[0]) * 100
            
            print(f"Memory Growth: {memory_growth:.1f}MB ({memory_growth_percent:.1f}%)")
            
            # Should not have significant memory growth
            assert memory_growth_percent < 50  # Less than 50% growth
            assert memory_growth < 100  # Less than 100MB growth


class TestDatabasePerformance:
    """Test database operation performance (mock implementation)"""
    
    @pytest.fixture
    def mock_database_operations(self):
        """Mock database operations for performance testing"""
        
        class MockDatabase:
            def __init__(self):
                self.query_times = []
            
            async def get_contract(self, contract_id: str) -> Dict:
                """Mock contract retrieval"""
                start_time = time.perf_counter()
                # Simulate database query time
                await asyncio.sleep(0.01)  # 10ms query time
                end_time = time.perf_counter()
                
                query_time = (end_time - start_time) * 1000
                self.query_times.append(query_time)
                
                return {
                    "id": contract_id,
                    "vendor_name": "Test Vendor",
                    "base_monthly_rate": 5000.00
                }
            
            async def save_analysis(self, analysis: Dict) -> str:
                """Mock analysis save operation"""
                start_time = time.perf_counter()
                await asyncio.sleep(0.005)  # 5ms save time
                end_time = time.perf_counter()
                
                query_time = (end_time - start_time) * 1000
                self.query_times.append(query_time)
                
                return f"analysis_{int(time.time())}"
            
            def get_average_query_time(self) -> float:
                """Get average query time in milliseconds"""
                return statistics.mean(self.query_times) if self.query_times else 0
        
        return MockDatabase()
    
    @pytest.mark.asyncio
    async def test_database_query_performance(self, mock_database_operations, performance_benchmarks):
        """Test database query performance"""
        
        # Test multiple contract retrievals
        for i in range(100):
            contract = await mock_database_operations.get_contract(f"contract_{i}")
            assert contract["id"] == f"contract_{i}"
        
        avg_query_time = mock_database_operations.get_average_query_time()
        print(f"Average Database Query Time: {avg_query_time:.2f}ms")
        
        # Database queries should be fast
        assert avg_query_time < performance_benchmarks['database_query_avg_ms']
    
    @pytest.mark.asyncio
    async def test_concurrent_database_operations(self, mock_database_operations, performance_benchmarks):
        """Test concurrent database operations"""
        
        async def database_workload():
            """Simulate database workload"""
            tasks = []
            for i in range(10):
                tasks.append(mock_database_operations.get_contract(f"contract_{i}"))
                tasks.append(mock_database_operations.save_analysis({"analysis": f"test_{i}"}))
            
            await asyncio.gather(*tasks)
        
        # Run concurrent database workloads
        start_time = time.perf_counter()
        
        workload_tasks = [database_workload() for _ in range(5)]
        await asyncio.gather(*workload_tasks)
        
        total_time = time.perf_counter() - start_time
        avg_query_time = mock_database_operations.get_average_query_time()
        
        print(f"Concurrent DB Operations - Total: {total_time:.2f}s, Avg Query: {avg_query_time:.2f}ms")
        
        # Concurrent operations should not significantly degrade performance
        assert avg_query_time < performance_benchmarks['database_query_avg_ms'] * 1.5
        assert total_time < 5.0  # Should complete in reasonable time


class TestAPIEndpointPerformance:
    """Test API endpoint performance (mock implementation)"""
    
    def test_health_check_performance_under_load(self):
        """Test health check endpoint performance under load"""
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        def make_health_request():
            """Make health check request and measure time"""
            start_time = time.perf_counter()
            response = client.get("/health")
            end_time = time.perf_counter()
            
            return {
                'status_code': response.status_code,
                'response_time_ms': (end_time - start_time) * 1000
            }
        
        # Test concurrent health checks
        results = []
        
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = [executor.submit(make_health_request) for _ in range(100)]
            for future in as_completed(futures):
                results.append(future.result())
        
        # Analyze results
        response_times = [r['response_time_ms'] for r in results]
        successful_requests = [r for r in results if r['status_code'] == 200]
        
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]
        success_rate = len(successful_requests) / len(results) * 100
        
        print(f"Health Check Under Load - Avg: {avg_response_time:.2f}ms, 95th: {p95_response_time:.2f}ms")
        print(f"Success Rate: {success_rate:.1f}%")
        
        # Performance assertions
        assert avg_response_time < 100  # Should be very fast
        assert p95_response_time < 200
        assert success_rate == 100  # All requests should succeed
    
    def test_api_response_consistency_under_load(self):
        """Test API response consistency under load"""
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        # Make multiple requests and check response consistency
        responses = []
        for _ in range(50):
            response = client.get("/health")
            responses.append(response.json())
        
        # All responses should have same structure and service name
        first_response = responses[0]
        
        for response in responses:
            assert response["service"] == first_response["service"]
            assert response["version"] == first_response["version"]
            assert response["status"] == first_response["status"]
            assert "timestamp" in response


class TestSystemResourceLimits:
    """Test system resource limits and optimization"""
    
    def test_cpu_efficiency(self, contract_analyzer, enterprise_scale_contracts, performance_test_properties, performance_monitor, performance_benchmarks):
        """Test CPU efficiency during analysis operations"""
        performance_monitor.start_monitoring()
        
        # CPU-intensive workload
        contracts = enterprise_scale_contracts[:10]
        properties = performance_test_properties[:10]
        
        analyses_completed = 0
        start_time = time.perf_counter()
        
        for contract in contracts:
            for property_info in properties:
                analysis = contract_analyzer.analyze_contract(contract, property_info)
                analyses_completed += 1
                assert 'overall_grade' in analysis
        
        end_time = time.perf_counter()
        performance_stats = performance_monitor.stop_monitoring()
        
        total_time = end_time - start_time
        analyses_per_second = analyses_completed / total_time
        cpu_efficiency = analyses_per_second / performance_stats['cpu_avg'] if performance_stats['cpu_avg'] > 0 else 0
        
        print(f"CPU Efficiency - {analyses_per_second:.1f} analyses/sec at {performance_stats['cpu_avg']:.1f}% CPU")
        print(f"Efficiency Ratio: {cpu_efficiency:.2f} analyses/sec per % CPU")
        
        # CPU usage should be reasonable
        assert performance_stats['cpu_avg'] < performance_benchmarks['cpu_limit_percent']
        assert cpu_efficiency > 1  # Should process more than 1 analysis per % CPU
    
    def test_memory_efficiency(self, contract_analyzer, enterprise_scale_contracts, performance_test_properties, performance_monitor):
        """Test memory efficiency and garbage collection"""
        performance_monitor.start_monitoring()
        
        # Generate many analyses to test memory management
        analyses = []
        
        for i in range(200):
            contract = enterprise_scale_contracts[i % len(enterprise_scale_contracts)]
            property_info = performance_test_properties[i % len(performance_test_properties)]
            
            analysis = contract_analyzer.analyze_contract(contract, property_info)
            analyses.append(analysis)
            
            # Periodically clear old analyses to simulate real usage
            if len(analyses) > 50:
                analyses = analyses[-25:]  # Keep only recent analyses
        
        performance_stats = performance_monitor.stop_monitoring()
        
        memory_per_analysis = performance_stats['memory_avg_mb'] / 200
        
        print(f"Memory Efficiency - {performance_stats['memory_avg_mb']:.1f}MB avg for 200 analyses")
        print(f"Memory per analysis: {memory_per_analysis:.3f}MB")
        
        # Memory usage should be efficient
        assert memory_per_analysis < 1.0  # Less than 1MB per analysis
        assert performance_stats['memory_max_mb'] < 200  # Reasonable total memory