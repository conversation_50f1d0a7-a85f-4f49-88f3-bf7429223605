"""
Renewal API Tests
================

Comprehensive tests for renewal management API endpoints.
Tests authentication, validation, business logic, and error handling.
"""

import pytest
from datetime import date, datetime, timedelta
from decimal import Decimal
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock

from backend.src.main import app
from backend.src.core.security import create_access_token, UserRole
from backend.src.schemas.renewal import RenewalStatus, PriorityLevel


@pytest.fixture
def client():
    """Test client fixture"""
    return TestClient(app)


@pytest.fixture
def property_manager_token():
    """Property manager authentication token"""
    token_data = {
        "sub": "user-pm-001",
        "email": "<EMAIL>",
        "role": UserRole.PROPERTY_MANAGER,
        "property_ids": ["PROP-001", "PROP-002"],
        "region_id": "REG-SE"
    }
    return create_access_token(token_data)


@pytest.fixture
def regional_director_token():
    """Regional director authentication token"""
    token_data = {
        "sub": "user-rd-001", 
        "email": "<EMAIL>",
        "role": UserRole.REGIONAL_DIRECTOR,
        "property_ids": [],
        "region_id": "REG-SE"
    }
    return create_access_token(token_data)


@pytest.fixture
def executive_token():
    """Executive authentication token"""
    token_data = {
        "sub": "user-exec-001",
        "email": "<EMAIL>", 
        "role": UserRole.EXECUTIVE,
        "property_ids": [],
        "region_id": None
    }
    return create_access_token(token_data)


class TestUpcomingRenewals:
    """Tests for GET /api/renewals/upcoming endpoint"""
    
    def test_get_upcoming_renewals_success(self, client, property_manager_token):
        """Test successful retrieval of upcoming renewals"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        response = client.get("/api/renewals/upcoming", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "renewals" in data
        assert "total_count" in data
        assert "metrics" in data
        assert isinstance(data["renewals"], list)
        
        # Validate renewal item structure
        if data["renewals"]:
            renewal = data["renewals"][0]
            required_fields = [
                "contract_id", "property_name", "vendor_name", 
                "expiry_date", "days_to_expiry", "monthly_cost",
                "priority", "status"
            ]
            for field in required_fields:
                assert field in renewal
    
    def test_get_upcoming_renewals_with_filters(self, client, property_manager_token):
        """Test upcoming renewals with query filters"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        params = {
            "days_ahead": 120,
            "priority_filter": PriorityLevel.HIGH,
            "status_filter": RenewalStatus.PENDING_REVIEW,
            "limit": 25,
            "offset": 0
        }
        
        response = client.get("/api/renewals/upcoming", headers=headers, params=params)
        
        assert response.status_code == 200
        data = response.json()
        
        # Should respect pagination limit
        assert len(data["renewals"]) <= 25
        
        # All renewals should match priority filter (if any returned)
        for renewal in data["renewals"]:
            if renewal.get("priority"):
                assert renewal["priority"] == PriorityLevel.HIGH
    
    def test_get_upcoming_renewals_unauthorized(self, client):
        """Test unauthorized access to upcoming renewals"""
        response = client.get("/api/renewals/upcoming")
        assert response.status_code == 401
    
    def test_get_upcoming_renewals_invalid_parameters(self, client, property_manager_token):
        """Test with invalid query parameters"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        # Invalid days_ahead (too small)
        response = client.get("/api/renewals/upcoming?days_ahead=10", headers=headers)
        assert response.status_code == 422
        
        # Invalid limit (too large)
        response = client.get("/api/renewals/upcoming?limit=1000", headers=headers)
        assert response.status_code == 422


class TestContractAnalysis:
    """Tests for POST /api/renewals/{contract_id}/analyze endpoint"""
    
    def test_analyze_contract_success(self, client, property_manager_token):
        """Test successful contract analysis"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        analysis_request = {
            "contract_id": "CNT-001",
            "include_benchmarks": True,
            "include_alternatives": True,
            "analysis_depth": "standard"
        }
        
        response = client.post(
            "/api/renewals/CNT-001/analyze",
            headers=headers,
            json=analysis_request
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate analysis structure
        required_fields = [
            "analysis_id", "contract_id", "analysis_date", "analyzed_by",
            "current_contract", "property", "vendor", "cost_analysis",
            "benchmarks", "renewal_recommendation", "confidence_score",
            "next_steps"
        ]
        for field in required_fields:
            assert field in data
        
        # Validate cost analysis structure
        cost_analysis = data["cost_analysis"]
        cost_fields = [
            "current_monthly_cost", "current_cost_per_door", "current_cost_per_yard",
            "projected_monthly_cost", "savings_opportunity", "savings_percentage"
        ]
        for field in cost_fields:
            assert field in cost_analysis
    
    def test_analyze_contract_not_found(self, client, property_manager_token):
        """Test analysis of non-existent contract"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        response = client.post(
            "/api/renewals/CNT-999/analyze",
            headers=headers,
            json={"contract_id": "CNT-999"}
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()
    
    def test_analyze_contract_comprehensive(self, client, regional_director_token):
        """Test comprehensive analysis (background task trigger)"""
        headers = {"Authorization": f"Bearer {regional_director_token}"}
        
        analysis_request = {
            "contract_id": "CNT-001",
            "include_benchmarks": True,
            "include_alternatives": True,
            "analysis_depth": "comprehensive"
        }
        
        with patch('backend.src.api.renewals.run_comprehensive_analysis') as mock_bg_task:
            response = client.post(
                "/api/renewals/CNT-001/analyze",
                headers=headers,
                json=analysis_request
            )
            
            assert response.status_code == 200
            # Verify background task was scheduled
            mock_bg_task.assert_called_once()


class TestNotificationHistory:
    """Tests for GET /api/renewals/{contract_id}/notification-history endpoint"""
    
    def test_get_notification_history_success(self, client, property_manager_token):
        """Test successful retrieval of notification history"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        response = client.get(
            "/api/renewals/CNT-001/notification-history",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        
        # Validate notification structure
        if data:
            notification = data[0]
            required_fields = [
                "notification_id", "contract_id", "recipient",
                "notification_type", "sent_date", "delivery_status",
                "content_summary"
            ]
            for field in required_fields:
                assert field in notification
    
    def test_get_notification_history_with_limit(self, client, property_manager_token):
        """Test notification history with limit parameter"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        response = client.get(
            "/api/renewals/CNT-001/notification-history?limit=10",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should respect limit
        assert len(data) <= 10


class TestDashboardMetrics:
    """Tests for GET /api/renewals/dashboard endpoint"""
    
    def test_get_dashboard_metrics_success(self, client, executive_token):
        """Test successful retrieval of dashboard metrics"""
        headers = {"Authorization": f"Bearer {executive_token}"}
        
        response = client.get("/api/renewals/dashboard", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate metrics structure
        required_fields = [
            "total_contracts_expiring", "total_monthly_value_at_risk",
            "potential_annual_savings", "average_cost_per_door",
            "contracts_under_review", "high_priority_renewals",
            "completion_rate"
        ]
        for field in required_fields:
            assert field in data
            assert isinstance(data[field], (int, float, str))
    
    def test_get_dashboard_metrics_regional_filter(self, client, regional_director_token):
        """Test dashboard metrics with regional filtering"""
        headers = {"Authorization": f"Bearer {regional_director_token}"}
        
        response = client.get(
            "/api/renewals/dashboard?region_filter=REG-SE",
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "total_contracts_expiring" in data
    
    @patch('backend.src.api.renewals.cache')
    def test_dashboard_metrics_caching(self, mock_cache, client, executive_token):
        """Test that dashboard metrics are properly cached"""
        headers = {"Authorization": f"Bearer {executive_token}"}
        
        # First request
        response1 = client.get("/api/renewals/dashboard", headers=headers)
        assert response1.status_code == 200
        
        # Second request should hit cache
        response2 = client.get("/api/renewals/dashboard", headers=headers)
        assert response2.status_code == 200


class TestRenewalReports:
    """Tests for GET /api/renewals/reports endpoint"""
    
    def test_generate_executive_summary_report(self, client, executive_token):
        """Test executive summary report generation"""
        headers = {"Authorization": f"Bearer {executive_token}"}
        
        params = {
            "report_type": "executive_summary",
            "include_projections": True,
            "format": "json"
        }
        
        response = client.get("/api/renewals/reports", headers=headers, params=params)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert "data" in data
        assert "report_id" in data["data"]
        assert data["data"]["report_type"] == "executive_summary"
    
    def test_generate_report_with_date_range(self, client, executive_token):
        """Test report generation with date range"""
        headers = {"Authorization": f"Bearer {executive_token}"}
        
        params = {
            "report_type": "cost_comparison",
            "date_range_start": "2025-01-01",
            "date_range_end": "2025-12-31",
            "format": "pdf"
        }
        
        response = client.get("/api/renewals/reports", headers=headers, params=params)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["success"] is True
        assert data["data"]["format"] == "pdf"
    
    def test_generate_report_insufficient_permissions(self, client, property_manager_token):
        """Test report generation with insufficient permissions"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        params = {"report_type": "executive_summary"}
        
        response = client.get("/api/renewals/reports", headers=headers, params=params)
        
        # Property managers should not have access to executive reports
        assert response.status_code == 403


class TestErrorHandling:
    """Tests for error handling and edge cases"""
    
    def test_rate_limiting(self, client, property_manager_token):
        """Test rate limiting functionality"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        # Make multiple rapid requests to trigger rate limiting
        # (Note: This test would need actual rate limiting to be enabled)
        for _ in range(5):
            response = client.get("/api/renewals/upcoming", headers=headers)
            # In a real test, we'd expect some requests to return 429
    
    def test_malformed_request_body(self, client, property_manager_token):
        """Test handling of malformed request bodies"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        # Invalid JSON
        response = client.post(
            "/api/renewals/CNT-001/analyze",
            headers=headers,
            data="invalid json"
        )
        
        assert response.status_code == 422
    
    def test_sql_injection_protection(self, client, property_manager_token):
        """Test protection against SQL injection attempts"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        malicious_params = {
            "days_ahead": "90; DROP TABLE contracts; --"
        }
        
        response = client.get(
            "/api/renewals/upcoming", 
            headers=headers, 
            params=malicious_params
        )
        
        # Should return validation error, not process malicious SQL
        assert response.status_code == 422
    
    def test_large_request_body_handling(self, client, property_manager_token):
        """Test handling of extremely large request bodies"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        # Create a very large request body
        large_data = {"message": "x" * 1000000}  # 1MB of data
        
        response = client.post(
            "/api/renewals/CNT-001/analyze",
            headers=headers,
            json=large_data
        )
        
        # Should handle gracefully (either process or reject cleanly)
        assert response.status_code in [200, 413, 422]


class TestBusinessLogicValidation:
    """Tests for business logic validation"""
    
    def test_contract_expiry_date_validation(self, client, property_manager_token):
        """Test validation of contract expiry dates"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        # Test with invalid date range (end before start)
        params = {
            "days_ahead": 30,  # Looking 30 days ahead
            "limit": 10
        }
        
        response = client.get("/api/renewals/upcoming", headers=headers, params=params)
        
        # Should handle edge cases gracefully
        assert response.status_code in [200, 400]
    
    def test_cost_analysis_validation(self, client, property_manager_token):
        """Test cost analysis business rules"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        response = client.post(
            "/api/renewals/CNT-001/analyze",
            headers=headers,
            json={"contract_id": "CNT-001"}
        )
        
        if response.status_code == 200:
            data = response.json()
            cost_analysis = data["cost_analysis"]
            
            # Validate business rules
            assert float(cost_analysis["current_monthly_cost"]) > 0
            assert float(cost_analysis["current_cost_per_door"]) > 0
            assert -100 <= float(cost_analysis["savings_percentage"]) <= 100
    
    def test_industry_benchmark_validation(self, client, property_manager_token):
        """Test industry benchmark calculations"""
        headers = {"Authorization": f"Bearer {property_manager_token}"}
        
        response = client.post(
            "/api/renewals/CNT-001/analyze",
            headers=headers,
            json={
                "contract_id": "CNT-001",
                "include_benchmarks": True
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            benchmarks = data["benchmarks"]
            
            for benchmark in benchmarks:
                # Validate benchmark structure and ranges
                assert 0 <= benchmark["industry_percentile"] <= 100
                assert float(benchmark["industry_average"]) > 0
                assert float(benchmark["best_in_class"]) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])