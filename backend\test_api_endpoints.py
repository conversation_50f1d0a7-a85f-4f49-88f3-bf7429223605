#!/usr/bin/env python3
"""
API Endpoint Test Script
=======================

Quick validation script to test the Advantage Waste Enterprise API endpoints.
Run this script to verify that the API is working correctly.
"""

import sys
import requests
import json
from datetime import datetime, date
from decimal import Decimal

# API Configuration
BASE_URL = "http://localhost:8000"
TIMEOUT = 30

def test_health_endpoint():
    """Test the health check endpoint"""
    print("Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
        print(f"✓ Health check: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  Service: {data.get('service')}")
            print(f"  Status: {data.get('status')}")
            print(f"  Version: {data.get('version')}")
        return response.status_code == 200
    except Exception as e:
        print(f"✗ Health check failed: {e}")
        return False

def test_root_endpoint():
    """Test the root endpoint"""
    print("\nTesting root endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/", timeout=TIMEOUT)
        print(f"✓ Root endpoint: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  Message: {data.get('message')}")
            print(f"  Version: {data.get('version')}")
            print(f"  Capabilities: {len(data.get('capabilities', []))} features")
        return response.status_code == 200
    except Exception as e:
        print(f"✗ Root endpoint failed: {e}")
        return False

def test_openapi_docs():
    """Test OpenAPI documentation endpoints"""
    print("\nTesting documentation endpoints...")
    
    endpoints = [
        ("/docs", "Swagger UI"),
        ("/redoc", "ReDoc"),
        ("/openapi.json", "OpenAPI Schema")
    ]
    
    success = True
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=TIMEOUT)
            print(f"✓ {name}: {response.status_code}")
            if response.status_code != 200:
                success = False
        except Exception as e:
            print(f"✗ {name} failed: {e}")
            success = False
    
    return success

def create_mock_token():
    """Create a mock JWT token for testing"""
    # Note: This creates a token with test data
    # In production, you would get this from authentication
    from backend.src.core.security import create_access_token
    
    token_data = {
        "sub": "test-user-001",
        "email": "<EMAIL>",
        "role": "property_manager",
        "property_ids": ["PROP-001", "PROP-002"],
        "region_id": "REG-TEST"
    }
    
    return create_access_token(token_data)

def test_renewals_endpoints():
    """Test renewal management endpoints"""
    print("\nTesting renewal endpoints...")
    
    try:
        # Create mock authentication token
        token = create_mock_token()
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test upcoming renewals endpoint
        print("  Testing upcoming renewals...")
        response = requests.get(
            f"{BASE_URL}/api/renewals/upcoming",
            headers=headers,
            timeout=TIMEOUT
        )
        print(f"  ✓ Upcoming renewals: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"    Found {data.get('total_count', 0)} contracts")
            print(f"    Renewals returned: {len(data.get('renewals', []))}")
        
        # Test with query parameters
        print("  Testing with filters...")
        params = {
            "days_ahead": 120,
            "limit": 10,
            "priority_filter": "high"
        }
        response = requests.get(
            f"{BASE_URL}/api/renewals/upcoming",
            headers=headers,
            params=params,
            timeout=TIMEOUT
        )
        print(f"  ✓ Filtered renewals: {response.status_code}")
        
        # Test dashboard endpoint
        print("  Testing dashboard...")
        response = requests.get(
            f"{BASE_URL}/api/renewals/dashboard",
            headers=headers,
            timeout=TIMEOUT
        )
        print(f"  ✓ Dashboard: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"    Contracts expiring: {data.get('total_contracts_expiring')}")
            print(f"    Value at risk: ${data.get('total_monthly_value_at_risk')}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Renewals endpoints failed: {e}")
        return False

def test_analysis_endpoint():
    """Test contract analysis endpoint"""
    print("\nTesting analysis endpoint...")
    
    try:
        token = create_mock_token()
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test contract analysis
        analysis_request = {
            "contract_id": "CNT-001",
            "include_benchmarks": True,
            "include_alternatives": True,
            "analysis_depth": "standard"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/renewals/CNT-001/analyze",
            headers=headers,
            json=analysis_request,
            timeout=TIMEOUT
        )
        print(f"  ✓ Contract analysis: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"    Analysis ID: {data.get('analysis_id')}")
            print(f"    Recommendation: {data.get('renewal_recommendation')}")
            print(f"    Confidence: {data.get('confidence_score')}%")
            
            cost_analysis = data.get('cost_analysis', {})
            savings = cost_analysis.get('savings_opportunity', '0')
            print(f"    Potential savings: ${savings}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Analysis endpoint failed: {e}")
        return False

def test_notifications_endpoints():
    """Test notification management endpoints"""
    print("\nTesting notification endpoints...")
    
    try:
        token = create_mock_token()
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test get preferences
        print("  Testing get preferences...")
        response = requests.get(
            f"{BASE_URL}/api/notifications/preferences",
            headers=headers,
            timeout=TIMEOUT
        )
        print(f"  ✓ Get preferences: {response.status_code}")
        
        # Test notification history
        print("  Testing notification history...")
        response = requests.get(
            f"{BASE_URL}/api/notifications/history",
            headers=headers,
            params={"limit": 10},
            timeout=TIMEOUT
        )
        print(f"  ✓ Notification history: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"    History items: {len(data)}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Notifications endpoints failed: {e}")
        return False

def test_authentication_errors():
    """Test authentication and authorization errors"""
    print("\nTesting authentication handling...")
    
    try:
        # Test without authorization header
        print("  Testing unauthorized access...")
        response = requests.get(f"{BASE_URL}/api/renewals/upcoming", timeout=TIMEOUT)
        print(f"  ✓ Unauthorized: {response.status_code}")
        
        if response.status_code == 401:
            print("    ✓ Correctly rejected unauthorized request")
        
        # Test with invalid token
        print("  Testing invalid token...")
        headers = {"Authorization": "Bearer invalid-token"}
        response = requests.get(
            f"{BASE_URL}/api/renewals/upcoming",
            headers=headers,
            timeout=TIMEOUT
        )
        print(f"  ✓ Invalid token: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Authentication tests failed: {e}")
        return False

def test_error_handling():
    """Test error handling for various scenarios"""
    print("\nTesting error handling...")
    
    try:
        token = create_mock_token()
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test 404 error
        print("  Testing 404 error...")
        response = requests.get(
            f"{BASE_URL}/api/renewals/CNT-999/notification-history",
            headers=headers,
            timeout=TIMEOUT
        )
        print(f"  ✓ Not found: {response.status_code}")
        
        # Test validation error
        print("  Testing validation error...")
        response = requests.get(
            f"{BASE_URL}/api/renewals/upcoming?days_ahead=10",  # Invalid: too low
            headers=headers,
            timeout=TIMEOUT
        )
        print(f"  ✓ Validation error: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Error handling tests failed: {e}")
        return False

def run_comprehensive_tests():
    """Run all API tests"""
    print("=" * 60)
    print("ADVANTAGE WASTE ENTERPRISE API TEST SUITE")
    print("=" * 60)
    
    tests = [
        ("Health Check", test_health_endpoint),
        ("Root Endpoint", test_root_endpoint),
        ("Documentation", test_openapi_docs),
        ("Renewals Endpoints", test_renewals_endpoints),
        ("Analysis Endpoint", test_analysis_endpoint),
        ("Notifications", test_notifications_endpoints),
        ("Authentication", test_authentication_errors),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{test_name:.<40} {status}")
    
    print("-" * 60)
    print(f"Total: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! API is working correctly.")
        return True
    else:
        print(f"⚠️  {total - passed} tests failed. Check the API implementation.")
        return False

def check_server_running():
    """Check if the API server is running"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    print("Advantage Waste Enterprise API Test Script")
    print(f"Testing API at: {BASE_URL}")
    print()
    
    # Check if server is running
    if not check_server_running():
        print("❌ API server is not running or not accessible.")
        print(f"Please start the server with: uvicorn backend.src.main:app --reload")
        print(f"Expected URL: {BASE_URL}")
        sys.exit(1)
    
    # Run tests
    success = run_comprehensive_tests()
    
    if success:
        print("\n🚀 API is ready for development!")
        sys.exit(0)
    else:
        print("\n🔧 API needs attention before deployment.")
        sys.exit(1)