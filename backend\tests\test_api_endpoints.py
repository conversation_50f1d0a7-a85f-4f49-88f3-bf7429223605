"""
API Integration Tests
=====================

Comprehensive integration tests for all FastAPI endpoints.
Tests authentication, validation, error handling, and response formats.

Tests cover:
- Health check and system endpoints
- Contract analysis API endpoints
- Property management endpoints
- Vendor API endpoints
- Analytics and reporting endpoints
- Authentication and authorization
- Input validation and error handling
- Response format validation
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from fastapi import status
from unittest.mock import Mock, AsyncMock, patch
import json
from decimal import Decimal
from datetime import datetime

# Import the FastAPI app
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from main import app


@pytest.fixture
def client():
    """FastAPI test client"""
    return TestClient(app)


@pytest.fixture
def auth_headers():
    """Mock authentication headers"""
    return {
        "Authorization": "Bearer test-token",
        "Content-Type": "application/json"
    }


class TestSystemEndpoints:
    """Test system health and status endpoints"""
    
    def test_health_check_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "advantage-waste-enterprise-api"
        assert data["version"] == "1.0.0"
        assert "timestamp" in data
        assert isinstance(data["timestamp"], (int, float))
    
    def test_root_endpoint(self, client):
        """Test root API information endpoint"""
        response = client.get("/")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data["message"] == "Advantage Waste Enterprise API"
        assert data["version"] == "1.0.0"
        assert data["docs_url"] == "/docs"
        assert data["redoc_url"] == "/redoc"
        assert "capabilities" in data
        assert len(data["capabilities"]) > 0
    
    def test_docs_endpoint_accessibility(self, client):
        """Test that API documentation is accessible"""
        # Test OpenAPI docs
        response = client.get("/docs")
        assert response.status_code == status.HTTP_200_OK
        
        # Test ReDoc docs  
        response = client.get("/redoc")
        assert response.status_code == status.HTTP_200_OK
        
        # Test OpenAPI JSON schema
        response = client.get("/openapi.json")
        assert response.status_code == status.HTTP_200_OK
        
        schema = response.json()
        assert "openapi" in schema
        assert "info" in schema
        assert schema["info"]["title"] == "Advantage Waste Enterprise API"


class TestContractAnalysisEndpoints:
    """Test contract analysis API endpoints"""
    
    @pytest.fixture
    def sample_contract_data(self):
        """Sample contract data for API testing"""
        return {
            "vendor_name": "Test Waste Management",
            "contact_name": "John Doe",
            "contact_info": "<EMAIL>",
            "quote_number": "TEST-2024-001",
            "contract_length_months": 24,
            "effective_date": "2024-01-01T00:00:00",
            "automatic_renewal": True,
            "renewal_term_months": 12,
            "termination_notice_days": 60,
            "container_size_yards": 34,
            "container_type": "compactor",
            "container_quantity": 1,
            "pickup_frequency_weekly": 1,
            "base_monthly_rate": 5000.00,
            "fuel_surcharge_percent": 5.0,
            "environmental_fee_percent": 3.0,
            "max_annual_increase_percent": 4.0
        }
    
    @pytest.fixture
    def sample_property_data(self):
        """Sample property data for API testing"""
        return {
            "name": "Test Property",
            "address": "123 Test St, Test City, TX",
            "unit_count": 252,
            "property_type": "garden-style"
        }
    
    def test_contract_analysis_endpoint_structure(self, client):
        """Test that contract analysis endpoint exists (when implemented)"""
        # This test will initially fail as the endpoint doesn't exist yet
        # But it documents the expected API structure
        
        response = client.get("/api/contracts")
        # For now, expect 404 since routes are commented out
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND]
    
    @pytest.mark.asyncio
    async def test_contract_analysis_workflow_mock(self, client, sample_contract_data, sample_property_data):
        """Test contract analysis workflow with mocked implementation"""
        
        # Mock the contract analysis endpoint behavior
        with patch('main.app') as mock_app:
            # Simulate contract analysis request
            analysis_request = {
                "contract": sample_contract_data,
                "property": sample_property_data
            }
            
            # Expected response structure
            expected_response = {
                "analysis_id": "test-analysis-001",
                "property_info": {
                    "name": sample_property_data["name"],
                    "units": sample_property_data["unit_count"],
                    "type": sample_property_data["property_type"]
                },
                "metrics": {
                    "monthly_volume_yards": 147.22,
                    "total_monthly_cost": 5400.00,
                    "cost_per_yard": 36.67,
                    "cost_per_door": 21.43,
                    "yards_per_door": 0.58,
                    "annual_cost": 64800.00
                },
                "benchmark_analysis": {
                    "cost_per_door": {
                        "value": 21.43,
                        "benchmark_range": [20, 30],
                        "status": "within_range",
                        "variance_percent": 0.0
                    },
                    "yards_per_door": {
                        "value": 0.58,
                        "benchmark_range": [2.0, 2.25],
                        "status": "outside_range",
                        "variance_percent": -71.0
                    }
                },
                "terms_analysis": {
                    "contract_length": {
                        "months": 24,
                        "status": "optimal",
                        "optimal_range": [12, 24]
                    },
                    "price_increases": {
                        "max_annual_percent": 4.0,
                        "status": "good",
                        "benchmark_max": 4.0
                    },
                    "fuel_surcharge": {
                        "percent": 5.0,
                        "status": "good",
                        "benchmark_max": 5.0
                    }
                },
                "overall_grade": "B",
                "recommendations": [
                    "Contract length is optimal for flexibility",
                    "Price increase protection is good",
                    "Consider reducing environmental fees"
                ],
                "analysis_date": "2024-01-01T12:00:00Z"
            }
            
            # Validate request structure would be properly formatted
            assert "contract" in analysis_request
            assert "property" in analysis_request
            assert analysis_request["contract"]["vendor_name"] == sample_contract_data["vendor_name"]
            assert analysis_request["property"]["unit_count"] == sample_property_data["unit_count"]
            
            # Validate expected response structure
            assert "analysis_id" in expected_response
            assert "metrics" in expected_response
            assert "benchmark_analysis" in expected_response
            assert "terms_analysis" in expected_response
            assert "overall_grade" in expected_response
            assert "recommendations" in expected_response


class TestInputValidation:
    """Test input validation for API endpoints"""
    
    def test_invalid_contract_data_validation(self, client):
        """Test validation of invalid contract data"""
        
        invalid_contracts = [
            # Missing required fields
            {
                "vendor_name": "Test Vendor"
                # Missing all other required fields
            },
            # Invalid data types
            {
                "vendor_name": "Test Vendor",
                "contract_length_months": "invalid",  # Should be int
                "base_monthly_rate": "not-a-number",  # Should be float
                "unit_count": -5  # Should be positive
            },
            # Invalid ranges
            {
                "vendor_name": "Test Vendor",
                "contract_length_months": -12,  # Should be positive
                "pickup_frequency_weekly": 0,   # Should be at least 1
                "container_size_yards": -10,    # Should be positive
                "fuel_surcharge_percent": -5.0  # Should be non-negative
            },
            # Invalid enum values
            {
                "vendor_name": "Test Vendor",
                "container_type": "invalid-type",  # Should be valid container type
                "property_type": "invalid-property"  # Should be valid property type
            }
        ]
        
        for invalid_contract in invalid_contracts:
            # Mock API call - would return validation errors
            # For now, just validate the test data structure
            assert "vendor_name" in invalid_contract
    
    def test_property_data_validation(self, client):
        """Test validation of property data"""
        
        invalid_properties = [
            # Missing required fields
            {
                "name": "Test Property"
                # Missing address, unit_count, property_type
            },
            # Invalid unit count
            {
                "name": "Test Property",
                "address": "123 Test St",
                "unit_count": 0,  # Should be positive
                "property_type": "garden-style"
            },
            # Invalid property type
            {
                "name": "Test Property", 
                "address": "123 Test St",
                "unit_count": 100,
                "property_type": "invalid-type"  # Should be valid type
            }
        ]
        
        for invalid_property in invalid_properties:
            # Mock validation - would return 422 Unprocessable Entity
            assert "name" in invalid_property


class TestErrorHandling:
    """Test error handling and HTTP status codes"""
    
    def test_404_not_found_handling(self, client):
        """Test 404 handling for non-existent endpoints"""
        response = client.get("/api/nonexistent")
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_405_method_not_allowed_handling(self, client):
        """Test 405 handling for wrong HTTP methods"""
        # Try POST on GET endpoint
        response = client.post("/health")
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
    
    def test_global_exception_handler(self, client):
        """Test global exception handler"""
        # This tests the middleware exception handling
        # The actual endpoint would need to be implemented and cause an exception
        
        # For now, test that the exception handler structure exists
        # by checking that unhandled endpoints return proper error format
        response = client.get("/api/force-error")  # Non-existent endpoint
        assert response.status_code in [404, 500]
    
    def test_cors_headers(self, client):
        """Test CORS headers are properly set"""
        response = client.options("/health")
        
        # CORS should be configured, but specific headers depend on request origin
        # At minimum, the request should not be blocked
        assert response.status_code in [200, 405]  # Either allowed or method not allowed


class TestResponseFormats:
    """Test API response formats and consistency"""
    
    def test_health_response_format(self, client):
        """Test health check response format consistency"""
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "application/json"
        
        data = response.json()
        
        # Required fields
        required_fields = ["status", "service", "version", "timestamp"]
        for field in required_fields:
            assert field in data
            assert data[field] is not None
        
        # Field types
        assert isinstance(data["status"], str)
        assert isinstance(data["service"], str)
        assert isinstance(data["version"], str)
        assert isinstance(data["timestamp"], (int, float))
    
    def test_error_response_format(self, client):
        """Test error response format consistency"""
        response = client.get("/api/nonexistent")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        
        # FastAPI default error format
        data = response.json()
        assert "detail" in data
    
    def test_json_content_type(self, client):
        """Test that all endpoints return proper JSON content type"""
        endpoints = ["/", "/health"]
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            if response.status_code == 200:
                assert "application/json" in response.headers.get("content-type", "")


class TestAuthenticationIntegration:
    """Test authentication and authorization integration"""
    
    def test_protected_endpoint_without_auth(self, client):
        """Test accessing protected endpoints without authentication"""
        # These endpoints would require authentication when implemented
        protected_endpoints = [
            "/api/contracts/analysis",
            "/api/properties",
            "/api/vendors",
            "/api/analytics/reports"
        ]
        
        for endpoint in protected_endpoints:
            response = client.get(endpoint)
            # Should return 401 Unauthorized or 404 (if not implemented)
            assert response.status_code in [
                status.HTTP_401_UNAUTHORIZED,
                status.HTTP_404_NOT_FOUND
            ]
    
    def test_protected_endpoint_with_invalid_auth(self, client):
        """Test accessing protected endpoints with invalid authentication"""
        invalid_headers = [
            {"Authorization": "Bearer invalid-token"},
            {"Authorization": "InvalidScheme token"},
            {"Authorization": "Bearer "},  # Empty token
        ]
        
        protected_endpoints = ["/api/contracts/analysis"]
        
        for headers in invalid_headers:
            for endpoint in protected_endpoints:
                response = client.get(endpoint, headers=headers)
                # Should return 401 Unauthorized or 404 (if not implemented)
                assert response.status_code in [
                    status.HTTP_401_UNAUTHORIZED,
                    status.HTTP_404_NOT_FOUND
                ]
    
    def test_protected_endpoint_with_valid_auth(self, client, auth_headers):
        """Test accessing protected endpoints with valid authentication"""
        # Mock valid authentication
        protected_endpoints = ["/api/contracts/analysis"]
        
        for endpoint in protected_endpoints:
            response = client.get(endpoint, headers=auth_headers)
            # Should return 200 OK or 404 (if not implemented)
            # But NOT 401 Unauthorized
            assert response.status_code != status.HTTP_401_UNAUTHORIZED


class TestPerformanceEndpoints:
    """Test API endpoint performance characteristics"""
    
    def test_health_check_response_time(self, client):
        """Test health check endpoint response time"""
        import time
        
        start_time = time.time()
        response = client.get("/health")
        end_time = time.time()
        
        response_time = end_time - start_time
        
        assert response.status_code == status.HTTP_200_OK
        assert response_time < 0.1  # Should respond in under 100ms
    
    def test_concurrent_health_checks(self, client):
        """Test multiple concurrent health check requests"""
        import threading
        import time
        
        results = []
        
        def make_request():
            start_time = time.time()
            response = client.get("/health")
            end_time = time.time()
            results.append({
                "status_code": response.status_code,
                "response_time": end_time - start_time
            })
        
        # Make 10 concurrent requests
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert len(results) == 10
        for result in results:
            assert result["status_code"] == status.HTTP_200_OK
            assert result["response_time"] < 0.5  # Even under load
    
    def test_process_time_header(self, client):
        """Test that process time header is added"""
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        assert "X-Process-Time" in response.headers
        
        process_time = float(response.headers["X-Process-Time"])
        assert process_time > 0
        assert process_time < 1.0  # Should be very fast


class TestRequestLogging:
    """Test request logging middleware"""
    
    @patch('main.logger')
    def test_request_logging_middleware(self, mock_logger, client):
        """Test that requests are properly logged"""
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        
        # Verify logger was called for request start and completion
        assert mock_logger.info.call_count >= 2
        
        # Check that log calls include expected information
        log_calls = mock_logger.info.call_args_list
        
        # Should log request start
        start_call = log_calls[0]
        assert "Request started" in str(start_call)
        
        # Should log request completion
        completion_call = log_calls[1]
        assert "Request completed" in str(completion_call)


class TestContentNegotiation:
    """Test content negotiation and format handling"""
    
    def test_json_accept_header(self, client):
        """Test JSON content negotiation"""
        headers = {"Accept": "application/json"}
        response = client.get("/health", headers=headers)
        
        assert response.status_code == status.HTTP_200_OK
        assert "application/json" in response.headers["content-type"]
        
        # Should be valid JSON
        data = response.json()
        assert isinstance(data, dict)
    
    def test_unsupported_content_type_request(self, client):
        """Test handling of unsupported content types"""
        # Most endpoints should only accept JSON
        headers = {"Content-Type": "text/xml"}
        
        # GET requests typically don't check content-type, but POST would
        response = client.get("/health", headers=headers)
        
        # Should still work for GET requests
        assert response.status_code == status.HTTP_200_OK


class TestSecurityHeaders:
    """Test security headers and configurations"""
    
    def test_security_headers_present(self, client):
        """Test that appropriate security headers are present"""
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        
        # Check for security headers (these would be added by middleware)
        headers = response.headers
        
        # These headers would be added by security middleware when implemented
        # For now, just test that the response doesn't expose sensitive info
        assert "Server" not in headers or "FastAPI" not in headers.get("Server", "")
        
        # Process time header should be present (added by middleware)
        assert "X-Process-Time" in headers
    
    def test_no_sensitive_info_in_errors(self, client):
        """Test that error responses don't expose sensitive information"""
        response = client.get("/api/nonexistent")
        
        data = response.json()
        
        # Error messages should not expose internal details
        error_text = str(data).lower()
        sensitive_terms = ["password", "token", "secret", "internal", "debug"]
        
        for term in sensitive_terms:
            assert term not in error_text


class TestDataIntegrity:
    """Test data integrity and consistency in API responses"""
    
    def test_timestamp_consistency(self, client):
        """Test that timestamps are consistent and reasonable"""
        import time
        
        current_time = time.time()
        response = client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        response_timestamp = data["timestamp"]
        
        # Timestamp should be recent (within 1 second)
        assert abs(response_timestamp - current_time) < 1.0
        
        # Should be a valid Unix timestamp
        assert response_timestamp > **********  # After year 2001
        assert response_timestamp < **********  # Before year 2286
    
    def test_version_consistency(self, client):
        """Test that version information is consistent across endpoints"""
        endpoints = ["/", "/health"]
        versions = []
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            if response.status_code == 200:
                data = response.json()
                if "version" in data:
                    versions.append(data["version"])
        
        # All endpoints should report the same version
        if len(versions) > 1:
            assert all(v == versions[0] for v in versions)
        
        # Version should follow semantic versioning pattern
        if versions:
            version = versions[0]
            import re
            semver_pattern = r"^\d+\.\d+\.\d+(?:-[a-zA-Z0-9-]+)?(?:\+[a-zA-Z0-9-]+)?$"
            assert re.match(semver_pattern, version), f"Invalid version format: {version}"