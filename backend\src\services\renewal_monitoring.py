"""
Renewal Monitoring Service for Advantage Waste Enterprise
=========================================================

Comprehensive background service for automated contract renewal tracking,
alert generation, and escalation management across 3,850+ Greystar properties.

Features:
- Automated renewal alert generation with configurable rules
- Escalation workflows based on priority and response times
- Notification delivery across multiple channels
- Performance analytics and reporting
- Redis-based task queuing for scalability
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
import uuid
import json

from sqlalchemy import select, and_, or_, func, update
from sqlalchemy.ext.asyncio import AsyncSession
import structlog
import redis.asyncio as redis

from ..core.database import DatabaseManager, get_database_manager
from ..models.contract import Contract, ContractStatus, Property, Vendor
from ..models.renewal import (
    RenewalAlert, AlertStatus, AlertPriority, ContractAnalysis,
    NotificationPreferences, NotificationLog, NotificationType,
    RenewalWorkflow, UserRole
)

logger = structlog.get_logger()

class RenewalAlertRule:
    """Configuration for automated renewal alert generation"""
    
    def __init__(
        self,
        rule_id: str,
        name: str,
        days_before_expiration: int,
        priority: AlertPriority,
        enabled: bool = True,
        conditions: Optional[Dict[str, Any]] = None,
        alert_template: Optional[Dict[str, str]] = None
    ):
        self.rule_id = rule_id
        self.name = name
        self.days_before_expiration = days_before_expiration
        self.priority = priority
        self.enabled = enabled
        self.conditions = conditions or {}
        self.alert_template = alert_template or {}

class RenewalMonitoringService:
    """
    Core service for automated contract renewal monitoring and alert management.
    Designed for enterprise-scale operations with high reliability and performance.
    """
    
    # Default alert rules based on Advantage Waste best practices
    DEFAULT_ALERT_RULES = [
        RenewalAlertRule(
            rule_id="initial_notice",
            name="Initial Renewal Notice",
            days_before_expiration=180,
            priority=AlertPriority.LOW,
            alert_template={
                "title": "Contract Renewal Planning Required",
                "message": "Contract expires in 6 months. Begin renewal evaluation process.",
                "action_required": "Schedule initial renewal review meeting and market analysis."
            }
        ),
        RenewalAlertRule(
            rule_id="market_analysis",
            name="Market Analysis Required",
            days_before_expiration=120,
            priority=AlertPriority.MEDIUM,
            alert_template={
                "title": "Market Analysis Due",
                "message": "Contract expires in 4 months. Market analysis should be completed.",
                "action_required": "Complete market analysis and vendor comparison."
            }
        ),
        RenewalAlertRule(
            rule_id="decision_deadline",
            name="Renewal Decision Deadline",
            days_before_expiration=90,
            priority=AlertPriority.HIGH,
            alert_template={
                "title": "Renewal Decision Required",
                "message": "Contract expires in 3 months. Renewal decision must be finalized.",
                "action_required": "Make final renewal decision and initiate contract negotiations."
            }
        ),
        RenewalAlertRule(
            rule_id="critical_deadline",
            name="Critical Renewal Deadline",
            days_before_expiration=30,
            priority=AlertPriority.CRITICAL,
            alert_template={
                "title": "CRITICAL: Contract Expiring Soon",
                "message": "Contract expires in 30 days. Immediate action required.",
                "action_required": "Execute renewal agreement or finalize alternative arrangements."
            }
        ),
        RenewalAlertRule(
            rule_id="final_warning",
            name="Final Renewal Warning",
            days_before_expiration=7,
            priority=AlertPriority.URGENT,
            alert_template={
                "title": "URGENT: Contract Expires This Week",
                "message": "Contract expires in 7 days. Service interruption imminent.",
                "action_required": "Complete all renewal paperwork immediately or arrange emergency extension."
            }
        ),
        RenewalAlertRule(
            rule_id="expired_contract",
            name="Contract Expired",
            days_before_expiration=0,
            priority=AlertPriority.URGENT,
            alert_template={
                "title": "CONTRACT EXPIRED",
                "message": "Contract has expired. Service may be interrupted.",
                "action_required": "Contact vendor immediately to arrange emergency extension or alternative service."
            }
        )
    ]
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.alert_rules = self.DEFAULT_ALERT_RULES.copy()
        self._redis_client: Optional[redis.Redis] = None
        self._monitoring_active = False
        
    async def initialize(self) -> None:
        """Initialize the renewal monitoring service"""
        self._redis_client = await self.db_manager.get_redis_client()
        logger.info("Renewal monitoring service initialized")
    
    async def start_monitoring(self) -> None:
        """Start the automated renewal monitoring process"""
        if self._monitoring_active:
            logger.warning("Renewal monitoring is already active")
            return
        
        self._monitoring_active = True
        logger.info("Starting automated renewal monitoring")
        
        # Start background tasks
        asyncio.create_task(self._monitor_contracts_loop())
        asyncio.create_task(self._process_escalations_loop())
        asyncio.create_task(self._cleanup_old_alerts_loop())
    
    async def stop_monitoring(self) -> None:
        """Stop the automated renewal monitoring process"""
        self._monitoring_active = False
        logger.info("Stopped automated renewal monitoring")
    
    async def _monitor_contracts_loop(self) -> None:
        """Main monitoring loop for contract renewals"""
        while self._monitoring_active:
            try:
                await self.scan_expiring_contracts()
                await asyncio.sleep(3600)  # Run every hour
            except Exception as e:
                logger.error("Error in contract monitoring loop", error=str(e))
                await asyncio.sleep(300)  # Wait 5 minutes before retry
    
    async def _process_escalations_loop(self) -> None:
        """Process alert escalations"""
        while self._monitoring_active:
            try:
                await self.process_alert_escalations()
                await asyncio.sleep(1800)  # Run every 30 minutes
            except Exception as e:
                logger.error("Error in escalation processing loop", error=str(e))
                await asyncio.sleep(300)
    
    async def _cleanup_old_alerts_loop(self) -> None:
        """Clean up old resolved alerts and logs"""
        while self._monitoring_active:
            try:
                await self.cleanup_old_data()
                await asyncio.sleep(86400)  # Run daily
            except Exception as e:
                logger.error("Error in cleanup loop", error=str(e))
                await asyncio.sleep(3600)
    
    async def scan_expiring_contracts(self) -> Dict[str, int]:
        """
        Scan for contracts requiring renewal alerts and generate them.
        Returns summary of alerts created by priority level.
        """
        logger.info("Scanning for expiring contracts")
        
        stats = {
            "scanned": 0,
            "alerts_created": 0,
            "by_priority": {priority.value: 0 for priority in AlertPriority}
        }
        
        async with self.db_manager.get_write_session() as session:
            # Get active contracts that might need renewal alerts
            query = select(Contract).where(
                and_(
                    Contract.status.in_([ContractStatus.ACTIVE, ContractStatus.PENDING_RENEWAL]),
                    Contract.expiration_date >= date.today() - timedelta(days=30),  # Include recently expired
                    Contract.expiration_date <= date.today() + timedelta(days=365)  # Look ahead 1 year
                )
            ).options(
                # Eager load related data to avoid N+1 queries
                selectinload(Contract.property),
                selectinload(Contract.vendor),
                selectinload(Contract.renewal_alerts)
            )
            
            result = await session.execute(query)
            contracts = result.scalars().all()
            
            stats["scanned"] = len(contracts)
            
            for contract in contracts:
                alerts_created = await self._process_contract_alerts(session, contract)
                stats["alerts_created"] += len(alerts_created)
                
                for alert in alerts_created:
                    stats["by_priority"][alert.priority.value] += 1
        
        logger.info("Contract scan completed", **stats)
        return stats
    
    async def _process_contract_alerts(
        self, 
        session: AsyncSession, 
        contract: Contract
    ) -> List[RenewalAlert]:
        """Process renewal alerts for a specific contract"""
        
        alerts_created = []
        days_to_expiration = (contract.expiration_date - date.today()).days
        
        # Check each alert rule
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            # Check if we need to create an alert for this rule
            if self._should_create_alert(contract, rule, days_to_expiration):
                alert = await self._create_renewal_alert(session, contract, rule, days_to_expiration)
                if alert:
                    alerts_created.append(alert)
        
        return alerts_created
    
    def _should_create_alert(
        self, 
        contract: Contract, 
        rule: RenewalAlertRule, 
        days_to_expiration: int
    ) -> bool:
        """Determine if an alert should be created for the given rule"""
        
        # Check if we're at or past the trigger point
        if days_to_expiration > rule.days_before_expiration:
            return False
        
        # Check if alert already exists for this rule
        existing_alert = next(
            (alert for alert in contract.renewal_alerts 
             if alert.generation_rule == rule.rule_id and 
             alert.status not in [AlertStatus.DISMISSED, AlertStatus.COMPLETED]),
            None
        )
        
        if existing_alert:
            return False
        
        # Check rule conditions
        if rule.conditions:
            if not self._evaluate_rule_conditions(contract, rule.conditions):
                return False
        
        return True
    
    def _evaluate_rule_conditions(
        self, 
        contract: Contract, 
        conditions: Dict[str, Any]
    ) -> bool:
        """Evaluate rule conditions against contract data"""
        
        for condition, value in conditions.items():
            if condition == "contract_value_min" and contract.calculated_monthly_cost:
                if contract.calculated_monthly_cost * 12 < value:
                    return False
            elif condition == "property_type" and contract.property:
                if contract.property.property_type.value != value:
                    return False
            elif condition == "vendor_rating" and contract.vendor:
                if not contract.vendor.average_rating or contract.vendor.average_rating < value:
                    return False
        
        return True
    
    async def _create_renewal_alert(
        self,
        session: AsyncSession,
        contract: Contract,
        rule: RenewalAlertRule,
        days_to_expiration: int
    ) -> Optional[RenewalAlert]:
        """Create a new renewal alert"""
        
        try:
            # Generate unique alert ID
            alert_id = f"ALT-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8].upper()}"
            
            # Calculate recommended action date
            recommended_action_date = None
            if rule.days_before_expiration > 30:
                # For early alerts, recommend action 30 days before the next milestone
                next_milestone_days = rule.days_before_expiration - 30
                recommended_action_date = contract.expiration_date - timedelta(days=next_milestone_days)
            else:
                # For urgent alerts, recommend immediate action
                recommended_action_date = date.today() + timedelta(days=3)
            
            # Create alert with template data
            template = rule.alert_template
            alert = RenewalAlert(
                alert_id=alert_id,
                contract_id=contract.id,
                alert_date=date.today(),
                priority=rule.priority,
                status=AlertStatus.PENDING,
                title=template.get("title", f"Contract Renewal Required - {rule.name}"),
                message=self._format_alert_message(template.get("message", ""), contract, days_to_expiration),
                action_required=template.get("action_required", "Review contract for renewal."),
                contract_expiration_date=contract.expiration_date,
                days_to_expiration=days_to_expiration,
                recommended_action_date=recommended_action_date,
                auto_generated=True,
                generation_rule=rule.rule_id
            )
            
            session.add(alert)
            await session.flush()  # Get the ID
            
            # Queue notification delivery
            await self._queue_alert_notifications(alert)
            
            logger.info(
                "Created renewal alert",
                alert_id=alert_id,
                contract_number=contract.contract_number,
                property_name=contract.property.name if contract.property else "Unknown",
                priority=rule.priority.value,
                days_to_expiration=days_to_expiration
            )
            
            return alert
            
        except Exception as e:
            logger.error(
                "Failed to create renewal alert",
                contract_id=contract.id,
                rule_id=rule.rule_id,
                error=str(e)
            )
            return None
    
    def _format_alert_message(
        self, 
        template_message: str, 
        contract: Contract, 
        days_to_expiration: int
    ) -> str:
        """Format alert message with contract-specific data"""
        
        replacements = {
            "{property_name}": contract.property.name if contract.property else "Unknown Property",
            "{vendor_name}": contract.vendor.name if contract.vendor else "Unknown Vendor",
            "{contract_number}": contract.contract_number,
            "{expiration_date}": contract.expiration_date.strftime("%B %d, %Y"),
            "{days_to_expiration}": str(days_to_expiration),
            "{monthly_cost}": f"${contract.calculated_monthly_cost:,.2f}" if contract.calculated_monthly_cost else "Unknown",
            "{annual_cost}": f"${contract.calculated_monthly_cost * 12:,.2f}" if contract.calculated_monthly_cost else "Unknown"
        }
        
        formatted_message = template_message
        for placeholder, value in replacements.items():
            formatted_message = formatted_message.replace(placeholder, value)
        
        return formatted_message
    
    async def _queue_alert_notifications(self, alert: RenewalAlert) -> None:
        """Queue notifications for a renewal alert"""
        
        if not self._redis_client:
            return
        
        notification_job = {
            "alert_id": alert.id,
            "priority": alert.priority.value,
            "created_at": datetime.utcnow().isoformat()
        }
        
        await self._redis_client.lpush(
            "renewal_alert_notifications",
            json.dumps(notification_job)
        )
    
    async def process_alert_escalations(self) -> Dict[str, int]:
        """Process alert escalations based on response times and priority"""
        
        logger.debug("Processing alert escalations")
        
        stats = {"evaluated": 0, "escalated": 0}
        
        async with self.db_manager.get_write_session() as session:
            # Get alerts that may need escalation
            query = select(RenewalAlert).where(
                and_(
                    RenewalAlert.status.in_([AlertStatus.PENDING, AlertStatus.SENT]),
                    RenewalAlert.escalation_level < 3,  # Max 3 escalation levels
                    or_(
                        # Critical/urgent alerts not acknowledged within 24 hours
                        and_(
                            RenewalAlert.priority.in_([AlertPriority.CRITICAL, AlertPriority.URGENT]),
                            RenewalAlert.acknowledged_at.is_(None),
                            RenewalAlert.created_at <= datetime.utcnow() - timedelta(hours=24)
                        ),
                        # High priority alerts not acknowledged within 72 hours
                        and_(
                            RenewalAlert.priority == AlertPriority.HIGH,
                            RenewalAlert.acknowledged_at.is_(None),
                            RenewalAlert.created_at <= datetime.utcnow() - timedelta(hours=72)
                        ),
                        # Any alert approaching expiration without action
                        and_(
                            RenewalAlert.days_to_expiration <= 7,
                            RenewalAlert.status != AlertStatus.IN_PROGRESS
                        )
                    )
                )
            )
            
            result = await session.execute(query)
            alerts = result.scalars().all()
            
            stats["evaluated"] = len(alerts)
            
            for alert in alerts:
                if await self._escalate_alert(session, alert):
                    stats["escalated"] += 1
        
        if stats["escalated"] > 0:
            logger.info("Alert escalations processed", **stats)
        
        return stats
    
    async def _escalate_alert(self, session: AsyncSession, alert: RenewalAlert) -> bool:
        """Escalate a specific alert"""
        
        try:
            # Increment escalation level
            alert.escalation_level += 1
            alert.escalated_at = datetime.utcnow()
            
            # Determine escalation target based on level
            escalation_targets = {
                1: "regional_manager",
                2: "operations_director", 
                3: "executive_team"
            }
            
            alert.escalated_to = escalation_targets.get(alert.escalation_level, "executive_team")
            alert.status = AlertStatus.ESCALATED
            
            # Queue escalation notifications
            await self._queue_escalation_notifications(alert)
            
            logger.info(
                "Alert escalated",
                alert_id=alert.alert_id,
                escalation_level=alert.escalation_level,
                escalated_to=alert.escalated_to
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to escalate alert",
                alert_id=alert.alert_id,
                error=str(e)
            )
            return False
    
    async def _queue_escalation_notifications(self, alert: RenewalAlert) -> None:
        """Queue escalation notifications"""
        
        if not self._redis_client:
            return
        
        escalation_job = {
            "alert_id": alert.id,
            "escalation_level": alert.escalation_level,
            "escalated_to": alert.escalated_to,
            "created_at": datetime.utcnow().isoformat()
        }
        
        await self._redis_client.lpush(
            "alert_escalations",
            json.dumps(escalation_job)
        )
    
    async def acknowledge_alert(
        self, 
        alert_id: int, 
        acknowledged_by: str,
        response_notes: Optional[str] = None
    ) -> bool:
        """Acknowledge a renewal alert"""
        
        async with self.db_manager.get_write_session() as session:
            query = select(RenewalAlert).where(RenewalAlert.id == alert_id)
            result = await session.execute(query)
            alert = result.scalar_one_or_none()
            
            if not alert:
                return False
            
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.utcnow()
            alert.acknowledged_by = acknowledged_by
            if response_notes:
                alert.response_notes = response_notes
            
            logger.info(
                "Alert acknowledged",
                alert_id=alert.alert_id,
                acknowledged_by=acknowledged_by
            )
            
            return True
    
    async def complete_alert(
        self,
        alert_id: int,
        completed_by: str,
        completion_notes: Optional[str] = None
    ) -> bool:
        """Mark an alert as completed"""
        
        async with self.db_manager.get_write_session() as session:
            query = select(RenewalAlert).where(RenewalAlert.id == alert_id)
            result = await session.execute(query)
            alert = result.scalar_one_or_none()
            
            if not alert:
                return False
            
            alert.status = AlertStatus.COMPLETED
            alert.resolved_at = datetime.utcnow()
            if completion_notes:
                alert.response_notes = (alert.response_notes or "") + f"\nCompleted: {completion_notes}"
            
            logger.info(
                "Alert completed",
                alert_id=alert.alert_id,
                completed_by=completed_by
            )
            
            return True
    
    async def get_active_alerts(
        self, 
        property_id: Optional[int] = None,
        priority: Optional[AlertPriority] = None,
        limit: int = 50
    ) -> List[RenewalAlert]:
        """Get active renewal alerts with optional filtering"""
        
        async with self.db_manager.get_read_session() as session:
            query = select(RenewalAlert).where(
                RenewalAlert.status.in_([
                    AlertStatus.PENDING, AlertStatus.SENT, 
                    AlertStatus.ACKNOWLEDGED, AlertStatus.ESCALATED
                ])
            )
            
            if property_id:
                query = query.join(Contract).where(Contract.property_id == property_id)
            
            if priority:
                query = query.where(RenewalAlert.priority == priority)
            
            query = query.order_by(
                RenewalAlert.priority.desc(),
                RenewalAlert.days_to_expiration.asc()
            ).limit(limit)
            
            result = await session.execute(query)
            return result.scalars().all()
    
    async def get_renewal_metrics(self, days_back: int = 30) -> Dict[str, Any]:
        """Get renewal monitoring metrics for reporting"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_back)
        
        async with self.db_manager.get_read_session() as session:
            # Alerts created
            alerts_query = select(func.count(RenewalAlert.id)).where(
                RenewalAlert.created_at >= cutoff_date
            )
            alerts_result = await session.execute(alerts_query)
            total_alerts = alerts_result.scalar()
            
            # Alerts by priority
            priority_query = select(
                RenewalAlert.priority,
                func.count(RenewalAlert.id)
            ).where(
                RenewalAlert.created_at >= cutoff_date
            ).group_by(RenewalAlert.priority)
            
            priority_result = await session.execute(priority_query)
            alerts_by_priority = dict(priority_result.fetchall())
            
            # Response times
            response_query = select(
                func.avg(
                    func.extract('epoch', RenewalAlert.acknowledged_at - RenewalAlert.created_at) / 3600
                ).label('avg_response_hours')
            ).where(
                and_(
                    RenewalAlert.created_at >= cutoff_date,
                    RenewalAlert.acknowledged_at.is_not(None)
                )
            )
            
            response_result = await session.execute(response_query)
            avg_response_hours = response_result.scalar()
            
            # Contracts expiring soon
            upcoming_query = select(func.count(Contract.id)).where(
                and_(
                    Contract.status == ContractStatus.ACTIVE,
                    Contract.expiration_date <= date.today() + timedelta(days=90),
                    Contract.expiration_date >= date.today()
                )
            )
            
            upcoming_result = await session.execute(upcoming_query)
            upcoming_expirations = upcoming_result.scalar()
            
            return {
                "period_days": days_back,
                "total_alerts": total_alerts,
                "alerts_by_priority": alerts_by_priority,
                "avg_response_hours": float(avg_response_hours) if avg_response_hours else None,
                "upcoming_expirations": upcoming_expirations,
                "generated_at": datetime.utcnow().isoformat()
            }
    
    async def cleanup_old_data(self, retention_days: int = 365) -> Dict[str, int]:
        """Clean up old resolved alerts and logs"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        stats = {"alerts_cleaned": 0, "logs_cleaned": 0}
        
        async with self.db_manager.get_write_session() as session:
            # Clean up old resolved alerts
            alerts_delete = update(RenewalAlert).where(
                and_(
                    RenewalAlert.status.in_([AlertStatus.COMPLETED, AlertStatus.DISMISSED]),
                    RenewalAlert.resolved_at < cutoff_date
                )
            ).values(
                # Soft delete by archiving important fields in JSONB
                status=AlertStatus.DISMISSED,
                response_notes=func.concat(
                    func.coalesce(RenewalAlert.response_notes, ''),
                    f' [ARCHIVED: {datetime.utcnow().date()}]'
                )
            )
            
            alerts_result = await session.execute(alerts_delete)
            stats["alerts_cleaned"] = alerts_result.rowcount
            
            # Clean up old notification logs (keep summary data)
            logs_delete = update(NotificationLog).where(
                and_(
                    NotificationLog.sent_at < cutoff_date,
                    NotificationLog.delivery_status.in_(['delivered', 'failed', 'bounced'])
                )
            ).values(
                # Clear large content fields but keep metadata
                content="[ARCHIVED]",
                delivery_response=None,
                provider_metadata=None
            )
            
            logs_result = await session.execute(logs_delete)
            stats["logs_cleaned"] = logs_result.rowcount
        
        if sum(stats.values()) > 0:
            logger.info("Cleanup completed", **stats)
        
        return stats