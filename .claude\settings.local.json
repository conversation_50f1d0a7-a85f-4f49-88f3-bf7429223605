{"allowedTools": ["filesystem", "web_search", "web_fetch", "repl", "desktop-commander", "artifacts", "google_drive_search", "google_drive_fetch"], "allowedDomains": ["docs.python.org", "fastapi.tiangolo.com", "docs.sqlalchemy.org", "react.dev", "vitejs.dev", "tailwindcss.com", "greystar.com", "docs.anthropic.com", "github.com", "stackoverflow.com", "developer.mozilla.org", "numpy.org", "pandas.pydata.org", "plotly.com", "docs.pydantic.dev"], "autoAcceptMode": false, "projectContext": {"name": "Advantage Waste Enterprise", "description": "Enterprise waste management solution for Greystar multifamily properties", "primaryLanguages": ["python", "typescript", "javascript"], "frameworks": ["<PERSON><PERSON><PERSON>", "react", "tailwindcss"], "database": "postgresql"}, "permissions": {"allow": ["Bash(ls:*)", "Bash(find:*)", "Bash(node:*)", "Bash(npm:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker compose:*)", "<PERSON><PERSON>(source:*)", "WebFetch(domain:fastapi.tiangolo.com)", "Bash(bash setup-dev-environment.ps1)", "<PERSON><PERSON>(chmod:*)", "Bash(./start-claude.sh:*)", "<PERSON><PERSON>(claude --dangerously-skip-permissions)", "mcp__claude-code__claude_code", "<PERSON><PERSON>(mkdir:*)", "mcp__github__get_file_contents", "mcp__github__list_commits", "mcp__github__search_code"], "deny": []}}