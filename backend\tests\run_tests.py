#!/usr/bin/env python3
"""
Advantage Waste Enterprise Test Runner
======================================

Comprehensive test runner for the Advantage Waste Contract Renewal Analysis System.
Provides organized test execution with performance monitoring and detailed reporting.

Test Categories:
- Unit Tests: Core business logic validation
- Integration Tests: API endpoints and service integration  
- Performance Tests: Enterprise-scale load testing
- Financial Tests: Financial calculation validation
- Monitoring Tests: Health check and system monitoring

Usage:
    python run_tests.py                    # Run all tests
    python run_tests.py --unit             # Run only unit tests
    python run_tests.py --performance      # Run only performance tests
    python run_tests.py --coverage         # Run with coverage report
    python run_tests.py --enterprise       # Run enterprise-scale tests
    python run_tests.py --smoke            # Run smoke tests only
"""

import subprocess
import sys
import argparse
import time
import os
from pathlib import Path
from typing import List, Dict, Any


class TestRunner:
    """Advanced test runner for Advantage Waste Enterprise"""
    
    def __init__(self):
        self.start_time = time.time()
        self.test_results = {}
        self.backend_path = Path(__file__).parent.parent
        
    def run_command(self, command: List[str], description: str) -> Dict[str, Any]:
        """Run a command and capture results"""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(command)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                cwd=self.backend_path,
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            success = result.returncode == 0
            
            print(f"Exit Code: {result.returncode}")
            print(f"Duration: {duration:.2f} seconds")
            
            if result.stdout:
                print(f"\nSTDOUT:\n{result.stdout}")
            
            if result.stderr:
                print(f"\nSTDERR:\n{result.stderr}")
            
            return {
                'success': success,
                'returncode': result.returncode,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'description': description
            }
            
        except subprocess.TimeoutExpired:
            print(f"ERROR: Command timed out after 10 minutes")
            return {
                'success': False,
                'returncode': -1,
                'duration': 600,
                'stdout': '',
                'stderr': 'Command timed out',
                'description': description
            }
        except Exception as e:
            print(f"ERROR: Failed to run command: {e}")
            return {
                'success': False,
                'returncode': -1,
                'duration': 0,
                'stdout': '',
                'stderr': str(e),
                'description': description
            }
    
    def run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests for business logic"""
        command = [
            sys.executable, "-m", "pytest",
            "tests/test_renewal_logic.py",
            "-v", "--tb=short",
            "-m", "not slow"
        ]
        return self.run_command(command, "Unit Tests - Contract Analysis Business Logic")
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests for API endpoints"""
        command = [
            sys.executable, "-m", "pytest", 
            "tests/test_api_endpoints.py",
            "-v", "--tb=short"
        ]
        return self.run_command(command, "Integration Tests - API Endpoints")
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance tests for enterprise-scale scenarios"""
        command = [
            sys.executable, "-m", "pytest",
            "tests/test_performance.py",
            "-v", "--tb=short",
            "-m", "not enterprise"  # Skip very long enterprise tests by default
        ]
        return self.run_command(command, "Performance Tests - Load and Scalability")
    
    def run_financial_tests(self) -> Dict[str, Any]:
        """Run financial calculation validation tests"""
        command = [
            sys.executable, "-m", "pytest",
            "tests/test_financial_validation.py", 
            "-v", "--tb=short"
        ]
        return self.run_command(command, "Financial Tests - Calculation Validation")
    
    def run_monitoring_tests(self) -> Dict[str, Any]:
        """Run monitoring and health check tests"""
        command = [
            sys.executable, "-m", "pytest",
            "tests/test_monitoring_health.py",
            "-v", "--tb=short"
        ]
        return self.run_command(command, "Monitoring Tests - Health Checks and System Monitoring")
    
    def run_enterprise_tests(self) -> Dict[str, Any]:
        """Run enterprise-scale tests (3,850+ properties)"""
        command = [
            sys.executable, "-m", "pytest", 
            "tests/",
            "-v", "--tb=short",
            "-m", "enterprise",
            "--maxfail=3"
        ]
        return self.run_command(command, "Enterprise Tests - 3,850+ Property Scale")
    
    def run_smoke_tests(self) -> Dict[str, Any]:
        """Run smoke tests for CI/CD"""
        command = [
            sys.executable, "-m", "pytest",
            "tests/",
            "-v", "--tb=short", 
            "-m", "smoke",
            "--maxfail=1"
        ]
        return self.run_command(command, "Smoke Tests - Basic Functionality")
    
    def run_coverage_tests(self) -> Dict[str, Any]:
        """Run all tests with coverage reporting"""
        command = [
            sys.executable, "-m", "pytest",
            "tests/",
            "-v", "--tb=short",
            "--cov=src",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing", 
            "--cov-report=xml",
            "--cov-fail-under=85",
            "-m", "not enterprise"  # Skip very long tests for coverage
        ]
        return self.run_command(command, "Coverage Tests - Code Coverage Analysis")
    
    def run_linting(self) -> Dict[str, Any]:
        """Run code linting and type checking"""
        # Run ruff linting
        ruff_result = self.run_command(
            [sys.executable, "-m", "ruff", "check", "src/"], 
            "Code Linting - Ruff"
        )
        
        # Run mypy type checking  
        mypy_result = self.run_command(
            [sys.executable, "-m", "mypy", "src/"],
            "Type Checking - MyPy"
        )
        
        # Combine results
        return {
            'success': ruff_result['success'] and mypy_result['success'],
            'returncode': max(ruff_result['returncode'], mypy_result['returncode']),
            'duration': ruff_result['duration'] + mypy_result['duration'],
            'stdout': f"RUFF:\n{ruff_result['stdout']}\n\nMYPY:\n{mypy_result['stdout']}",
            'stderr': f"RUFF:\n{ruff_result['stderr']}\n\nMYPY:\n{mypy_result['stderr']}",
            'description': "Code Quality - Linting and Type Checking"
        }
    
    def run_all_tests(self) -> Dict[str, List[Dict[str, Any]]]:
        """Run all test suites"""
        print("Starting Advantage Waste Enterprise Test Suite")
        print(f"Backend Path: {self.backend_path}")
        print(f"Start Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Run linting first
        results = {
            'quality': [self.run_linting()],
            'unit': [self.run_unit_tests()],
            'integration': [self.run_integration_tests()],
            'financial': [self.run_financial_tests()],
            'monitoring': [self.run_monitoring_tests()],
            'performance': [self.run_performance_tests()]
        }
        
        return results
    
    def print_summary(self, results: Dict[str, List[Dict[str, Any]]]):
        """Print test execution summary"""
        total_time = time.time() - self.start_time
        
        print(f"\n{'='*80}")
        print("ADVANTAGE WASTE ENTERPRISE TEST SUMMARY")
        print(f"{'='*80}")
        print(f"Total Execution Time: {total_time:.2f} seconds")
        print(f"Completion Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Summary by category
        total_tests = 0
        passed_tests = 0
        
        for category, test_results in results.items():
            print(f"\n{category.upper()} TESTS:")
            print("-" * 40)
            
            for result in test_results:
                status = "PASSED" if result['success'] else "FAILED"
                print(f"  {result['description']}: {status} ({result['duration']:.2f}s)")
                
                total_tests += 1
                if result['success']:
                    passed_tests += 1
        
        # Overall summary
        print(f"\n{'='*80}")
        print("OVERALL RESULTS:")
        print(f"  Total Test Suites: {total_tests}")
        print(f"  Passed: {passed_tests}")
        print(f"  Failed: {total_tests - passed_tests}")
        print(f"  Success Rate: {(passed_tests / total_tests) * 100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! System is ready for enterprise deployment.")
        else:
            print(f"\n❌ {total_tests - passed_tests} test suite(s) failed. Review failures above.")
        
        print(f"{'='*80}")
        
        return passed_tests == total_tests


def main():
    """Main test runner entry point"""
    parser = argparse.ArgumentParser(
        description="Advantage Waste Enterprise Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py                    # Run all tests
  python run_tests.py --unit             # Run only unit tests  
  python run_tests.py --performance      # Run only performance tests
  python run_tests.py --coverage         # Run with coverage report
  python run_tests.py --enterprise       # Run enterprise-scale tests
  python run_tests.py --smoke            # Run smoke tests only
        """
    )
    
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only") 
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    parser.add_argument("--financial", action="store_true", help="Run financial validation tests only")
    parser.add_argument("--monitoring", action="store_true", help="Run monitoring tests only")
    parser.add_argument("--enterprise", action="store_true", help="Run enterprise-scale tests")
    parser.add_argument("--smoke", action="store_true", help="Run smoke tests only")
    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage reporting")
    parser.add_argument("--lint", action="store_true", help="Run linting and type checking only")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    # Determine which tests to run
    if args.unit:
        results = {'unit': [runner.run_unit_tests()]}
    elif args.integration:
        results = {'integration': [runner.run_integration_tests()]}
    elif args.performance:
        results = {'performance': [runner.run_performance_tests()]}
    elif args.financial:
        results = {'financial': [runner.run_financial_tests()]}
    elif args.monitoring:
        results = {'monitoring': [runner.run_monitoring_tests()]}
    elif args.enterprise:
        results = {'enterprise': [runner.run_enterprise_tests()]}
    elif args.smoke:
        results = {'smoke': [runner.run_smoke_tests()]}
    elif args.coverage:
        results = {'coverage': [runner.run_coverage_tests()]}
    elif args.lint:
        results = {'quality': [runner.run_linting()]}
    else:
        # Run all tests
        results = runner.run_all_tests()
    
    # Print summary and determine exit code
    success = runner.print_summary(results)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()