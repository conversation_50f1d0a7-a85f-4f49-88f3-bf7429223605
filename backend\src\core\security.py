"""
Core Security Module
===================

Authentication and authorization logic for the Advantage Waste Enterprise API.
Implements role-based access control for Property Managers, Regional Directors, 
and Executives with enterprise security standards.
"""

from typing import Optional, List, Annotated
from enum import Enum
from datetime import datetime, timedelta
import jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import structlog

logger = structlog.get_logger()

# Security configuration
SECRET_KEY = "your-secret-key-change-in-production"  # TODO: Move to environment variables
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

class UserRole(str, Enum):
    """User roles with hierarchical permissions"""
    PROPERTY_MANAGER = "property_manager"
    REGIONAL_DIRECTOR = "regional_director" 
    EXECUTIVE = "executive"
    ADMIN = "admin"

class Permission(str, Enum):
    """Granular permissions for different operations"""
    # Contract permissions
    VIEW_CONTRACTS = "view_contracts"
    EDIT_CONTRACTS = "edit_contracts"
    APPROVE_CONTRACTS = "approve_contracts"
    
    # Renewal permissions
    VIEW_RENEWALS = "view_renewals"
    ANALYZE_RENEWALS = "analyze_renewals"
    APPROVE_RENEWALS = "approve_renewals"
    
    # Reporting permissions
    VIEW_PROPERTY_REPORTS = "view_property_reports"
    VIEW_REGIONAL_REPORTS = "view_regional_reports"
    VIEW_EXECUTIVE_REPORTS = "view_executive_reports"
    
    # Administrative permissions
    MANAGE_USERS = "manage_users"
    MANAGE_SETTINGS = "manage_settings"
    VIEW_AUDIT_LOGS = "view_audit_logs"

# Role-based permission mapping
ROLE_PERMISSIONS = {
    UserRole.PROPERTY_MANAGER: [
        Permission.VIEW_CONTRACTS,
        Permission.EDIT_CONTRACTS,
        Permission.VIEW_RENEWALS,
        Permission.ANALYZE_RENEWALS,
        Permission.VIEW_PROPERTY_REPORTS,
    ],
    UserRole.REGIONAL_DIRECTOR: [
        Permission.VIEW_CONTRACTS,
        Permission.EDIT_CONTRACTS,
        Permission.APPROVE_CONTRACTS,
        Permission.VIEW_RENEWALS,
        Permission.ANALYZE_RENEWALS,
        Permission.APPROVE_RENEWALS,
        Permission.VIEW_PROPERTY_REPORTS,
        Permission.VIEW_REGIONAL_REPORTS,
    ],
    UserRole.EXECUTIVE: [
        Permission.VIEW_CONTRACTS,
        Permission.APPROVE_CONTRACTS,
        Permission.VIEW_RENEWALS,
        Permission.APPROVE_RENEWALS,
        Permission.VIEW_PROPERTY_REPORTS,
        Permission.VIEW_REGIONAL_REPORTS,
        Permission.VIEW_EXECUTIVE_REPORTS,
    ],
    UserRole.ADMIN: [permission for permission in Permission],  # All permissions
}

class User:
    """User model for authentication and authorization"""
    def __init__(
        self,
        user_id: str,
        email: str,
        role: UserRole,
        property_ids: Optional[List[str]] = None,
        region_id: Optional[str] = None,
        is_active: bool = True,
    ):
        self.user_id = user_id
        self.email = email
        self.role = role
        self.property_ids = property_ids or []
        self.region_id = region_id
        self.is_active = is_active
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if user has specific permission"""
        return permission in ROLE_PERMISSIONS.get(self.role, [])
    
    def can_access_property(self, property_id: str) -> bool:
        """Check if user can access specific property"""
        if self.role in [UserRole.EXECUTIVE, UserRole.ADMIN]:
            return True  # Executives and admins can access all properties
        
        if self.role == UserRole.REGIONAL_DIRECTOR:
            # TODO: Implement region-based property access
            return True  # For now, regional directors can access all
        
        return property_id in self.property_ids

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> dict:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """Get current authenticated user from token"""
    try:
        payload = verify_token(credentials.credentials)
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # TODO: Replace with actual user lookup from database
        # For now, create a mock user based on token data
        user = User(
            user_id=user_id,
            email=payload.get("email", ""),
            role=UserRole(payload.get("role", UserRole.PROPERTY_MANAGER)),
            property_ids=payload.get("property_ids", []),
            region_id=payload.get("region_id"),
        )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Inactive user",
            )
        
        return user
        
    except Exception as e:
        logger.error("Authentication error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def require_permission(permission: Permission):
    """Decorator factory for requiring specific permissions"""
    def permission_checker(current_user: User = Depends(get_current_user)) -> User:
        if not current_user.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {permission.value}",
            )
        return current_user
    return permission_checker

def require_role(required_role: UserRole):
    """Decorator factory for requiring specific role or higher"""
    role_hierarchy = {
        UserRole.PROPERTY_MANAGER: 1,
        UserRole.REGIONAL_DIRECTOR: 2,
        UserRole.EXECUTIVE: 3,
        UserRole.ADMIN: 4,
    }
    
    def role_checker(current_user: User = Depends(get_current_user)) -> User:
        if role_hierarchy.get(current_user.role, 0) < role_hierarchy.get(required_role, 0):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient role. Required: {required_role.value} or higher",
            )
        return current_user
    return role_checker

def require_property_access(property_id: str):
    """Decorator factory for requiring access to specific property"""
    def property_checker(current_user: User = Depends(get_current_user)) -> User:
        if not current_user.can_access_property(property_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied to property {property_id}",
            )
        return current_user
    return property_checker

# Type aliases for dependency injection
AuthenticatedUser = Annotated[User, Depends(get_current_user)]
PropertyManager = Annotated[User, Depends(require_role(UserRole.PROPERTY_MANAGER))]
RegionalDirector = Annotated[User, Depends(require_role(UserRole.REGIONAL_DIRECTOR))]
Executive = Annotated[User, Depends(require_role(UserRole.EXECUTIVE))]
Admin = Annotated[User, Depends(require_role(UserRole.ADMIN))]

# Permission-based dependencies
CanViewRenewals = Annotated[User, Depends(require_permission(Permission.VIEW_RENEWALS))]
CanAnalyzeRenewals = Annotated[User, Depends(require_permission(Permission.ANALYZE_RENEWALS))]
CanApproveRenewals = Annotated[User, Depends(require_permission(Permission.APPROVE_RENEWALS))]
CanViewExecutiveReports = Annotated[User, Depends(require_permission(Permission.VIEW_EXECUTIVE_REPORTS))]