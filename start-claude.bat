@echo off
REM Claude Code startup for Advantage Waste Enterprise

echo Starting Claude Code for Advantage Waste Enterprise...

REM Change to project directory  
cd "C:\Users\<USER>\Documents\Claude Code\advantage-waste-enterprise"

REM Start Claude Code via WSL
wsl --distribution Ubuntu --exec bash -c "cd '/mnt/c/Users/<USER>/Documents/Claude Code/advantage-waste-enterprise' && export CLAUDE_PROJECT=advantage-waste-enterprise && export CLAUDE_CONTEXT_FILES=CLAUDE.md,examples/contract_analysis_example.py,PRPs/templates/prp_base.md && export PATH=~/.npm-global/bin:/usr/bin:$PATH && claude"
