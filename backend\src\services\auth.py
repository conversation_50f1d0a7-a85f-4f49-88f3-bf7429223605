"""
Authentication Service
=====================

Service layer for authentication operations including user login, token management,
and password operations for the Advantage Waste Enterprise API.
"""

from typing import Optional, Dict, List
from datetime import datetime, timedelta
from uuid import uuid4
import structlog
from sqlalchemy.orm import Session
from sqlalchemy import select
from fastapi import HTTPException, status

from ..core.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    UserRole,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    REFRESH_TOKEN_EXPIRE_DAYS
)
from ..models.base import Base
from ..database.database import get_db
from ..core.exceptions import ValidationError, NotFoundError

logger = structlog.get_logger()


class AuthService:
    """Service for handling authentication operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def authenticate_user(self, email: str, password: str) -> Optional[Dict]:
        """
        Authenticate user by email and password.
        
        Args:
            email: User's email address
            password: Plain text password
            
        Returns:
            User data dict if authentication successful, None otherwise
        """
        try:
            # TODO: Replace with actual database query when User model is created
            # For now, using mock users for development
            mock_users = {
                "<EMAIL>": {
                    "user_id": "pm-001",
                    "email": "<EMAIL>",
                    "hashed_password": get_password_hash("password123"),
                    "role": UserRole.PROPERTY_MANAGER,
                    "property_ids": ["prop-001", "prop-002"],
                    "is_active": True,
                    "first_name": "John",
                    "last_name": "Manager"
                },
                "<EMAIL>": {
                    "user_id": "rd-001",
                    "email": "<EMAIL>",
                    "hashed_password": get_password_hash("password123"),
                    "role": UserRole.REGIONAL_DIRECTOR,
                    "region_id": "region-001",
                    "is_active": True,
                    "first_name": "Jane",
                    "last_name": "Director"
                },
                "<EMAIL>": {
                    "user_id": "exec-001",
                    "email": "<EMAIL>",
                    "hashed_password": get_password_hash("password123"),
                    "role": UserRole.EXECUTIVE,
                    "is_active": True,
                    "first_name": "Mike",
                    "last_name": "Executive"
                },
                "<EMAIL>": {
                    "user_id": "admin-001",
                    "email": "<EMAIL>",
                    "hashed_password": get_password_hash("password123"),
                    "role": UserRole.ADMIN,
                    "is_active": True,
                    "first_name": "Admin",
                    "last_name": "User"
                }
            }
            
            user_data = mock_users.get(email.lower())
            if not user_data:
                logger.warning("Login attempt for non-existent user", email=email)
                return None
            
            if not verify_password(password, user_data["hashed_password"]):
                logger.warning("Invalid password attempt", email=email)
                return None
            
            if not user_data.get("is_active", True):
                logger.warning("Login attempt for inactive user", email=email)
                return None
            
            # Remove password from returned data
            user_dict = {k: v for k, v in user_data.items() if k != "hashed_password"}
            
            logger.info(
                "User authenticated successfully",
                user_id=user_data["user_id"],
                role=user_data["role"]
            )
            
            return user_dict
            
        except Exception as e:
            logger.error("Authentication error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service error"
            )
    
    async def create_tokens(self, user_data: Dict) -> Dict[str, str]:
        """
        Create access and refresh tokens for authenticated user.
        
        Args:
            user_data: Dictionary containing user information
            
        Returns:
            Dictionary with access_token and refresh_token
        """
        # Create access token payload
        access_token_data = {
            "sub": user_data["user_id"],
            "email": user_data["email"],
            "role": user_data["role"],
            "property_ids": user_data.get("property_ids", []),
            "region_id": user_data.get("region_id"),
            "type": "access"
        }
        
        # Create refresh token payload
        refresh_token_data = {
            "sub": user_data["user_id"],
            "type": "refresh",
            "jti": str(uuid4())  # JWT ID for token revocation
        }
        
        # Generate tokens
        access_token = create_access_token(
            data=access_token_data,
            expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        
        refresh_token = create_access_token(
            data=refresh_token_data,
            expires_delta=timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        )
        
        # Store refresh token in database for revocation capability
        # TODO: Implement refresh token storage when database model is ready
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
        """
        Generate new access token using refresh token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            New access token
        """
        from ..core.security import verify_token
        
        try:
            # Verify refresh token
            payload = verify_token(refresh_token)
            
            if payload.get("type") != "refresh":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token type"
                )
            
            user_id = payload.get("sub")
            jti = payload.get("jti")
            
            # TODO: Check if refresh token is revoked in database
            # TODO: Load fresh user data from database
            
            # For now, create new access token with minimal data
            access_token_data = {
                "sub": user_id,
                "type": "access"
                # Additional user data should be loaded from database
            }
            
            access_token = create_access_token(
                data=access_token_data,
                expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            )
            
            logger.info("Access token refreshed", user_id=user_id)
            
            return {
                "access_token": access_token,
                "token_type": "bearer"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("Token refresh error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not refresh token"
            )
    
    async def revoke_refresh_token(self, user_id: str, jti: Optional[str] = None):
        """
        Revoke refresh token(s) for a user.
        
        Args:
            user_id: User ID whose tokens to revoke
            jti: Specific token ID to revoke, or None to revoke all
        """
        # TODO: Implement token revocation in database
        logger.info(
            "Refresh token revoked",
            user_id=user_id,
            jti=jti
        )
    
    async def change_password(
        self,
        user_id: str,
        current_password: str,
        new_password: str
    ) -> bool:
        """
        Change user password.
        
        Args:
            user_id: User ID
            current_password: Current password for verification
            new_password: New password to set
            
        Returns:
            True if password changed successfully
        """
        try:
            # TODO: Load user from database
            # For now, this is a placeholder
            
            # Validate new password
            if len(new_password) < 8:
                raise ValidationError("Password must be at least 8 characters long")
            
            if not any(c.isupper() for c in new_password):
                raise ValidationError("Password must contain at least one uppercase letter")
            
            if not any(c.islower() for c in new_password):
                raise ValidationError("Password must contain at least one lowercase letter")
            
            if not any(c.isdigit() for c in new_password):
                raise ValidationError("Password must contain at least one number")
            
            # Hash new password
            new_password_hash = get_password_hash(new_password)
            
            # TODO: Update password in database
            
            logger.info("Password changed successfully", user_id=user_id)
            
            # Revoke all refresh tokens for security
            await self.revoke_refresh_token(user_id)
            
            return True
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error("Password change error", error=str(e), user_id=user_id)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Could not change password"
            )
    
    async def request_password_reset(self, email: str) -> str:
        """
        Request password reset for user.
        
        Args:
            email: User's email address
            
        Returns:
            Reset token (in production, this would be sent via email)
        """
        try:
            # TODO: Check if user exists in database
            
            # Generate reset token
            reset_token = str(uuid4())
            expires = datetime.utcnow() + timedelta(hours=1)
            
            # TODO: Store reset token in database with expiration
            
            # TODO: Send reset email in production
            # For development, return token (remove in production)
            
            logger.info("Password reset requested", email=email)
            
            return reset_token
            
        except Exception as e:
            logger.error("Password reset request error", error=str(e))
            # Don't reveal if email exists or not for security
            return ""
    
    async def reset_password(self, reset_token: str, new_password: str) -> bool:
        """
        Reset password using reset token.
        
        Args:
            reset_token: Valid reset token
            new_password: New password to set
            
        Returns:
            True if password reset successfully
        """
        try:
            # TODO: Verify reset token in database
            # TODO: Check if token is expired
            # TODO: Get user_id from token
            
            # Validate new password (same as change_password)
            if len(new_password) < 8:
                raise ValidationError("Password must be at least 8 characters long")
            
            # Hash new password
            new_password_hash = get_password_hash(new_password)
            
            # TODO: Update password in database
            # TODO: Delete reset token
            
            logger.info("Password reset successfully")
            
            return True
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error("Password reset error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
    
    async def get_user_sessions(self, user_id: str) -> List[Dict]:
        """
        Get active sessions for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of active sessions
        """
        # TODO: Implement session tracking in database
        return []
    
    async def terminate_session(self, user_id: str, session_id: str) -> bool:
        """
        Terminate a specific user session.
        
        Args:
            user_id: User ID
            session_id: Session ID to terminate
            
        Returns:
            True if session terminated successfully
        """
        # TODO: Implement session termination
        logger.info(
            "Session terminated",
            user_id=user_id,
            session_id=session_id
        )
        return True