# Advantage Waste Enterprise - Development Startup Script
# This script starts all development services

Write-Host "Starting Advantage Waste Enterprise Development Environment..." -ForegroundColor Green

# Start backend in new terminal
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'C:\Users\<USER>\Documents\Claude Code\advantage-waste-enterprise\backend'; .\venv\Scripts\Activate.ps1; uvicorn src.main:app --reload --host 0.0.0.0 --port 8000"

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start frontend in new terminal
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'C:\Users\<USER>\Documents\Claude Code\advantage-waste-enterprise\frontend'; npm run dev"

Write-Host "Development servers starting..." -ForegroundColor Green
Write-Host "Backend API: http://localhost:8000" -ForegroundColor Yellow
Write-Host "Frontend: http://localhost:3000" -ForegroundColor Yellow
Write-Host "API Docs: http://localhost:8000/docs" -ForegroundColor Yellow

# Open Cursor IDE
if (Get-Command cursor -ErrorAction SilentlyContinue) {
    Write-Host "Opening Cursor IDE..." -ForegroundColor Yellow
    Start-Process cursor -ArgumentList "."
} else {
    Write-Host "Cursor IDE not found. Please install Cursor and run 'cursor .' in this directory." -ForegroundColor Yellow
}
