"""
Application Settings
===================

Configuration management for the Advantage Waste Enterprise API.
Uses Pydantic for environment variable validation and type safety.
"""

from typing import List, Optional
from pydantic import BaseSettings, Field, validator
import os


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application settings
    app_name: str = Field("Advantage Waste Enterprise API", env="APP_NAME")
    app_version: str = Field("1.0.0", env="APP_VERSION")
    environment: str = Field("development", env="ENVIRONMENT")
    debug: bool = Field(False, env="DEBUG")
    
    # Security settings
    secret_key: str = Field("your-secret-key-change-in-production", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    
    # Database settings
    database_url: str = Field("postgresql://user:password@localhost/advantage_waste", env="DATABASE_URL")
    database_pool_size: int = Field(10, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(20, env="DATABASE_MAX_OVERFLOW")
    
    # Redis settings (for caching and rate limiting)
    redis_url: str = Field("redis://localhost:6379", env="REDIS_URL")
    cache_ttl: int = Field(300, env="CACHE_TTL")  # 5 minutes default
    
    # CORS settings
    allowed_origins: List[str] = Field(
        [
            "http://localhost:3000",
            "http://localhost:5173",
            "https://advantage-waste.greystar.com"
        ],
        env="ALLOWED_ORIGINS"
    )
    
    # Rate limiting settings
    default_rate_limit: int = Field(300, env="DEFAULT_RATE_LIMIT")  # requests per minute
    dashboard_rate_limit: int = Field(60, env="DASHBOARD_RATE_LIMIT")
    analysis_rate_limit: int = Field(30, env="ANALYSIS_RATE_LIMIT")
    reports_rate_limit: int = Field(20, env="REPORTS_RATE_LIMIT")
    
    # External service settings
    email_service_url: Optional[str] = Field(None, env="EMAIL_SERVICE_URL")
    email_api_key: Optional[str] = Field(None, env="EMAIL_API_KEY")
    email_from_address: str = Field("<EMAIL>", env="EMAIL_FROM_ADDRESS")
    
    # Monitoring and logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    sentry_dsn: Optional[str] = Field(None, env="SENTRY_DSN")
    
    # Business logic settings
    default_contract_expiry_warning_days: int = Field(60, env="DEFAULT_CONTRACT_EXPIRY_WARNING_DAYS")
    auto_approval_savings_threshold: float = Field(10.0, env="AUTO_APPROVAL_SAVINGS_THRESHOLD")  # 10%
    max_analysis_depth_days: int = Field(30, env="MAX_ANALYSIS_DEPTH_DAYS")
    
    # File upload settings
    max_file_size_mb: int = Field(10, env="MAX_FILE_SIZE_MB")
    allowed_file_types: List[str] = Field(
        ["pdf", "xlsx", "csv", "docx"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # Greystar integration settings
    greystar_api_url: Optional[str] = Field(None, env="GREYSTAR_API_URL")
    greystar_api_key: Optional[str] = Field(None, env="GREYSTAR_API_KEY")
    greystar_timeout_seconds: int = Field(30, env="GREYSTAR_TIMEOUT_SECONDS")
    
    @validator("environment")
    def validate_environment(cls, v):
        allowed = ["development", "staging", "production"]
        if v not in allowed:
            raise ValueError(f"Environment must be one of: {allowed}")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        allowed = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed:
            raise ValueError(f"Log level must be one of: {allowed}")
        return v.upper()
    
    @validator("secret_key")
    def validate_secret_key(cls, v, values):
        if values.get("environment") == "production" and v == "your-secret-key-change-in-production":
            raise ValueError("Must set a secure secret key in production")
        if len(v) < 32:
            raise ValueError("Secret key must be at least 32 characters long")
        return v
    
    @validator("auto_approval_savings_threshold")
    def validate_savings_threshold(cls, v):
        if not 0 <= v <= 100:
            raise ValueError("Auto approval savings threshold must be between 0 and 100 percent")
        return v
    
    @property
    def is_development(self) -> bool:
        return self.environment == "development"
    
    @property
    def is_production(self) -> bool:
        return self.environment == "production"
    
    @property
    def max_file_size_bytes(self) -> int:
        return self.max_file_size_mb * 1024 * 1024
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Industry benchmark settings (business logic constants)
class IndustryBenchmarks:
    """Industry benchmark constants for waste management calculations"""
    
    # Cost benchmarks (per door per month)
    COST_PER_DOOR_MIN = 10.00
    COST_PER_DOOR_MAX = 30.00
    COST_PER_DOOR_AVERAGE = 16.50
    COST_PER_DOOR_BEST_IN_CLASS = 12.00
    
    # Volume benchmarks (cubic yards per door per month)
    VOLUME_PER_DOOR_MIN = 1.0
    VOLUME_PER_DOOR_MAX = 2.5
    VOLUME_PER_DOOR_AVERAGE = 1.75
    
    # Container size standards (cubic yards)
    STANDARD_CONTAINER_SIZES = [2, 4, 6, 8, 30, 34]
    
    # Pickup frequency standards (times per week)
    MIN_PICKUP_FREQUENCY = 1
    MAX_PICKUP_FREQUENCY = 7
    TYPICAL_PICKUP_FREQUENCY = 3
    
    # Cost variance thresholds for alerts
    COST_INCREASE_WARNING_THRESHOLD = 5.0  # 5%
    COST_INCREASE_CRITICAL_THRESHOLD = 10.0  # 10%
    SAVINGS_OPPORTUNITY_THRESHOLD = 10.0  # 10%
    
    # Contract term recommendations
    RECOMMENDED_CONTRACT_LENGTH_MONTHS = 24
    MAX_PRICE_INCREASE_CAP = 4.0  # 4% annual
    MAX_FUEL_SURCHARGE_CAP = 5.0  # 5%
    
    # Risk assessment factors
    HIGH_RISK_COST_VARIANCE = 20.0  # 20% above average
    HIGH_RISK_SERVICE_ISSUES = 3  # 3+ service issues per quarter
    
    @classmethod
    def get_cost_per_yard_benchmark(cls, pickup_frequency: int) -> float:
        """Calculate cost per cubic yard benchmark based on pickup frequency"""
        base_cost = 40.0  # Base cost per yard
        frequency_multiplier = 1 + (pickup_frequency - 1) * 0.1
        return base_cost * frequency_multiplier
    
    @classmethod
    def get_property_type_modifier(cls, property_type: str) -> float:
        """Get cost modifier based on property type"""
        modifiers = {
            "garden_style": 1.15,  # 15% higher costs
            "mid_rise": 1.0,       # Baseline
            "high_rise": 0.85,     # 15% lower costs
            "mixed_use": 1.05,     # 5% higher costs
        }
        return modifiers.get(property_type.lower(), 1.0)


# Notification templates
class NotificationTemplates:
    """Email and notification templates"""
    
    RENEWAL_REMINDER = {
        "subject": "Contract Renewal Required - {property_name}",
        "template": """
        Contract Renewal Required
        
        Property: {property_name}
        Vendor: {vendor_name}
        Expiry Date: {expiry_date}
        Days Remaining: {days_remaining}
        Monthly Cost: ${monthly_cost:,.2f}
        
        Action Required:
        - Review contract terms
        - Analyze renewal options
        - Submit renewal decision
        
        View Details: {dashboard_url}
        """
    }
    
    COST_ALERT = {
        "subject": "Cost Alert - {alert_type} Detected",
        "template": """
        Cost Alert: {alert_type}
        
        Contract: {contract_id}
        Property: {property_name}
        Cost Change: {cost_change_percentage:+.1f}% (${cost_change_amount:+,.2f})
        
        Current Monthly Cost: ${current_cost:,.2f}
        New Monthly Cost: ${new_cost:,.2f}
        
        Recommended Actions:
        {recommended_actions}
        
        View Analysis: {analysis_url}
        """
    }
    
    ANALYSIS_COMPLETE = {
        "subject": "Renewal Analysis Complete - {property_name}",
        "template": """
        Renewal Analysis Complete
        
        Property: {property_name}
        Recommendation: {recommendation}
        Confidence: {confidence_score}%
        Potential Savings: ${potential_savings:,.2f}/month
        
        Key Findings:
        {key_findings}
        
        Next Steps:
        {next_steps}
        
        View Full Report: {report_url}
        """
    }