"""
API Package
===========

FastAPI routers for the Advantage Waste Enterprise API.
Provides RESTful endpoints for contract renewal management, 
notifications, and waste management operations.
"""

__version__ = "1.0.0"
__author__ = "Advantage Waste Enterprise Team"

# API version and metadata
API_VERSION = "v1"
API_TITLE = "Advantage Waste Enterprise API"
API_DESCRIPTION = """
Enterprise waste management solution for Greystar multifamily properties.

## Features

- **Contract Renewal Management**: Automated renewal analysis and recommendations
- **Notification System**: Configurable alerts and communication preferences  
- **Cost Optimization**: Industry benchmark analysis and savings identification
- **Executive Reporting**: Comprehensive dashboards and analytics
- **Security**: Role-based access control and audit logging

## Authentication

All endpoints require Bearer token authentication with role-based permissions:
- Property Managers: Property-level access
- Regional Directors: Regional-level access  
- Executives: Portfolio-level access
- Admins: Full system access

## Rate Limiting

API endpoints are rate limited per user/IP:
- Dashboard endpoints: 60 requests/minute
- Analysis endpoints: 30 requests/minute
- Report generation: 20 requests/minute
- General endpoints: 300 requests/minute

## Error Handling

Standard HTTP status codes with detailed error responses:
- 400: Bad Request (validation errors)
- 401: Unauthorized (authentication required)
- 403: Forbidden (insufficient permissions)
- 404: Not Found (resource not found)
- 429: Too Many Requests (rate limit exceeded)
- 500: Internal Server Error

## Pagination

List endpoints support pagination with query parameters:
- `limit`: Number of items per page (default: 50, max: 500)
- `offset`: Number of items to skip (default: 0)

## Filtering

List endpoints support filtering with query parameters:
- Date ranges: `date_from`, `date_to`
- Status filtering: `status_filter`
- Property filtering: `property_ids[]`
- Priority filtering: `priority_filter`
"""

# Common response models and utilities will be imported here
from typing import Dict, Any, List, Optional
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field


class PaginationInfo(BaseModel):
    """Standard pagination information"""
    limit: int = Field(..., description="Items per page")
    offset: int = Field(..., description="Items skipped")
    total_count: int = Field(..., description="Total number of items")
    has_next: bool = Field(..., description="Whether there are more items")
    has_previous: bool = Field(..., description="Whether there are previous items")


class APIMetadata(BaseModel):
    """Standard API response metadata"""
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    api_version: str = Field(API_VERSION, description="API version")
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    execution_time_ms: Optional[float] = Field(None, description="Execution time in milliseconds")


class StandardListResponse(BaseModel):
    """Standard list response wrapper"""
    data: List[Any] = Field(..., description="Response data items")
    pagination: PaginationInfo = Field(..., description="Pagination information")
    metadata: APIMetadata = Field(default_factory=APIMetadata, description="Response metadata")


# Common validation utilities
def validate_contract_id(contract_id: str) -> bool:
    """Validate contract ID format"""
    import re
    pattern = r'^CNT-\d{3,6}$'
    return bool(re.match(pattern, contract_id))


def validate_property_id(property_id: str) -> bool:
    """Validate property ID format"""
    import re
    pattern = r'^PROP-\d{3,6}$'
    return bool(re.match(pattern, property_id))


def validate_cost_amount(amount: Decimal) -> bool:
    """Validate cost amount is reasonable"""
    return Decimal("0") <= amount <= Decimal("1000000")  # $0 to $1M monthly max


def validate_percentage(percentage: Decimal) -> bool:
    """Validate percentage is in valid range"""
    return Decimal("0") <= percentage <= Decimal("100")


# Common error messages
ERROR_MESSAGES = {
    "CONTRACT_NOT_FOUND": "The specified contract was not found or you don't have access to it",
    "PROPERTY_ACCESS_DENIED": "You don't have permission to access this property",
    "INSUFFICIENT_PERMISSIONS": "Your user role doesn't have permission for this operation",
    "INVALID_CONTRACT_ID": "Contract ID must be in format CNT-XXXXXX",
    "INVALID_PROPERTY_ID": "Property ID must be in format PROP-XXXXXX",
    "INVALID_DATE_RANGE": "End date must be after start date",
    "ANALYSIS_IN_PROGRESS": "An analysis is already in progress for this contract",
    "RATE_LIMIT_EXCEEDED": "Too many requests. Please wait before trying again",
    "EXTERNAL_SERVICE_ERROR": "External service temporarily unavailable",
}

# Industry constants for validation
INDUSTRY_CONSTANTS = {
    "MIN_CONTAINER_SIZE": 2.0,      # cubic yards
    "MAX_CONTAINER_SIZE": 40.0,     # cubic yards  
    "MIN_PICKUP_FREQUENCY": 1,      # times per week
    "MAX_PICKUP_FREQUENCY": 7,      # times per week
    "MIN_MONTHLY_COST": 100.0,      # dollars
    "MAX_MONTHLY_COST": 50000.0,    # dollars
    "MIN_UNITS": 1,                 # property units
    "MAX_UNITS": 2000,              # property units
    "STANDARD_CONTAINER_SIZES": [2, 4, 6, 8, 10, 15, 20, 30, 34, 40],
    "VALID_CONTRACT_TYPES": [
        "front_load_waste",
        "rear_load_waste", 
        "compactor_waste",
        "recycling",
        "organic_waste",
        "bulk_waste"
    ]
}