"""
Comprehensive Test Suite for Advantage Waste Renewal Logic
=========================================================

Unit and integration tests for contract renewal analysis, business logic validation,
and financial calculation accuracy using enterprise test standards.

Built by Testing & QA Agent - Enterprise Development Force
"""

import pytest
import asyncio
from decimal import Decimal
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List

# Test imports
from src.services.renewal_analyzer import EnterpriseRenewalAnalyzer, RenewalRecommendation
from src.models.renewal import (
    Contract, Property, Vendor, RenewalAnalysis, RenewalAlert,
    RecommendationType, RenewalStatus, AlertType
)
from src.tasks.renewal_scanner import (
    scan_contract_expirations, schedule_contract_analysis,
    process_notification_queue
)
from src.services.email_service import EmailService, EmailTemplate, EmailRecipient
from examples.contract_analysis_example import ContractAnalyzer, ContractTerms, PropertyInfo

# Test fixtures and data factories

@pytest.fixture
def sample_property():
    """Sample property for testing"""
    return Property(
        id="prop-123",
        name="Columbia Square Living",
        address="123 Main St",
        city="Dallas",
        state="TX",
        zip_code="75201",
        unit_count=252,
        property_type="garden-style"
    )

@pytest.fixture
def sample_vendor():
    """Sample vendor for testing"""
    return Vendor(
        id="vendor-456",
        name="GFL Environmental",
        contact_email="<EMAIL>",
        contact_phone="555-0123",
        service_areas={"TX": ["Dallas", "Houston"], "GA": ["Atlanta"]}
    )

@pytest.fixture
def sample_contract(sample_property, sample_vendor):
    """Sample contract for testing with realistic waste management terms"""
    return Contract(
        id="contract-789",
        property_id=sample_property.id,
        vendor_id=sample_vendor.id,
        contract_number="GFL-2024-001",
        vendor_name="GFL Environmental",
        contact_name="John Smith",
        contact_email="<EMAIL>",
        
        # Contract dates
        contract_start_date=datetime(2024, 1, 1),
        contract_end_date=datetime(2024, 12, 31),
        contract_length_months=12,
        automatic_renewal=True,
        renewal_term_months=12,
        termination_notice_days=30,
        
        # Service configuration
        container_size_yards=34,
        container_type="compactor",
        container_quantity=1,
        pickup_frequency_weekly=1,
        
        # Financial terms using industry benchmarks
        base_monthly_rate=Decimal("4500.00"),  # $4,500/month = ~$21.43/door
        fuel_surcharge_percent=Decimal("8.0"),
        environmental_fee_percent=Decimal("5.0"),
        admin_fee=Decimal("50.00"),
        
        # Price protection
        cpi_increases=True,
        max_annual_increase_percent=Decimal("4.0"),
        
        # Renewal tracking
        renewal_status=RenewalStatus.PENDING,
        renewal_analysis_completed=False,
        
        # Relationships
        property=sample_property,
        vendor=sample_vendor
    )

@pytest.fixture
def mock_db_session():
    """Mock database session for testing"""
    session = Mock()
    session.query.return_value.filter.return_value.first.return_value = None
    session.commit = Mock()
    session.rollback = Mock()
    session.close = Mock()
    return session

class TestContractAnalyzer:
    """Test suite for contract analysis business logic"""
    
    def test_basic_metrics_calculation(self, sample_contract, sample_property):
        """Test core waste management metric calculations"""
        
        analyzer = ContractAnalyzer()
        
        # Convert to legacy format for testing
        contract_terms = ContractTerms(
            vendor_name=sample_contract.vendor_name,
            contact_name=sample_contract.contact_name,
            contact_info=sample_contract.contact_email,
            quote_number=sample_contract.contract_number,
            contract_length_months=sample_contract.contract_length_months,
            effective_date=sample_contract.contract_start_date,
            automatic_renewal=sample_contract.automatic_renewal,
            renewal_term_months=sample_contract.renewal_term_months,
            termination_notice_days=sample_contract.termination_notice_days,
            container_size_yards=sample_contract.container_size_yards,
            container_type=sample_contract.container_type,
            container_quantity=sample_contract.container_quantity,
            pickup_frequency_weekly=sample_contract.pickup_frequency_weekly,
            base_monthly_rate=sample_contract.base_monthly_rate,
            fuel_surcharge_percent=float(sample_contract.fuel_surcharge_percent),
            environmental_fee_percent=float(sample_contract.environmental_fee_percent),
            admin_fee=sample_contract.admin_fee,
            max_annual_increase_percent=float(sample_contract.max_annual_increase_percent)
        )
        
        property_info = PropertyInfo(
            name=sample_property.name,
            address=f"{sample_property.address}, {sample_property.city}",
            unit_count=sample_property.unit_count,
            property_type=sample_property.property_type
        )
        
        analysis = analyzer.analyze_contract(contract_terms, property_info)
        
        # Test volume calculation: 34 yards × 1 container × 1 pickup/week × 4.33 weeks/month
        expected_monthly_volume = 34 * 1 * 1 * 4.33
        assert abs(analysis["metrics"]["monthly_volume_yards"] - expected_monthly_volume) < 0.01
        
        # Test total cost calculation including fees
        base_cost = float(sample_contract.base_monthly_rate)
        fuel_fee = base_cost * 0.08  # 8% fuel surcharge
        env_fee = base_cost * 0.05   # 5% environmental fee
        admin_fee = float(sample_contract.admin_fee)
        expected_total_cost = base_cost + fuel_fee + env_fee + admin_fee
        
        assert abs(analysis["metrics"]["total_monthly_cost"] - expected_total_cost) < 0.01
        
        # Test cost per door: Total monthly cost / 252 units
        expected_cost_per_door = expected_total_cost / 252
        assert abs(analysis["metrics"]["cost_per_door"] - expected_cost_per_door) < 0.01
        
        # Test cost per yard
        expected_cost_per_yard = expected_total_cost / expected_monthly_volume
        assert abs(analysis["metrics"]["cost_per_yard"] - expected_cost_per_yard) < 0.01
    
    def test_benchmark_comparison(self, sample_contract, sample_property):
        """Test industry benchmark comparison logic"""
        
        analyzer = ContractAnalyzer()
        
        # Create contract terms
        contract_terms = ContractTerms(
            vendor_name=sample_contract.vendor_name,
            contact_name=sample_contract.contact_name,
            contact_info=sample_contract.contact_email,
            quote_number=sample_contract.contract_number,
            contract_length_months=sample_contract.contract_length_months,
            effective_date=sample_contract.contract_start_date,
            automatic_renewal=sample_contract.automatic_renewal,
            renewal_term_months=sample_contract.renewal_term_months,
            termination_notice_days=sample_contract.termination_notice_days,
            container_size_yards=sample_contract.container_size_yards,
            container_type=sample_contract.container_type,
            container_quantity=sample_contract.container_quantity,
            pickup_frequency_weekly=sample_contract.pickup_frequency_weekly,
            base_monthly_rate=sample_contract.base_monthly_rate,
            fuel_surcharge_percent=float(sample_contract.fuel_surcharge_percent),
            environmental_fee_percent=float(sample_contract.environmental_fee_percent),
            admin_fee=sample_contract.admin_fee,
            max_annual_increase_percent=float(sample_contract.max_annual_increase_percent)
        )
        
        property_info = PropertyInfo(
            name=sample_property.name,
            address=f"{sample_property.address}, {sample_property.city}",
            unit_count=sample_property.unit_count,
            property_type=sample_property.property_type
        )
        
        analysis = analyzer.analyze_contract(contract_terms, property_info)
        
        # Test benchmark ranges for garden-style properties
        benchmark_analysis = analysis["benchmark_analysis"]
        
        # Garden-style cost per door benchmark: $20-30
        cost_benchmark = benchmark_analysis["cost_per_door"]["benchmark_range"]
        assert cost_benchmark == (20, 30)
        
        # Garden-style volume per door benchmark: 2.0-2.25 yd³
        volume_benchmark = benchmark_analysis["yards_per_door"]["benchmark_range"]
        assert volume_benchmark == (2.0, 2.25)
        
        # Check status determination
        cost_per_door = analysis["metrics"]["cost_per_door"]
        if 20 <= cost_per_door <= 30:
            assert benchmark_analysis["cost_per_door"]["status"] == "within_range"
        else:
            assert benchmark_analysis["cost_per_door"]["status"] == "outside_range"
    
    def test_contract_terms_analysis(self, sample_contract, sample_property):
        """Test contract terms quality assessment"""
        
        analyzer = ContractAnalyzer()
        
        # Test with good contract terms
        contract_terms = ContractTerms(
            vendor_name=sample_contract.vendor_name,
            contact_name=sample_contract.contact_name,
            contact_info=sample_contract.contact_email,
            quote_number=sample_contract.contract_number,
            contract_length_months=18,  # Good length (12-24 months)
            effective_date=sample_contract.contract_start_date,
            automatic_renewal=sample_contract.automatic_renewal,
            renewal_term_months=sample_contract.renewal_term_months,
            termination_notice_days=60,  # Reasonable notice period
            container_size_yards=sample_contract.container_size_yards,
            container_type=sample_contract.container_type,
            container_quantity=sample_contract.container_quantity,
            pickup_frequency_weekly=sample_contract.pickup_frequency_weekly,
            base_monthly_rate=sample_contract.base_monthly_rate,
            fuel_surcharge_percent=3.0,  # Low fuel surcharge
            environmental_fee_percent=0.0,  # No environmental fee
            max_annual_increase_percent=3.5,  # Good price protection
            cpi_increases=True
        )
        
        property_info = PropertyInfo(
            name=sample_property.name,
            address=f"{sample_property.address}, {sample_property.city}",
            unit_count=sample_property.unit_count,
            property_type=sample_property.property_type
        )
        
        analysis = analyzer.analyze_contract(contract_terms, property_info)
        terms_analysis = analysis["terms_analysis"]
        
        # Test contract length assessment
        assert terms_analysis["contract_length"]["status"] == "optimal"
        assert terms_analysis["contract_length"]["months"] == 18
        
        # Test price increase protection
        assert terms_analysis["price_increases"]["status"] == "good"
        assert terms_analysis["price_increases"]["max_annual_percent"] == 3.5
        
        # Test fuel surcharge assessment
        assert terms_analysis["fuel_surcharge"]["status"] == "good"
        assert terms_analysis["fuel_surcharge"]["percent"] == 3.0

class TestEnterpriseRenewalAnalyzer:
    """Test suite for enterprise renewal analysis engine"""
    
    @pytest.mark.asyncio
    async def test_renewal_opportunity_analysis(self, sample_contract, sample_property, mock_db_session):
        """Test comprehensive renewal opportunity analysis"""
        
        analyzer = EnterpriseRenewalAnalyzer(mock_db_session)
        
        # Mock alternative vendors
        alternative_vendors = [
            Vendor(
                id="alt-vendor-1",
                name="Waste Management",
                contact_email="<EMAIL>",
                service_areas={"TX": ["Dallas"]}
            )
        ]
        
        # Perform analysis
        analysis = analyzer.analyze_renewal_opportunity(
            contract=sample_contract,
            property_info=sample_property,
            market_data=None,
            alternative_vendors=alternative_vendors
        )
        
        # Verify analysis structure
        assert isinstance(analysis, RenewalAnalysis)
        assert analysis.contract_id == sample_contract.id
        assert analysis.current_performance_grade is not None
        assert analysis.primary_recommendation in [r.value for r in RecommendationType]
        assert analysis.recommendation_confidence >= 0
        assert analysis.recommendation_confidence <= 100
        
        # Verify financial calculations
        assert analysis.cost_per_door > 0
        assert analysis.annual_cost > 0
        assert analysis.monthly_volume_yards > 0
        
        # Verify data quality
        assert analysis.data_quality_score >= 0
        assert analysis.data_quality_score <= 100
    
    def test_risk_assessment(self, sample_contract, sample_property, mock_db_session):
        """Test contract renewal risk assessment"""
        
        analyzer = EnterpriseRenewalAnalyzer(mock_db_session)
        
        # Test high-risk scenario (overdue contract)
        overdue_contract = sample_contract
        overdue_contract.contract_end_date = datetime.utcnow() - timedelta(days=30)
        
        risk_assessment = analyzer._assess_renewal_risks(overdue_contract, {})
        
        # Should identify high risk for overdue contract
        assert risk_assessment["overall_risk"] in ["high", "very_high"]
        assert risk_assessment["risk_score"] > 0.5
        
        # Verify risk categories
        assert "service_disruption" in risk_assessment["risk_categories"]
        assert "cost_volatility" in risk_assessment["risk_categories"]
        assert "vendor_reliability" in risk_assessment["risk_categories"]
    
    def test_financial_projections(self, sample_contract, sample_property, mock_db_session):
        """Test financial projection calculations"""
        
        analyzer = EnterpriseRenewalAnalyzer(mock_db_session)
        
        # Mock base analysis
        base_analysis = {
            "metrics": {
                "annual_cost": 54000.0,  # $4,500 * 12
                "total_monthly_cost": 4500.0
            },
            "optimization_areas": []
        }
        
        # Mock vendor alternatives
        vendor_alternatives = {
            "best_alternative": {
                "estimated_annual_cost": 48000.0,  # $6K savings
                "potential_savings": 6000.0
            }
        }
        
        projections = analyzer._calculate_financial_projections(
            sample_contract, base_analysis, vendor_alternatives
        )
        
        # Verify projection structure
        assert "current_trajectory" in projections
        assert "optimized_scenario" in projections
        assert "three_year_savings" in projections
        assert "roi_analysis" in projections
        
        # Verify savings calculations
        assert projections["three_year_savings"] > 0
        assert projections["roi_analysis"]["optimization_roi"] >= 0

class TestRenewalScanner:
    """Test suite for automated renewal scanning"""
    
    @pytest.mark.asyncio
    async def test_contract_expiration_scanning(self, mock_db_session):
        """Test daily contract expiration scanning logic"""
        
        # Mock contracts expiring in different timeframes
        contracts = [
            Mock(
                id="contract-1",
                contract_number="TEST-001",
                contract_end_date=datetime.utcnow() + timedelta(days=90),
                renewal_status=RenewalStatus.PENDING,
                renewal_analysis_completed=False,
                property=Mock(name="Property 1"),
                vendor=Mock(name="Vendor 1")
            ),
            Mock(
                id="contract-2", 
                contract_number="TEST-002",
                contract_end_date=datetime.utcnow() + timedelta(days=30),
                renewal_status=RenewalStatus.PENDING,
                renewal_analysis_completed=False,
                property=Mock(name="Property 2"),
                vendor=Mock(name="Vendor 2")
            ),
            Mock(
                id="contract-3",
                contract_number="TEST-003",
                contract_end_date=datetime.utcnow() - timedelta(days=5),  # Overdue
                renewal_status=RenewalStatus.PENDING,
                renewal_analysis_completed=False,
                property=Mock(name="Property 3"),
                vendor=Mock(name="Vendor 3")
            )
        ]
        
        # Mock database query
        mock_db_session.query.return_value.filter.return_value.join.return_value.join.return_value.all.return_value = contracts
        
        with patch('src.tasks.renewal_scanner.get_database_session', return_value=mock_db_session):
            with patch('src.tasks.renewal_scanner.process_notification_queue.delay'):
                # This would normally be called as a Celery task
                # For testing, we'll simulate the core logic
                
                alerts_created = 0
                analyses_scheduled = 0
                
                for contract in contracts:
                    days_to_expiration = (contract.contract_end_date - datetime.utcnow()).days
                    
                    # Simulate alert creation logic
                    if days_to_expiration <= 90:
                        alerts_created += 1
                    
                    # Simulate analysis scheduling for 90-day alerts
                    if days_to_expiration <= 90 and not contract.renewal_analysis_completed:
                        analyses_scheduled += 1
                
                # Verify expected results
                assert alerts_created == 3  # All three contracts need alerts
                assert analyses_scheduled == 3  # All need analysis
    
    def test_alert_priority_calculation(self):
        """Test alert priority calculation logic"""
        
        # Mock contract with high annual cost
        high_value_contract = Mock(
            base_monthly_rate=Decimal("10000.00")  # $120K annual
        )
        
        # Mock contract with low annual cost
        low_value_contract = Mock(
            base_monthly_rate=Decimal("1000.00")  # $12K annual
        )
        
        # Test priority for different scenarios
        from src.tasks.renewal_scanner import RenewalScannerTask
        scanner = RenewalScannerTask()
        
        # Critical timeframe + high value = critical priority
        priority = scanner._calculate_alert_priority(high_value_contract, 10)
        assert priority == "critical"
        
        # Medium timeframe + high value = high priority  
        priority = scanner._calculate_alert_priority(high_value_contract, 45)
        assert priority == "high"
        
        # Medium timeframe + low value = medium priority
        priority = scanner._calculate_alert_priority(low_value_contract, 45)
        assert priority == "medium"

class TestEmailService:
    """Test suite for email notification system"""
    
    @pytest.mark.asyncio
    async def test_renewal_notification_generation(self, sample_contract, sample_property):
        """Test renewal notification email generation"""
        
        email_service = EmailService()
        
        # Mock template environment
        with patch.object(email_service, 'template_env') as mock_env:
            mock_template = Mock()
            mock_template.render.return_value = "Test email content"
            mock_env.get_template.return_value = mock_template
            
            # Mock SMTP sending
            with patch.object(email_service, '_send_email', return_value=True) as mock_send:
                recipient = EmailRecipient(
                    email="<EMAIL>",
                    name="Test Manager",
                    role="property_manager"
                )
                
                alert = RenewalAlert(
                    id="alert-123",
                    contract_id=sample_contract.id,
                    alert_type=AlertType.NINETY_DAY.value,
                    days_before_expiration=90,
                    priority_level="medium"
                )
                
                success = await email_service.send_renewal_notification(
                    recipient=recipient,
                    contract=sample_contract,
                    alert=alert
                )
                
                assert success is True
                mock_send.assert_called_once()
                
                # Verify email parameters
                call_args = mock_send.call_args[1]
                assert call_args['to_email'] == "<EMAIL>"
                assert call_args['to_name'] == "Test Manager"
                assert 'subject' in call_args
                assert 'html_body' in call_args
                assert 'text_body' in call_args
    
    def test_currency_formatting(self):
        """Test currency formatting for email templates"""
        
        email_service = EmailService()
        
        # Test various currency amounts
        assert email_service._format_currency(1500.00) == "$1,500.00"
        assert email_service._format_currency(15000.00) == "$15K"
        assert email_service._format_currency(1500000.00) == "$1.5M"
        assert email_service._format_currency(2750000.00) == "$2.8M"
    
    def test_urgency_color_mapping(self):
        """Test urgency level color mapping"""
        
        email_service = EmailService()
        
        assert email_service._get_urgency_color("critical") == "#dc2626"  # Red
        assert email_service._get_urgency_color("high") == "#ea580c"      # Orange
        assert email_service._get_urgency_color("medium") == "#ca8a04"    # Yellow
        assert email_service._get_urgency_color("low") == "#16a34a"       # Green
        assert email_service._get_urgency_color("unknown") == "#6b7280"   # Gray default

class TestIntegration:
    """Integration tests for end-to-end workflows"""
    
    @pytest.mark.asyncio
    async def test_complete_renewal_workflow(self, sample_contract, sample_property, mock_db_session):
        """Test complete renewal workflow from detection to notification"""
        
        # Step 1: Contract expiration detection
        days_to_expiration = 90
        sample_contract.contract_end_date = datetime.utcnow() + timedelta(days=days_to_expiration)
        
        # Step 2: Alert creation
        alert = RenewalAlert(
            id="alert-test",
            contract_id=sample_contract.id,
            alert_type=AlertType.NINETY_DAY.value,
            days_before_expiration=days_to_expiration,
            priority_level="medium"
        )
        
        # Step 3: Analysis scheduling and execution
        analyzer = EnterpriseRenewalAnalyzer(mock_db_session)
        analysis = analyzer.analyze_renewal_opportunity(
            contract=sample_contract,
            property_info=sample_property
        )
        
        # Step 4: Notification generation
        email_service = EmailService()
        recipient = EmailRecipient(
            email="<EMAIL>",
            name="Test Manager", 
            role="property_manager"
        )
        
        with patch.object(email_service, '_send_email', return_value=True):
            notification_success = await email_service.send_renewal_notification(
                recipient=recipient,
                contract=sample_contract,
                alert=alert
            )
        
        # Verify workflow completion
        assert isinstance(analysis, RenewalAnalysis)
        assert analysis.primary_recommendation is not None
        assert notification_success is True
    
    def test_performance_with_large_dataset(self, mock_db_session):
        """Test system performance with enterprise-scale data"""
        
        # Simulate 1000 contracts (representative of large property portfolio)
        large_contract_set = []
        for i in range(1000):
            contract = Mock(
                id=f"contract-{i}",
                contract_number=f"TEST-{i:04d}",
                contract_end_date=datetime.utcnow() + timedelta(days=(i % 120)),  # Spread over 120 days
                renewal_status=RenewalStatus.PENDING,
                base_monthly_rate=Decimal("4500.00"),
                property=Mock(name=f"Property {i}", unit_count=250),
                vendor=Mock(name=f"Vendor {i % 10}")  # 10 vendors total
            )
            large_contract_set.append(contract)
        
        # Test batch processing performance
        batch_size = 100
        processed_count = 0
        
        start_time = datetime.utcnow()
        
        for i in range(0, len(large_contract_set), batch_size):
            batch = large_contract_set[i:i + batch_size]
            
            # Simulate processing each contract in batch
            for contract in batch:
                days_to_expiration = (contract.contract_end_date - datetime.utcnow()).days
                if days_to_expiration <= 90:
                    processed_count += 1
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        # Performance assertions
        assert processed_count > 0
        assert processing_time < 5.0  # Should process 1000 contracts in under 5 seconds
        assert processing_time / len(large_contract_set) < 0.01  # Less than 10ms per contract

# Test data validation

class TestDataValidation:
    """Test suite for data validation and business rules"""
    
    def test_container_size_validation(self):
        """Test container size validation against industry standards"""
        
        # Valid container sizes (cubic yards)
        valid_sizes = [2, 4, 6, 8, 10, 15, 20, 30, 34, 40]
        invalid_sizes = [3, 5, 7, 9, 25, 50]
        
        for size in valid_sizes:
            # Should not raise validation error
            assert size in [2, 4, 6, 8, 10, 15, 20, 30, 34, 40]
        
        for size in invalid_sizes:
            # Should raise validation warning
            assert size not in [2, 4, 6, 8, 10, 15, 20, 30, 34, 40]
    
    def test_pickup_frequency_validation(self):
        """Test pickup frequency validation"""
        
        valid_frequencies = [1, 2, 3, 4, 5, 6, 7]  # 1-7 times per week
        invalid_frequencies = [0, 8, 10, -1]
        
        for freq in valid_frequencies:
            assert 1 <= freq <= 7
        
        for freq in invalid_frequencies:
            assert not (1 <= freq <= 7)
    
    def test_financial_precision(self):
        """Test financial calculation precision using Decimal"""
        
        # Test that we're using Decimal for financial calculations
        base_rate = Decimal("4500.00")
        fuel_surcharge = Decimal("8.0")  # 8%
        
        # Calculate fuel surcharge amount
        fuel_amount = base_rate * (fuel_surcharge / Decimal("100"))
        expected_fuel_amount = Decimal("360.00")  # $4500 * 0.08
        
        assert fuel_amount == expected_fuel_amount
        assert isinstance(fuel_amount, Decimal)
        
        # Test precision maintenance
        cost_per_door = base_rate / Decimal("252")  # 252 units
        assert isinstance(cost_per_door, Decimal)
        assert cost_per_door.quantize(Decimal('0.01')) == Decimal("17.86")

# Performance and load testing

@pytest.mark.performance
class TestPerformance:
    """Performance tests for enterprise-scale operations"""
    
    def test_analysis_performance(self, sample_contract, sample_property, mock_db_session):
        """Test analysis performance for single contract"""
        
        analyzer = EnterpriseRenewalAnalyzer(mock_db_session)
        
        start_time = datetime.utcnow()
        
        analysis = analyzer.analyze_renewal_opportunity(
            contract=sample_contract,
            property_info=sample_property
        )
        
        end_time = datetime.utcnow()
        analysis_time = (end_time - start_time).total_seconds()
        
        # Performance requirements
        assert analysis_time < 1.0  # Analysis should complete in under 1 second
        assert isinstance(analysis, RenewalAnalysis)
    
    @pytest.mark.asyncio
    async def test_notification_performance(self):
        """Test email notification performance"""
        
        email_service = EmailService()
        
        # Mock fast email sending
        with patch.object(email_service, '_send_email', return_value=True) as mock_send:
            recipients = [
                EmailRecipient(f"test{i}@greystar.com", f"Test User {i}", "property_manager")
                for i in range(50)  # 50 notifications
            ]
            
            start_time = datetime.utcnow()
            
            # Send notifications
            tasks = []
            for recipient in recipients:
                task = email_service.send_renewal_notification(
                    recipient=recipient,
                    contract=Mock(
                        id=f"contract-{recipient.email}",
                        contract_number="TEST-001",
                        property=Mock(name="Test Property", unit_count=250),
                        vendor_name="Test Vendor",
                        base_monthly_rate=Decimal("4500.00"),
                        contract_end_date=datetime.utcnow() + timedelta(days=90)
                    ),
                    alert=Mock(
                        id=f"alert-{recipient.email}",
                        days_before_expiration=90,
                        priority_level="medium"
                    )
                )
                tasks.append(task)
            
            # Execute all notifications
            results = await asyncio.gather(*tasks)
            
            end_time = datetime.utcnow()
            total_time = (end_time - start_time).total_seconds()
            
            # Performance assertions
            assert all(results)  # All notifications succeeded
            assert total_time < 10.0  # 50 notifications in under 10 seconds
            assert total_time / len(recipients) < 0.5  # Less than 0.5 seconds per notification

# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])