"""
Background task execution tracking models for Advantage Waste Enterprise.
Monitors Celery task execution, performance, and health status.
"""

from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, JSON,
    Numeric, Index, Enum
)
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any

from ..core.database import Base
from .base import TimestampMixin


class TaskStatus(PyEnum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"
    REVOKED = "revoked"


class TaskPriority(PyEnum):
    """Task priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TaskExecution(Base, TimestampMixin):
    """
    Background task execution tracking model.
    Monitors all Celery task executions for performance and debugging.
    """
    __tablename__ = "task_executions"

    id = Column(Integer, primary_key=True, index=True)
    
    # Task identification
    task_id = Column(String(100), unique=True, nullable=False, index=True)
    task_name = Column(String(100), nullable=False, index=True)
    task_type = Column(String(50), nullable=False, index=True)  # renewal_scan, analysis, notification
    
    # Task scheduling
    scheduled_at = Column(DateTime, nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Task status and priority
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, nullable=False, index=True)
    priority = Column(Enum(TaskPriority), default=TaskPriority.MEDIUM, nullable=False)
    
    # Task parameters and context
    task_args = Column(JSON, nullable=True)  # Task arguments
    task_kwargs = Column(JSON, nullable=True)  # Task keyword arguments
    context_data = Column(JSON, nullable=True)  # Additional context
    
    # Execution details
    worker_name = Column(String(100), nullable=True)
    queue_name = Column(String(50), nullable=True, default="default")
    execution_time = Column(Numeric(8, 3), nullable=True)  # Seconds
    
    # Results and errors
    result = Column(JSON, nullable=True)  # Task result data
    error_message = Column(Text, nullable=True)
    error_traceback = Column(Text, nullable=True)
    
    # Retry tracking
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    retry_delay = Column(Integer, nullable=True)  # Seconds
    next_retry_at = Column(DateTime, nullable=True)
    
    # Performance metrics
    memory_usage_mb = Column(Numeric(8, 2), nullable=True)
    cpu_time = Column(Numeric(8, 3), nullable=True)  # CPU seconds
    database_queries = Column(Integer, nullable=True)
    external_api_calls = Column(Integer, nullable=True)
    
    # Business metrics (for specific task types)
    records_processed = Column(Integer, nullable=True)
    emails_sent = Column(Integer, nullable=True)
    contracts_analyzed = Column(Integer, nullable=True)
    
    # Monitoring flags
    is_critical = Column(Boolean, default=False)
    alert_sent = Column(Boolean, default=False)
    requires_attention = Column(Boolean, default=False)
    
    # Additional metadata
    execution_metadata = Column(JSON, nullable=True)
    parent_task_id = Column(String(100), nullable=True, index=True)
    child_task_ids = Column(JSON, nullable=True)  # Array of child task IDs
    
    # Indexes for performance monitoring
    __table_args__ = (
        Index("idx_task_status_type", "status", "task_type"),
        Index("idx_task_scheduled_date", "scheduled_at"),
        Index("idx_task_execution_time", "execution_time"),
        Index("idx_task_critical_failed", "is_critical", "status"),
        Index("idx_task_retry", "status", "next_retry_at"),
    )
    
    @property
    def duration(self) -> Optional[timedelta]:
        """Calculate task execution duration"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None
    
    @property
    def queue_time(self) -> Optional[timedelta]:
        """Calculate time spent in queue before execution"""
        if self.created_at and self.started_at:
            return self.started_at - self.created_at
        return None
    
    @property
    def is_successful(self) -> bool:
        """Check if task completed successfully"""
        return self.status == TaskStatus.SUCCESS
    
    @property
    def is_failed(self) -> bool:
        """Check if task failed"""
        return self.status == TaskStatus.FAILED
    
    @property
    def should_retry(self) -> bool:
        """Check if task should be retried"""
        return (
            self.status == TaskStatus.FAILED and
            self.retry_count < self.max_retries and
            self.next_retry_at and
            self.next_retry_at <= datetime.utcnow()
        )
    
    @property
    def performance_score(self) -> float:
        """Calculate performance score based on execution time and success rate"""
        if not self.execution_time:
            return 0.0
        
        # Base score from execution time (faster = better)
        time_score = max(0, 10 - (float(self.execution_time) / 60))  # 10 points for <1 minute
        
        # Success bonus/penalty
        if self.is_successful:
            success_bonus = 2.0
        elif self.is_failed and self.retry_count == 0:
            success_bonus = -3.0  # First failure
        else:
            success_bonus = -5.0  # Multiple failures
        
        return max(0, min(10, time_score + success_bonus))
    
    def mark_started(self, worker_name: str, queue_name: str = "default"):
        """Mark task as started"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.utcnow()
        self.worker_name = worker_name
        self.queue_name = queue_name
    
    def mark_completed(self, result: Dict[str, Any], execution_time: float):
        """Mark task as completed successfully"""
        self.status = TaskStatus.SUCCESS
        self.completed_at = datetime.utcnow()
        self.result = result
        self.execution_time = execution_time
    
    def mark_failed(self, error_message: str, traceback: str = None, retry: bool = True):
        """Mark task as failed"""
        if retry and self.retry_count < self.max_retries:
            self.status = TaskStatus.RETRY
            self.retry_count += 1
            # Exponential backoff: 2^retry_count minutes
            delay_minutes = 2 ** self.retry_count
            self.next_retry_at = datetime.utcnow() + timedelta(minutes=delay_minutes)
        else:
            self.status = TaskStatus.FAILED
            self.completed_at = datetime.utcnow()
        
        self.error_message = error_message
        self.error_traceback = traceback
        
        # Flag for attention if critical task fails
        if self.is_critical:
            self.requires_attention = True