"""
Core Exceptions
==============

Custom exception classes for the Advantage Waste Enterprise API.
Provides structured error handling with proper HTTP status codes.
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, status


class AdvantageWasteException(Exception):
    """Base exception for Advantage Waste application"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "GENERAL_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ContractNotFoundException(AdvantageWasteException):
    """Raised when a contract is not found"""
    
    def __init__(self, contract_id: str):
        super().__init__(
            message=f"Contract {contract_id} not found",
            error_code="CONTRACT_NOT_FOUND",
            details={"contract_id": contract_id}
        )


class PropertyAccessDeniedException(AdvantageWasteException):
    """Raised when user lacks access to a property"""
    
    def __init__(self, property_id: str, user_id: str):
        super().__init__(
            message=f"Access denied to property {property_id}",
            error_code="PROPERTY_ACCESS_DENIED",
            details={"property_id": property_id, "user_id": user_id}
        )


class InsufficientPermissionsException(AdvantageWasteException):
    """Raised when user lacks required permissions"""
    
    def __init__(self, required_permission: str, user_role: str):
        super().__init__(
            message=f"Insufficient permissions. Required: {required_permission}",
            error_code="INSUFFICIENT_PERMISSIONS", 
            details={"required_permission": required_permission, "user_role": user_role}
        )


class AnalysisFailedException(AdvantageWasteException):
    """Raised when contract analysis fails"""
    
    def __init__(self, contract_id: str, reason: str):
        super().__init__(
            message=f"Analysis failed for contract {contract_id}: {reason}",
            error_code="ANALYSIS_FAILED",
            details={"contract_id": contract_id, "reason": reason}
        )


class ValidationException(AdvantageWasteException):
    """Raised when input validation fails"""
    
    def __init__(self, field: str, value: Any, reason: str):
        super().__init__(
            message=f"Validation failed for {field}: {reason}",
            error_code="VALIDATION_ERROR",
            details={"field": field, "value": str(value), "reason": reason}
        )


class RateLimitExceededException(AdvantageWasteException):
    """Raised when rate limit is exceeded"""
    
    def __init__(self, endpoint: str, retry_after: int):
        super().__init__(
            message=f"Rate limit exceeded for {endpoint}. Retry after {retry_after} seconds",
            error_code="RATE_LIMIT_EXCEEDED",
            details={"endpoint": endpoint, "retry_after": retry_after}
        )


class ExternalServiceException(AdvantageWasteException):
    """Raised when external service calls fail"""
    
    def __init__(self, service: str, operation: str, reason: str):
        super().__init__(
            message=f"External service error: {service} {operation} failed - {reason}",
            error_code="EXTERNAL_SERVICE_ERROR",
            details={"service": service, "operation": operation, "reason": reason}
        )


def to_http_exception(exc: AdvantageWasteException) -> HTTPException:
    """Convert AdvantageWasteException to HTTPException"""
    
    status_mapping = {
        "CONTRACT_NOT_FOUND": status.HTTP_404_NOT_FOUND,
        "PROPERTY_ACCESS_DENIED": status.HTTP_403_FORBIDDEN,
        "INSUFFICIENT_PERMISSIONS": status.HTTP_403_FORBIDDEN,
        "VALIDATION_ERROR": status.HTTP_400_BAD_REQUEST,
        "RATE_LIMIT_EXCEEDED": status.HTTP_429_TOO_MANY_REQUESTS,
        "ANALYSIS_FAILED": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "EXTERNAL_SERVICE_ERROR": status.HTTP_502_BAD_GATEWAY,
        "GENERAL_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
    }
    
    status_code = status_mapping.get(exc.error_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return HTTPException(
        status_code=status_code,
        detail={
            "error": exc.error_code,
            "message": exc.message,
            "details": exc.details
        }
    )