"""
Celery application configuration for Advantage Waste Enterprise.
Provides distributed task queue for background processing and automation.
"""

import os
from celery import Celery
from celery.schedules import crontab
from kombu import Queue, Exchange
import structlog

logger = structlog.get_logger()

# Celery configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", REDIS_URL)
CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", REDIS_URL)

# Create Celery app
celery_app = Celery(
    "advantage_waste_enterprise",
    broker=CELERY_BROKER_URL,
    backend=CELERY_RESULT_BACKEND,
    include=[
        "backend.src.tasks.renewal_scanner",
        "backend.src.tasks.analysis_scheduler", 
        "backend.src.tasks.email_notifications",
        "backend.src.tasks.vendor_performance",
        "backend.src.tasks.executive_reports",
        "backend.src.tasks.system_monitoring",
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task routing and queues
    task_routes={
        "tasks.renewal_scanner.*": {"queue": "renewal_scanning"},
        "tasks.analysis_scheduler.*": {"queue": "contract_analysis"},
        "tasks.email_notifications.*": {"queue": "notifications"},
        "tasks.vendor_performance.*": {"queue": "vendor_analysis"},
        "tasks.executive_reports.*": {"queue": "reporting"},
        "tasks.system_monitoring.*": {"queue": "monitoring"},
    },
    
    # Queue definitions with priorities
    task_queues=(
        Queue("renewal_scanning", Exchange("renewal_scanning"), routing_key="renewal_scanning", 
              queue_arguments={"x-max-priority": 10}),
        Queue("contract_analysis", Exchange("contract_analysis"), routing_key="contract_analysis",
              queue_arguments={"x-max-priority": 8}),
        Queue("notifications", Exchange("notifications"), routing_key="notifications",
              queue_arguments={"x-max-priority": 9}),
        Queue("vendor_analysis", Exchange("vendor_analysis"), routing_key="vendor_analysis",
              queue_arguments={"x-max-priority": 5}),
        Queue("reporting", Exchange("reporting"), routing_key="reporting",
              queue_arguments={"x-max-priority": 3}),
        Queue("monitoring", Exchange("monitoring"), routing_key="monitoring",
              queue_arguments={"x-max-priority": 7}),
    ),
    
    # Task execution settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="America/New_York",
    enable_utc=True,
    
    # Task behavior
    task_always_eager=False,  # Set to True for testing
    task_eager_propagates=True,
    task_ignore_result=False,
    task_store_eager_result=True,
    
    # Worker settings
    worker_prefetch_multiplier=4,
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # Result backend settings
    result_expires=3600 * 24 * 7,  # Keep results for 7 days
    result_persistent=True,
    
    # Task retry settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Monitoring and logging
    worker_send_task_events=True,
    task_send_sent_event=True,
    worker_log_format="[%(asctime)s: %(levelname)s/%(processName)s] %(message)s",
    worker_task_log_format="[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s",
    
    # Security
    worker_hijack_root_logger=False,
    worker_log_color=False,
    
    # Rate limiting
    task_annotations={
        "tasks.email_notifications.send_email": {"rate_limit": "100/m"},  # 100 emails per minute
        "tasks.renewal_scanner.scan_expiring_contracts": {"rate_limit": "1/m"},  # Once per minute max
        "tasks.vendor_performance.analyze_vendor_performance": {"rate_limit": "10/m"},  # 10 per minute
    },
)

# Periodic task schedule (Celery Beat)
celery_app.conf.beat_schedule = {
    # Daily contract renewal scanning at 6:00 AM EST
    "scan-expiring-contracts": {
        "task": "tasks.renewal_scanner.scan_expiring_contracts",
        "schedule": crontab(hour=6, minute=0),  # 6:00 AM EST
        "options": {"priority": 9, "queue": "renewal_scanning"},
    },
    
    # Daily contract analysis scheduling at 6:30 AM EST
    "schedule-contract-analyses": {
        "task": "tasks.analysis_scheduler.schedule_pending_analyses",
        "schedule": crontab(hour=6, minute=30),  # 6:30 AM EST
        "options": {"priority": 8, "queue": "contract_analysis"},
    },
    
    # Process notification queue every 15 minutes
    "process-notification-queue": {
        "task": "tasks.email_notifications.process_notification_queue",
        "schedule": crontab(minute="*/15"),  # Every 15 minutes
        "options": {"priority": 9, "queue": "notifications"},
    },
    
    # Check for escalations every hour during business hours
    "check-escalations": {
        "task": "tasks.email_notifications.check_escalations",
        "schedule": crontab(minute=0, hour="8-18"),  # Every hour from 8 AM to 6 PM
        "options": {"priority": 8, "queue": "notifications"},
    },
    
    # Weekly vendor performance analysis on Mondays at 7:00 AM
    "weekly-vendor-analysis": {
        "task": "tasks.vendor_performance.weekly_performance_analysis",
        "schedule": crontab(hour=7, minute=0, day_of_week=1),  # Monday 7:00 AM
        "options": {"priority": 5, "queue": "vendor_analysis"},
    },
    
    # Monthly executive summary on first day of month at 8:00 AM
    "monthly-executive-summary": {
        "task": "tasks.executive_reports.generate_monthly_summary",
        "schedule": crontab(hour=8, minute=0, day_of_month=1),  # 1st of month, 8:00 AM
        "options": {"priority": 6, "queue": "reporting"},
    },
    
    # System health monitoring every 5 minutes
    "system-health-check": {
        "task": "tasks.system_monitoring.health_check",
        "schedule": crontab(minute="*/5"),  # Every 5 minutes
        "options": {"priority": 7, "queue": "monitoring"},
    },
    
    # Task cleanup (remove old task records) daily at 2:00 AM
    "cleanup-old-tasks": {
        "task": "tasks.system_monitoring.cleanup_old_tasks",
        "schedule": crontab(hour=2, minute=0),  # 2:00 AM EST
        "options": {"priority": 3, "queue": "monitoring"},
    },
    
    # Retry failed notifications every 30 minutes
    "retry-failed-notifications": {
        "task": "tasks.email_notifications.retry_failed_notifications",
        "schedule": crontab(minute="*/30"),  # Every 30 minutes
        "options": {"priority": 7, "queue": "notifications"},
    },
}

# Error handling and logging
@celery_app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery configuration"""
    print(f"Request: {self.request!r}")
    logger.info("Debug task executed", task_id=self.request.id)
    return {"status": "success", "message": "Debug task completed"}


# Task failure callback
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None):
    """Handle task failures for monitoring and alerting"""
    logger.error(
        "Task failed",
        task_id=task_id,
        task_name=sender.name if sender else "unknown",
        error=str(exception),
        traceback=traceback
    )
    
    # Record failure in database
    from .database import get_sync_db
    from ..models.tasks import TaskExecution
    
    try:
        with next(get_sync_db()) as db:
            task_record = db.query(TaskExecution).filter(
                TaskExecution.task_id == task_id
            ).first()
            
            if task_record:
                task_record.mark_failed(
                    error_message=str(exception),
                    traceback=traceback,
                    retry=True
                )
                db.commit()
                
                # Send alert for critical task failures
                if task_record.is_critical:
                    from ..tasks.email_notifications import send_critical_task_alert
                    send_critical_task_alert.delay(task_id, str(exception))
                    
    except Exception as e:
        logger.error("Failed to record task failure", error=str(e))


# Task success callback
def task_success_handler(sender=None, result=None, **kwargs):
    """Handle successful task completion"""
    task_id = kwargs.get("task_id")
    
    logger.info(
        "Task completed successfully",
        task_id=task_id,
        task_name=sender.name if sender else "unknown",
        result_summary=str(result)[:200] if result else None
    )


# Connect signal handlers
from celery.signals import task_failure, task_success
task_failure.connect(task_failure_handler)
task_success.connect(task_success_handler)


# Health check function
def check_celery_health():
    """Check Celery worker and broker health"""
    try:
        # Check if workers are available
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if not stats:
            return {"status": "unhealthy", "message": "No active workers"}
        
        # Check Redis connectivity
        from redis import Redis
        redis_client = Redis.from_url(REDIS_URL)
        redis_client.ping()
        
        return {
            "status": "healthy",
            "workers": len(stats),
            "queues": list(celery_app.conf.task_routes.keys())
        }
        
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


if __name__ == "__main__":
    # Run worker with: celery -A backend.src.core.celery_app worker --loglevel=info
    # Run beat with: celery -A backend.src.core.celery_app beat --loglevel=info
    celery_app.start()