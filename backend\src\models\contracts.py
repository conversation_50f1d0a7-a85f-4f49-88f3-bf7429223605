"""
Contract-related database models for Advantage Waste Enterprise.
Handles waste management contracts, renewals, and analysis data.
"""

from sqlalchemy import (
    Column, Integer, String, Text, Numeric, Date, DateTime, Boolean,
    ForeignKey, Enum, JSON, Index
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from datetime import date, datetime
from typing import Optional, Dict, Any

from ..core.database import Base
from .base import TimestampMixin, AuditMixin


class ContractStatus(PyEnum):
    """Contract status enumeration"""
    ACTIVE = "active"
    EXPIRED = "expired"
    PENDING_RENEWAL = "pending_renewal"
    CANCELLED = "cancelled"
    DRAFT = "draft"


class ContractType(PyEnum):
    """Contract type enumeration"""
    COLLECTION = "collection"
    DISPOSAL = "disposal"
    RECYCLING = "recycling"
    COMPACTOR = "compactor"
    FULL_SERVICE = "full_service"


class RenewalStatus(PyEnum):
    """Renewal analysis status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    APPROVED = "approved"
    REJECTED = "rejected"


class Contract(Base, AuditMixin):
    """
    Waste management contract model.
    Represents contracts between Greystar properties and waste vendors.
    """
    __tablename__ = "contracts"

    id = Column(Integer, primary_key=True, index=True)
    
    # Contract identification
    contract_number = Column(String(50), unique=True, nullable=False, index=True)
    greystar_property_id = Column(String(20), nullable=False, index=True)
    vendor_id = Column(Integer, ForeignKey("vendors.id"), nullable=False)
    
    # Contract details
    contract_type = Column(Enum(ContractType), nullable=False)
    status = Column(Enum(ContractStatus), default=ContractStatus.ACTIVE, nullable=False)
    
    # Financial terms
    monthly_cost = Column(Numeric(10, 2), nullable=False)
    cost_per_door = Column(Numeric(8, 2), nullable=True)
    fuel_surcharge_rate = Column(Numeric(5, 4), nullable=True)  # As percentage
    environmental_fee = Column(Numeric(8, 2), nullable=True)
    
    # Service details
    container_size = Column(Numeric(4, 1), nullable=False)  # Cubic yards
    container_quantity = Column(Integer, nullable=False)
    pickup_frequency = Column(Integer, nullable=False)  # Times per week
    service_days = Column(JSON, nullable=True)  # Array of weekday numbers
    
    # Contract periods
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    auto_renewal = Column(Boolean, default=False)
    renewal_notice_days = Column(Integer, default=90)
    
    # Terms and conditions
    price_increase_cap = Column(Numeric(4, 2), nullable=True)  # As percentage
    early_termination_fee = Column(Numeric(10, 2), nullable=True)
    
    # Contract document storage
    contract_document_url = Column(String(500), nullable=True)
    contract_metadata = Column(JSON, nullable=True)
    
    # Relationships
    vendor = relationship("Vendor", back_populates="contracts")
    renewals = relationship("ContractRenewal", back_populates="contract", cascade="all, delete-orphan")
    analyses = relationship("ContractAnalysis", back_populates="contract", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index("idx_contract_property_status", "greystar_property_id", "status"),
        Index("idx_contract_end_date", "end_date"),
        Index("idx_contract_vendor_status", "vendor_id", "status"),
    )
    
    @property
    def monthly_volume(self) -> float:
        """Calculate monthly volume in cubic yards using industry formula"""
        return float(self.container_size * self.container_quantity * self.pickup_frequency * 4.33)
    
    @property
    def cost_per_yard(self) -> float:
        """Calculate cost per cubic yard"""
        volume = self.monthly_volume
        return float(self.monthly_cost / volume) if volume > 0 else 0.0
    
    @property
    def days_until_expiration(self) -> int:
        """Calculate days until contract expires"""
        today = date.today()
        return (self.end_date - today).days if self.end_date > today else 0
    
    @property
    def requires_renewal_analysis(self) -> bool:
        """Check if contract requires renewal analysis"""
        days_left = self.days_until_expiration
        return (
            self.status == ContractStatus.ACTIVE and
            days_left <= self.renewal_notice_days and
            days_left > 0
        )


class ContractRenewal(Base, TimestampMixin):
    """
    Contract renewal tracking model.
    Manages the renewal process for expiring contracts.
    """
    __tablename__ = "contract_renewals"

    id = Column(Integer, primary_key=True, index=True)
    contract_id = Column(Integer, ForeignKey("contracts.id"), nullable=False)
    
    # Renewal timeline
    renewal_date = Column(Date, nullable=False)
    notice_sent_date = Column(Date, nullable=True)
    response_due_date = Column(Date, nullable=True)
    
    # Renewal status
    status = Column(Enum(RenewalStatus), default=RenewalStatus.PENDING, nullable=False)
    decision = Column(String(20), nullable=True)  # renew, terminate, renegotiate
    decision_date = Column(Date, nullable=True)
    decision_by = Column(String(100), nullable=True)
    
    # Proposed terms (if different from current)
    proposed_monthly_cost = Column(Numeric(10, 2), nullable=True)
    proposed_contract_length = Column(Integer, nullable=True)  # Months
    proposed_changes = Column(JSON, nullable=True)
    
    # Analysis results
    recommendation = Column(String(20), nullable=True)  # renew, terminate, renegotiate
    recommendation_reason = Column(Text, nullable=True)
    potential_savings = Column(Numeric(10, 2), nullable=True)
    
    # Escalation tracking
    escalation_level = Column(Integer, default=0)
    escalation_date = Column(Date, nullable=True)
    assigned_to = Column(String(100), nullable=True)
    
    # Relationships
    contract = relationship("Contract", back_populates="renewals")
    
    # Indexes
    __table_args__ = (
        Index("idx_renewal_status_date", "status", "renewal_date"),
        Index("idx_renewal_contract", "contract_id"),
    )


class ContractAnalysis(Base, TimestampMixin):
    """
    Contract analysis results model.
    Stores comprehensive analysis data for contract optimization.
    """
    __tablename__ = "contract_analyses"

    id = Column(Integer, primary_key=True, index=True)
    contract_id = Column(Integer, ForeignKey("contracts.id"), nullable=False)
    renewal_id = Column(Integer, ForeignKey("contract_renewals.id"), nullable=True)
    
    # Analysis metadata
    analysis_type = Column(String(50), nullable=False)  # renewal, optimization, benchmark
    analysis_date = Column(DateTime, default=func.now(), nullable=False)
    analyzed_by = Column(String(100), nullable=True)
    
    # Performance metrics
    current_cost_per_door = Column(Numeric(8, 2), nullable=True)
    benchmark_cost_per_door = Column(Numeric(8, 2), nullable=True)
    cost_variance_percent = Column(Numeric(5, 2), nullable=True)
    
    current_cost_per_yard = Column(Numeric(8, 2), nullable=True)
    benchmark_cost_per_yard = Column(Numeric(8, 2), nullable=True)
    volume_efficiency = Column(Numeric(5, 2), nullable=True)
    
    # Vendor performance
    service_quality_score = Column(Numeric(3, 1), nullable=True)  # 1-10 scale
    reliability_score = Column(Numeric(3, 1), nullable=True)
    communication_score = Column(Numeric(3, 1), nullable=True)
    overall_vendor_score = Column(Numeric(3, 1), nullable=True)
    
    # Recommendations
    recommendation = Column(String(50), nullable=False)
    confidence_level = Column(Numeric(3, 1), nullable=True)  # 1-10 scale
    potential_annual_savings = Column(Numeric(10, 2), nullable=True)
    
    # Analysis details
    analysis_data = Column(JSON, nullable=True)  # Detailed analysis results
    comparison_vendors = Column(JSON, nullable=True)  # Alternative vendor options
    risk_factors = Column(JSON, nullable=True)
    
    # Executive summary
    executive_summary = Column(Text, nullable=True)
    key_findings = Column(JSON, nullable=True)
    action_items = Column(JSON, nullable=True)
    
    # Relationships
    contract = relationship("Contract", back_populates="analyses")
    renewal = relationship("ContractRenewal")
    
    # Indexes
    __table_args__ = (
        Index("idx_analysis_contract_date", "contract_id", "analysis_date"),
        Index("idx_analysis_type", "analysis_type"),
    )