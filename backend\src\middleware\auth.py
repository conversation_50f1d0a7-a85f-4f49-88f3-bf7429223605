"""
Authentication Middleware
========================

FastAPI middleware for authentication, rate limiting, and request validation
for the Advantage Waste Enterprise API.
"""

import time
from typing import Dict, Optional
from collections import defaultdict, deque
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
import structlog

logger = structlog.get_logger()

class RateLimiter:
    """Token bucket rate limiter for API endpoints"""
    
    def __init__(self):
        self.buckets: Dict[str, deque] = defaultdict(deque)
        self.limits = {
            # Rate limits per endpoint pattern (requests per minute)
            "/api/renewals/dashboard": 60,  # Dashboard can be hit frequently
            "/api/renewals/upcoming": 120,  # List endpoints
            "/api/renewals/*/analyze": 30,  # Analysis is resource intensive
            "/api/renewals/reports": 20,    # Report generation is expensive
            "default": 300,  # Default rate limit
        }
    
    def is_allowed(self, client_ip: str, endpoint: str) -> bool:
        """Check if request is allowed based on rate limits"""
        now = time.time()
        
        # Determine rate limit for this endpoint
        limit = self.limits.get("default", 300)
        for pattern, pattern_limit in self.limits.items():
            if pattern in endpoint:
                limit = pattern_limit
                break
        
        # Clean old requests (older than 1 minute)
        bucket_key = f"{client_ip}:{endpoint}"
        bucket = self.buckets[bucket_key]
        
        while bucket and bucket[0] < now - 60:
            bucket.popleft()
        
        # Check if under limit
        if len(bucket) >= limit:
            return False
        
        # Add current request
        bucket.append(now)
        return True
    
    def get_remaining(self, client_ip: str, endpoint: str) -> int:
        """Get remaining requests for this client/endpoint"""
        limit = self.limits.get("default", 300)
        for pattern, pattern_limit in self.limits.items():
            if pattern in endpoint:
                limit = pattern_limit
                break
        
        bucket_key = f"{client_ip}:{endpoint}"
        current_count = len(self.buckets[bucket_key])
        return max(0, limit - current_count)

# Global rate limiter instance
rate_limiter = RateLimiter()

async def rate_limit_middleware(request: Request, call_next):
    """Rate limiting middleware"""
    client_ip = request.client.host if request.client else "unknown"
    endpoint = request.url.path
    
    # Skip rate limiting for health checks and system endpoints
    if endpoint in ["/health", "/", "/docs", "/redoc", "/openapi.json"]:
        return await call_next(request)
    
    # Check rate limit
    if not rate_limiter.is_allowed(client_ip, endpoint):
        logger.warning(
            "Rate limit exceeded",
            client_ip=client_ip,
            endpoint=endpoint
        )
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "error": "Rate limit exceeded",
                "message": "Too many requests. Please try again later.",
                "retry_after": 60
            },
            headers={"Retry-After": "60"}
        )
    
    # Add rate limit headers to response
    response = await call_next(request)
    remaining = rate_limiter.get_remaining(client_ip, endpoint)
    response.headers["X-RateLimit-Remaining"] = str(remaining)
    response.headers["X-RateLimit-Reset"] = str(int(time.time() + 60))
    
    return response

async def security_headers_middleware(request: Request, call_next):
    """Add security headers to all responses"""
    response = await call_next(request)
    
    # Security headers for enterprise compliance
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Content-Security-Policy"] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline'; "
        "style-src 'self' 'unsafe-inline'; "
        "img-src 'self' data: https:; "
        "connect-src 'self'"
    )
    
    return response

async def audit_logging_middleware(request: Request, call_next):
    """Enhanced audit logging for compliance"""
    start_time = time.time()
    
    # Extract user info from token if present
    user_id = None
    user_role = None
    authorization = request.headers.get("authorization")
    
    if authorization and authorization.startswith("Bearer "):
        try:
            from ..core.security import verify_token
            token = authorization.split(" ")[1]
            payload = verify_token(token)
            user_id = payload.get("sub")
            user_role = payload.get("role")
        except Exception:
            pass  # Invalid token, will be handled by auth endpoints
    
    # Log request start
    logger.info(
        "API request started",
        method=request.method,
        path=request.url.path,
        query_params=dict(request.query_params),
        client_ip=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
        user_id=user_id,
        user_role=user_role,
    )
    
    # Process request
    response = await call_next(request)
    
    # Log request completion
    process_time = time.time() - start_time
    logger.info(
        "API request completed",
        method=request.method,
        path=request.url.path,
        status_code=response.status_code,
        process_time=process_time,
        user_id=user_id,
        user_role=user_role,
    )
    
    # Log potential security events
    if response.status_code == 401:
        logger.warning(
            "Authentication failed",
            path=request.url.path,
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
        )
    elif response.status_code == 403:
        logger.warning(
            "Authorization failed",
            path=request.url.path,
            client_ip=request.client.host if request.client else None,
            user_id=user_id,
            user_role=user_role,
        )
    
    return response

class RequestValidator:
    """Validate common request patterns for security"""
    
    SUSPICIOUS_PATTERNS = [
        # SQL injection patterns
        "union select",
        "drop table",
        "delete from",
        "update set",
        "insert into",
        # XSS patterns
        "<script",
        "javascript:",
        "onload=",
        "onerror=",
        # Path traversal
        "../",
        "..\\",
        # Command injection
        ";cat ",
        "|whoami",
        "&& rm",
    ]
    
    @staticmethod
    def is_suspicious(value: str) -> bool:
        """Check if a string contains suspicious patterns"""
        value_lower = value.lower()
        return any(pattern in value_lower for pattern in RequestValidator.SUSPICIOUS_PATTERNS)
    
    @staticmethod
    def validate_query_params(request: Request) -> bool:
        """Validate query parameters for suspicious content"""
        for param, value in request.query_params.items():
            if RequestValidator.is_suspicious(value):
                logger.warning(
                    "Suspicious query parameter detected",
                    param=param,
                    value=value[:100],  # Truncate for logging
                    client_ip=request.client.host if request.client else None,
                )
                return False
        return True

async def request_validation_middleware(request: Request, call_next):
    """Validate requests for security threats"""
    
    # Validate query parameters
    if not RequestValidator.validate_query_params(request):
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "error": "Invalid request",
                "message": "Request contains suspicious content",
            }
        )
    
    # Check request size (prevent large payload attacks)
    content_length = request.headers.get("content-length")
    if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB limit
        return JSONResponse(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            content={
                "error": "Request too large",
                "message": "Request payload exceeds maximum size limit",
            }
        )
    
    return await call_next(request)

async def cors_validation_middleware(request: Request, call_next):
    """Additional CORS validation for enterprise security"""
    origin = request.headers.get("origin")
    
    # Allow requests without origin (same-origin or direct API calls)
    if not origin:
        return await call_next(request)
    
    # Define allowed origins (should match CORS middleware config)
    allowed_origins = [
        "http://localhost:3000",
        "http://localhost:5173", 
        "https://advantage-waste.greystar.com",
    ]
    
    # Check if origin is allowed
    if origin not in allowed_origins:
        logger.warning(
            "CORS violation detected",
            origin=origin,
            client_ip=request.client.host if request.client else None,
        )
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content={
                "error": "CORS violation",
                "message": "Origin not allowed",
            }
        )
    
    return await call_next(request)