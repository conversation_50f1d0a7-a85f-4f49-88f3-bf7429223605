#!/usr/bin/env python3
"""
Comprehensive Test Runner for Advantage Waste Enterprise
=======================================================

Automated test execution with comprehensive reporting, performance analysis,
and enterprise validation for the complete renewal workflow system.

Built by Testing Automation Agent - Enterprise Development Force
"""

import subprocess
import sys
import time
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple


class AdvantageWasteTestRunner:
    """Enterprise test runner with comprehensive reporting and validation"""
    
    def __init__(self):
        self.start_time = datetime.utcnow()
        self.test_results = {}
        self.performance_metrics = {}
        
    def run_test_suite(self, test_type: str = "all") -> Dict:
        """Run comprehensive test suite with enterprise validation"""
        
        print("=" * 80)
        print("Advantage Waste Enterprise - Automated Test Suite")
        print("=" * 80)
        print(f"Started: {self.start_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"Test Type: {test_type}")
        print()
        
        # Test execution plan
        test_phases = {
            "unit": {
                "description": "Unit Tests - Core Business Logic Validation",
                "command": ["pytest", "tests/test_renewal_logic.py", "-m", "unit", "-v"],
                "critical": True
            },
            "integration": {
                "description": "Integration Tests - End-to-End Workflow Validation", 
                "command": ["pytest", "tests/test_integration_workflows.py", "-v"],
                "critical": True
            },
            "performance": {
                "description": "Performance Tests - Enterprise Scale Validation",
                "command": ["pytest", "tests/test_performance_scale.py", "-m", "performance", "-v"],
                "critical": False
            },
            "financial": {
                "description": "Financial Tests - Calculation Accuracy Validation",
                "command": ["pytest", "tests/test_renewal_logic.py", "-k", "financial", "-v"],
                "critical": True
            },
            "enterprise": {
                "description": "Enterprise Tests - 3,850+ Property Portfolio Validation",
                "command": ["pytest", "tests/", "-m", "enterprise", "-v"],
                "critical": True
            }
        }
        
        # Execute test phases
        if test_type == "all":
            phases_to_run = test_phases.keys()
        elif test_type in test_phases:
            phases_to_run = [test_type]
        else:
            print(f"Error: Unknown test type '{test_type}'")
            print(f"Available types: {', '.join(test_phases.keys())}, all")
            return {"status": "error", "message": f"Unknown test type: {test_type}"}
        
        overall_success = True
        
        for phase_name in phases_to_run:
            phase_config = test_phases[phase_name]
            print(f"Running {phase_config['description']}")
            print("-" * 60)
            
            phase_result = self._execute_test_phase(phase_name, phase_config)
            self.test_results[phase_name] = phase_result
            
            if phase_config["critical"] and not phase_result["success"]:
                overall_success = False
                print(f"❌ CRITICAL FAILURE in {phase_name}")
            elif phase_result["success"]:
                print(f"✅ {phase_name} completed successfully")
            else:
                print(f"⚠️  {phase_name} completed with issues")
            
            print()
        
        # Generate comprehensive report
        final_report = self._generate_final_report(overall_success)
        
        return final_report
    
    def _execute_test_phase(self, phase_name: str, phase_config: Dict) -> Dict:
        """Execute individual test phase with detailed tracking"""
        
        phase_start = time.time()
        
        try:
            # Execute pytest command
            result = subprocess.run(
                phase_config["command"],
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout
            )
            
            phase_end = time.time()
            execution_time = phase_end - phase_start
            
            # Parse test results
            success = result.returncode == 0
            output_lines = result.stdout.split('\n')
            error_lines = result.stderr.split('\n')
            
            # Extract test metrics
            test_metrics = self._parse_test_output(output_lines)
            
            return {
                "success": success,
                "execution_time": execution_time,
                "return_code": result.returncode,
                "tests_run": test_metrics.get("tests_run", 0),
                "tests_passed": test_metrics.get("tests_passed", 0),
                "tests_failed": test_metrics.get("tests_failed", 0),
                "tests_skipped": test_metrics.get("tests_skipped", 0),
                "coverage": test_metrics.get("coverage", 0),
                "output": result.stdout,
                "errors": result.stderr
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "execution_time": 600,
                "return_code": -1,
                "error": "Test phase timed out after 10 minutes",
                "tests_run": 0,
                "tests_passed": 0,
                "tests_failed": 0,
                "tests_skipped": 0
            }
        except Exception as e:
            return {
                "success": False,
                "execution_time": time.time() - phase_start,
                "return_code": -1,
                "error": str(e),
                "tests_run": 0,
                "tests_passed": 0,
                "tests_failed": 0,
                "tests_skipped": 0
            }
    
    def _parse_test_output(self, output_lines: List[str]) -> Dict:
        """Parse pytest output to extract test metrics"""
        
        metrics = {
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "tests_skipped": 0,
            "coverage": 0
        }
        
        for line in output_lines:
            # Parse pytest summary line
            if "passed" in line and "failed" in line:
                # Example: "5 failed, 10 passed, 2 skipped in 30.2s"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "passed" and i > 0:
                        metrics["tests_passed"] = int(parts[i-1])
                    elif part == "failed" and i > 0:
                        metrics["tests_failed"] = int(parts[i-1])
                    elif part == "skipped" and i > 0:
                        metrics["tests_skipped"] = int(parts[i-1])
            
            # Parse coverage information
            elif "Total coverage:" in line:
                # Example: "Total coverage: 95%"
                coverage_str = line.split(":")[-1].strip().rstrip("%")
                try:
                    metrics["coverage"] = float(coverage_str)
                except ValueError:
                    pass
        
        metrics["tests_run"] = metrics["tests_passed"] + metrics["tests_failed"] + metrics["tests_skipped"]
        
        return metrics
    
    def _generate_final_report(self, overall_success: bool) -> Dict:
        """Generate comprehensive final test report"""
        
        end_time = datetime.utcnow()
        total_execution_time = (end_time - self.start_time).total_seconds()
        
        # Aggregate metrics
        total_tests = sum(result.get("tests_run", 0) for result in self.test_results.values())
        total_passed = sum(result.get("tests_passed", 0) for result in self.test_results.values())
        total_failed = sum(result.get("tests_failed", 0) for result in self.test_results.values())
        total_skipped = sum(result.get("tests_skipped", 0) for result in self.test_results.values())
        
        # Calculate success rate
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # Performance analysis
        performance_summary = self._analyze_performance_metrics()
        
        # Enterprise validation summary
        enterprise_validation = self._validate_enterprise_requirements()
        
        # Generate report
        report = {
            "timestamp": end_time.isoformat(),
            "overall_success": overall_success,
            "execution_summary": {
                "total_execution_time": total_execution_time,
                "total_tests": total_tests,
                "tests_passed": total_passed,
                "tests_failed": total_failed,
                "tests_skipped": total_skipped,
                "success_rate": success_rate
            },
            "phase_results": self.test_results,
            "performance_summary": performance_summary,
            "enterprise_validation": enterprise_validation,
            "recommendations": self._generate_recommendations()
        }
        
        # Print final report
        self._print_final_report(report)
        
        # Save detailed report
        self._save_report_to_file(report)
        
        return report
    
    def _analyze_performance_metrics(self) -> Dict:
        """Analyze performance test results for enterprise requirements"""
        
        performance_result = self.test_results.get("performance", {})
        
        if not performance_result.get("success"):
            return {
                "status": "not_measured",
                "message": "Performance tests did not complete successfully"
            }
        
        # Extract performance metrics from test output
        output = performance_result.get("output", "")
        
        metrics = {
            "portfolio_scan_performance": "not_measured",
            "analysis_performance": "not_measured", 
            "notification_performance": "not_measured",
            "concurrent_performance": "not_measured",
            "memory_efficiency": "not_measured"
        }
        
        # Parse performance output (simplified - real implementation would be more sophisticated)
        if "Portfolio scan performance:" in output:
            metrics["portfolio_scan_performance"] = "measured"
        if "Contract analysis performance:" in output:
            metrics["analysis_performance"] = "measured"
        if "Notification performance:" in output:
            metrics["notification_performance"] = "measured"
        
        return {
            "status": "measured",
            "metrics": metrics,
            "enterprise_ready": all(v == "measured" for v in metrics.values())
        }
    
    def _validate_enterprise_requirements(self) -> Dict:
        """Validate that all enterprise requirements are met"""
        
        validations = {
            "financial_accuracy": {
                "requirement": "All financial calculations must be accurate to 2 decimal places",
                "status": "pass" if self.test_results.get("financial", {}).get("success") else "fail"
            },
            "scale_performance": {
                "requirement": "System must handle 3,850+ properties efficiently",
                "status": "pass" if self.test_results.get("performance", {}).get("success") else "fail"
            },
            "workflow_integration": {
                "requirement": "End-to-end workflows must complete successfully",
                "status": "pass" if self.test_results.get("integration", {}).get("success") else "fail"
            },
            "business_logic": {
                "requirement": "Core renewal logic must pass all validations",
                "status": "pass" if self.test_results.get("unit", {}).get("success") else "fail"
            },
            "data_integrity": {
                "requirement": "All data operations must maintain integrity",
                "status": "pass" if all(r.get("success") for r in self.test_results.values()) else "fail"
            }
        }
        
        overall_compliance = all(v["status"] == "pass" for v in validations.values())
        
        return {
            "enterprise_compliant": overall_compliance,
            "validations": validations,
            "compliance_percentage": sum(1 for v in validations.values() if v["status"] == "pass") / len(validations) * 100
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        
        recommendations = []
        
        # Check test coverage
        total_tests = sum(result.get("tests_run", 0) for result in self.test_results.values())
        if total_tests < 50:
            recommendations.append("Consider adding more comprehensive test coverage")
        
        # Check performance results
        if not self.test_results.get("performance", {}).get("success"):
            recommendations.append("Performance tests need attention - ensure enterprise scalability")
        
        # Check critical test failures
        for phase_name, result in self.test_results.items():
            if not result.get("success") and result.get("tests_failed", 0) > 0:
                recommendations.append(f"Address test failures in {phase_name} phase")
        
        # Check execution time
        total_time = sum(result.get("execution_time", 0) for result in self.test_results.values())
        if total_time > 300:  # 5 minutes
            recommendations.append("Consider optimizing test execution time for CI/CD efficiency")
        
        if not recommendations:
            recommendations.append("All tests passed successfully - system is enterprise-ready!")
        
        return recommendations
    
    def _print_final_report(self, report: Dict):
        """Print formatted final report to console"""
        
        print("=" * 80)
        print("FINAL TEST REPORT - ADVANTAGE WASTE ENTERPRISE")
        print("=" * 80)
        
        # Executive Summary
        success_indicator = "✅ SUCCESS" if report["overall_success"] else "❌ FAILURE"
        print(f"Overall Status: {success_indicator}")
        print(f"Test Execution Time: {report['execution_summary']['total_execution_time']:.1f} seconds")
        print(f"Success Rate: {report['execution_summary']['success_rate']:.1f}%")
        print()
        
        # Test Summary
        summary = report["execution_summary"]
        print("Test Execution Summary:")
        print(f"  Total Tests: {summary['total_tests']}")
        print(f"  Passed: {summary['tests_passed']}")
        print(f"  Failed: {summary['tests_failed']}")
        print(f"  Skipped: {summary['tests_skipped']}")
        print()
        
        # Enterprise Validation
        enterprise = report["enterprise_validation"]
        compliance_indicator = "✅ COMPLIANT" if enterprise["enterprise_compliant"] else "❌ NON-COMPLIANT"
        print(f"Enterprise Compliance: {compliance_indicator}")
        print(f"Compliance Score: {enterprise['compliance_percentage']:.1f}%")
        print()
        
        # Recommendations
        print("Recommendations:")
        for recommendation in report["recommendations"]:
            print(f"  • {recommendation}")
        print()
        
        print("=" * 80)
    
    def _save_report_to_file(self, report: Dict):
        """Save detailed report to JSON file"""
        
        report_dir = Path("test_reports")
        report_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        report_file = report_dir / f"test_report_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"Detailed report saved to: {report_file}")


def main():
    """Main test runner entry point"""
    
    # Parse command line arguments
    test_type = "all"
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
    
    # Run test suite
    runner = AdvantageWasteTestRunner()
    report = runner.run_test_suite(test_type)
    
    # Exit with appropriate code
    exit_code = 0 if report["overall_success"] else 1
    sys.exit(exit_code)


if __name__ == "__main__":
    main()