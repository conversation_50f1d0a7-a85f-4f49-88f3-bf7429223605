@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }

  input:focus,
  textarea:focus,
  select:focus {
    @apply outline-none;
  }
}

@layer components {
  .btn-primary {
    @apply bg-greystar-blue text-white px-4 py-2 rounded-md hover:bg-greystar-dark-blue transition-colors duration-200 font-medium;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors duration-200 font-medium;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-greystar-blue focus:border-transparent;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .table-header {
    @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm;
  }
}