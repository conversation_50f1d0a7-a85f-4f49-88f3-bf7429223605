"""
Renewal Schemas
==============

Pydantic models for contract renewal operations, analysis, and reporting.
Follows Advantage Waste business logic and validation rules.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date
from decimal import Decimal
from enum import Enum
from pydantic import BaseModel, Field, validator, root_validator
import structlog

logger = structlog.get_logger()

class ContractStatus(str, Enum):
    """Contract status enumeration"""
    ACTIVE = "active"
    EXPIRING = "expiring"
    EXPIRED = "expired"
    RENEWED = "renewed"
    TERMINATED = "terminated"

class RenewalStatus(str, Enum):
    """Renewal process status"""
    PENDING_REVIEW = "pending_review"
    UNDER_ANALYSIS = "under_analysis"
    NEGOTIATING = "negotiating"
    AWAITING_APPROVAL = "awaiting_approval"
    APPROVED = "approved"
    RENEWED = "renewed"
    DECLINED = "declined"

class NotificationFrequency(str, Enum):
    """Notification frequency options"""
    IMMEDIATE = "immediate"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    DISABLED = "disabled"

class ReportType(str, Enum):
    """Report type enumeration"""
    EXECUTIVE_SUMMARY = "executive_summary"
    DETAILED_ANALYSIS = "detailed_analysis"
    COST_COMPARISON = "cost_comparison"
    BENCHMARK_ANALYSIS = "benchmark_analysis"
    SAVINGS_OPPORTUNITY = "savings_opportunity"

class PriorityLevel(str, Enum):
    """Priority levels for renewals"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

# Base schemas
class PropertyBase(BaseModel):
    """Base property information"""
    property_id: str = Field(..., description="Unique property identifier")
    property_name: str = Field(..., description="Property display name")
    units: int = Field(..., gt=0, description="Number of units in property")
    address: str = Field(..., description="Property address")
    region_id: Optional[str] = Field(None, description="Regional identifier")

class VendorBase(BaseModel):
    """Base vendor information"""
    vendor_id: str = Field(..., description="Unique vendor identifier")
    vendor_name: str = Field(..., description="Vendor company name")
    contact_email: Optional[str] = Field(None, description="Primary contact email")
    contact_phone: Optional[str] = Field(None, description="Primary contact phone")

class ContractBase(BaseModel):
    """Base contract information"""
    contract_id: str = Field(..., description="Unique contract identifier")
    property_id: str = Field(..., description="Associated property ID")
    vendor_id: str = Field(..., description="Associated vendor ID")
    contract_type: str = Field(..., description="Type of waste service")
    start_date: date = Field(..., description="Contract start date")
    end_date: date = Field(..., description="Contract end date")
    monthly_cost: Decimal = Field(..., ge=0, description="Monthly service cost")
    status: ContractStatus = Field(..., description="Current contract status")

# Contract and renewal schemas
class ContractDetails(ContractBase):
    """Detailed contract information"""
    container_size: float = Field(..., gt=0, description="Container size in cubic yards")
    pickup_frequency: int = Field(..., ge=1, le=7, description="Pickups per week")
    fuel_surcharge: Optional[Decimal] = Field(None, ge=0, description="Monthly fuel surcharge")
    environmental_fee: Optional[Decimal] = Field(None, ge=0, description="Environmental fee")
    admin_fee: Optional[Decimal] = Field(None, ge=0, description="Administrative fee")
    early_termination_fee: Optional[Decimal] = Field(None, ge=0, description="Early termination penalty")
    price_increase_cap: Optional[Decimal] = Field(None, ge=0, description="Annual price increase cap percentage")
    
    @validator('monthly_cost')
    def validate_monthly_cost(cls, v, values):
        """Validate monthly cost is within reasonable range"""
        if 'container_size' in values and 'pickup_frequency' in values:
            # Industry benchmark: $10-30 per door monthly
            # Rough validation based on service level
            min_cost = values['container_size'] * values['pickup_frequency'] * 50
            max_cost = values['container_size'] * values['pickup_frequency'] * 300
            
            if not (min_cost <= v <= max_cost):
                logger.warning(
                    "Monthly cost outside typical range",
                    cost=float(v),
                    min_expected=min_cost,
                    max_expected=max_cost
                )
        return v

class RenewalRequest(BaseModel):
    """Contract renewal request"""
    contract_id: str = Field(..., description="Contract to renew")
    requested_by: str = Field(..., description="User ID of requester")
    priority: PriorityLevel = Field(PriorityLevel.MEDIUM, description="Renewal priority")
    target_renewal_date: Optional[date] = Field(None, description="Target renewal date")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")
    auto_approve_threshold: Optional[Decimal] = Field(
        None, 
        ge=0, 
        le=100,
        description="Auto-approve if savings percentage above this threshold"
    )

class RenewalAnalysisRequest(BaseModel):
    """Request for renewal analysis"""
    contract_id: str = Field(..., description="Contract to analyze")
    include_benchmarks: bool = Field(True, description="Include industry benchmarks")
    include_alternatives: bool = Field(True, description="Include alternative vendors")
    analysis_depth: str = Field("standard", regex="^(basic|standard|comprehensive)$", description="Analysis depth")

class NotificationPreferences(BaseModel):
    """User notification preferences"""
    user_id: str = Field(..., description="User identifier")
    renewal_reminders: NotificationFrequency = Field(NotificationFrequency.WEEKLY, description="Renewal reminder frequency")
    cost_alerts: NotificationFrequency = Field(NotificationFrequency.IMMEDIATE, description="Cost alert frequency")
    contract_expiry_days: int = Field(60, ge=30, le=365, description="Days before expiry to send notifications")
    email_enabled: bool = Field(True, description="Enable email notifications")
    dashboard_alerts: bool = Field(True, description="Show dashboard alerts")
    mobile_push: bool = Field(False, description="Enable mobile push notifications")

# Analysis and reporting schemas
class BenchmarkData(BaseModel):
    """Industry benchmark data"""
    metric_name: str = Field(..., description="Benchmark metric name")
    property_value: Decimal = Field(..., description="Property's current value")
    industry_average: Decimal = Field(..., description="Industry average value")
    industry_percentile: int = Field(..., ge=0, le=100, description="Property's percentile ranking")
    best_in_class: Decimal = Field(..., description="Best-in-class value")
    variance_percentage: Decimal = Field(..., description="Percentage variance from industry average")
    improvement_opportunity: Decimal = Field(..., description="Potential improvement amount")

class CostAnalysis(BaseModel):
    """Cost analysis results"""
    current_monthly_cost: Decimal = Field(..., description="Current monthly cost")
    current_cost_per_door: Decimal = Field(..., description="Current cost per door")
    current_cost_per_yard: Decimal = Field(..., description="Current cost per cubic yard")
    projected_monthly_cost: Decimal = Field(..., description="Projected renewal cost")
    savings_opportunity: Decimal = Field(..., description="Potential monthly savings")
    savings_percentage: Decimal = Field(..., description="Percentage savings")
    roi_months: Optional[int] = Field(None, description="ROI payback period in months")

class AlternativeVendor(BaseModel):
    """Alternative vendor proposal"""
    vendor_id: str = Field(..., description="Alternative vendor ID")
    vendor_name: str = Field(..., description="Vendor name")
    proposed_monthly_cost: Decimal = Field(..., description="Proposed monthly cost")
    container_size: float = Field(..., description="Proposed container size")
    pickup_frequency: int = Field(..., description="Proposed pickup frequency")
    estimated_savings: Decimal = Field(..., description="Estimated monthly savings")
    service_quality_score: Optional[int] = Field(None, ge=1, le=10, description="Service quality rating")
    contract_terms_score: Optional[int] = Field(None, ge=1, le=10, description="Contract terms rating")
    transition_risk: str = Field(..., regex="^(low|medium|high)$", description="Risk level of switching")

class RenewalAnalysis(BaseModel):
    """Complete renewal analysis"""
    analysis_id: str = Field(..., description="Unique analysis identifier")
    contract_id: str = Field(..., description="Contract being analyzed")
    analysis_date: datetime = Field(..., description="Analysis timestamp")
    analyzed_by: str = Field(..., description="User who performed analysis")
    
    # Current contract details
    current_contract: ContractDetails = Field(..., description="Current contract details")
    property: PropertyBase = Field(..., description="Property information")
    vendor: VendorBase = Field(..., description="Current vendor information")
    
    # Analysis results
    cost_analysis: CostAnalysis = Field(..., description="Cost analysis results")
    benchmarks: List[BenchmarkData] = Field(..., description="Industry benchmarks")
    alternative_vendors: List[AlternativeVendor] = Field(default_factory=list, description="Alternative vendor options")
    
    # Recommendations
    renewal_recommendation: str = Field(..., regex="^(renew|renegotiate|switch|terminate)$", description="Primary recommendation")
    confidence_score: int = Field(..., ge=1, le=100, description="Confidence in recommendation")
    risk_factors: List[str] = Field(default_factory=list, description="Identified risk factors")
    next_steps: List[str] = Field(..., description="Recommended next steps")
    
    # Validation
    @validator('alternative_vendors')
    def validate_alternatives(cls, v, values):
        """Ensure alternative vendors offer genuine alternatives"""
        if len(v) > 5:
            raise ValueError("Too many alternative vendors (max 5)")
        return v

class RenewalItem(BaseModel):
    """Individual renewal item for lists and dashboards"""
    contract_id: str = Field(..., description="Contract identifier")
    property_name: str = Field(..., description="Property name")
    vendor_name: str = Field(..., description="Current vendor")
    expiry_date: date = Field(..., description="Contract expiry date")
    days_to_expiry: int = Field(..., description="Days until expiry")
    monthly_cost: Decimal = Field(..., description="Current monthly cost")
    priority: PriorityLevel = Field(..., description="Renewal priority")
    status: RenewalStatus = Field(..., description="Renewal status")
    potential_savings: Optional[Decimal] = Field(None, description="Estimated potential savings")
    last_analyzed: Optional[datetime] = Field(None, description="Last analysis date")

class DashboardMetrics(BaseModel):
    """Dashboard summary metrics"""
    total_contracts_expiring: int = Field(..., description="Total contracts expiring soon")
    total_monthly_value_at_risk: Decimal = Field(..., description="Total monthly value of expiring contracts")
    potential_annual_savings: Decimal = Field(..., description="Potential annual savings from renewals")
    average_cost_per_door: Decimal = Field(..., description="Portfolio average cost per door")
    contracts_under_review: int = Field(..., description="Contracts currently under review")
    high_priority_renewals: int = Field(..., description="High priority renewals")
    completion_rate: Decimal = Field(..., ge=0, le=100, description="Renewal process completion rate")
    
class NotificationHistory(BaseModel):
    """Notification history record"""
    notification_id: str = Field(..., description="Unique notification identifier")
    contract_id: str = Field(..., description="Related contract")
    recipient: str = Field(..., description="Notification recipient")
    notification_type: str = Field(..., description="Type of notification")
    sent_date: datetime = Field(..., description="When notification was sent")
    delivery_status: str = Field(..., regex="^(sent|delivered|failed|read)$", description="Delivery status")
    content_summary: str = Field(..., description="Brief content summary")

# Request/Response schemas for API endpoints
class UpcomingRenewalsRequest(BaseModel):
    """Request parameters for upcoming renewals"""
    days_ahead: int = Field(90, ge=30, le=365, description="Days ahead to look for expiring contracts")
    priority_filter: Optional[PriorityLevel] = Field(None, description="Filter by priority level")
    property_ids: Optional[List[str]] = Field(None, description="Filter by specific properties")
    status_filter: Optional[RenewalStatus] = Field(None, description="Filter by renewal status")
    limit: int = Field(50, ge=1, le=500, description="Maximum number of results")
    offset: int = Field(0, ge=0, description="Pagination offset")

class UpcomingRenewalsResponse(BaseModel):
    """Response for upcoming renewals"""
    renewals: List[RenewalItem] = Field(..., description="List of upcoming renewals")
    total_count: int = Field(..., description="Total number of matching renewals")
    metrics: DashboardMetrics = Field(..., description="Summary metrics")

class RenewalReportRequest(BaseModel):
    """Request for renewal reports"""
    report_type: ReportType = Field(..., description="Type of report to generate")
    date_range_start: Optional[date] = Field(None, description="Report start date")
    date_range_end: Optional[date] = Field(None, description="Report end date")
    property_ids: Optional[List[str]] = Field(None, description="Filter by properties")
    include_projections: bool = Field(True, description="Include future projections")
    format: str = Field("json", regex="^(json|pdf|excel)$", description="Report format")

class APIResponse(BaseModel):
    """Standard API response wrapper"""
    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Response message")
    data: Optional[Any] = Field(None, description="Response data")
    errors: Optional[List[str]] = Field(None, description="Error messages if any")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")