version: '3.8'

services:
  # PostgreSQL database for development
  postgres:
    image: postgres:15-alpine
    container_name: advantage-waste-db
    environment:
      POSTGRES_DB: advantage_waste
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - advantage-waste-network

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: advantage-waste-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - advantage-waste-network

  # Backend API (FastAPI)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: advantage-waste-api
    environment:
      - DATABASE_URL=********************************************/advantage_waste
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/venv  # Anonymous volume for venv
    depends_on:
      - postgres
      - redis
    networks:
      - advantage-waste-network
    develop:
      watch:
        - action: sync
          path: ./backend/src
          target: /app/src
        - action: rebuild
          path: ./backend/requirements.txt

  # Frontend (React/Vite)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: advantage-waste-ui
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_APP_TITLE=Advantage Waste Enterprise
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules  # Anonymous volume for node_modules
    depends_on:
      - backend
    networks:
      - advantage-waste-network
    develop:
      watch:
        - action: sync
          path: ./frontend/src
          target: /app/src
        - action: rebuild
          path: ./frontend/package.json

  # Development tools container
  dev-tools:
    image: node:18-alpine
    container_name: advantage-waste-tools
    working_dir: /workspace
    volumes:
      - .:/workspace
    networks:
      - advantage-waste-network
    profiles:
      - tools
    command: tail -f /dev/null  # Keep container running

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  advantage-waste-network:
    driver: bridge

# Development profile for additional services
# Usage: docker-compose --profile dev up
profiles:
  dev:
    services:
      # PgAdmin for database management
      pgadmin:
        image: dpage/pgadmin4:latest
        container_name: advantage-waste-pgadmin
        environment:
          PGADMIN_DEFAULT_EMAIL: <EMAIL>
          PGADMIN_DEFAULT_PASSWORD: admin
          PGADMIN_CONFIG_SERVER_MODE: 'False'
        ports:
          - "5050:80"
        depends_on:
          - postgres
        networks:
          - advantage-waste-network

      # Redis Commander for cache management
      redis-commander:
        image: rediscommander/redis-commander:latest
        container_name: advantage-waste-redis-ui
        environment:
          - REDIS_HOSTS=local:redis:6379
        ports:
          - "8081:8081"
        depends_on:
          - redis
        networks:
          - advantage-waste-network
