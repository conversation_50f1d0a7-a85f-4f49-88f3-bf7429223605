# Advantage Waste Enterprise API Documentation

## Overview

The Advantage Waste Enterprise API provides comprehensive contract renewal management, analysis, and reporting capabilities for Greystar's multifamily property portfolio. Built with FastAPI, it offers enterprise-grade security, performance, and scalability.

## Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://api.advantage-waste.greystar.com`

## Authentication

All API endpoints require Bearer token authentication:

```http
Authorization: Bearer <your-jwt-token>
```

### User Roles & Permissions

| Role | Permissions | Access Level |
|------|-------------|--------------|
| Property Manager | View/analyze renewals for assigned properties | Property-level |
| Regional Director | Approve renewals, view regional reports | Regional-level |
| Executive | View all reports, high-level analytics | Portfolio-level |
| Admin | Full system access, user management | System-level |

## Rate Limits

| Endpoint Category | Limit | Window |
|-------------------|-------|--------|
| Dashboard | 60 requests | 1 minute |
| Analysis | 30 requests | 1 minute |
| Reports | 20 requests | 1 minute |
| General | 300 requests | 1 minute |

## API Endpoints

### Contract Renewals

#### GET /api/renewals/upcoming

Retrieve contracts approaching expiration with filtering and pagination.

**Query Parameters:**
- `days_ahead` (int, optional): Days ahead to look for expiring contracts (30-365, default: 90)
- `priority_filter` (enum, optional): Filter by priority level (`low`, `medium`, `high`, `critical`)
- `property_ids` (array, optional): Filter by specific property IDs
- `status_filter` (enum, optional): Filter by renewal status
- `limit` (int, optional): Maximum results per page (1-500, default: 50)
- `offset` (int, optional): Pagination offset (default: 0)

**Example Request:**
```bash
curl -X GET "http://localhost:8000/api/renewals/upcoming?days_ahead=120&priority_filter=high&limit=25" \
  -H "Authorization: Bearer <token>"
```

**Response:**
```json
{
  "renewals": [
    {
      "contract_id": "CNT-001",
      "property_name": "Greystar Gardens",
      "vendor_name": "Waste Pro",
      "expiry_date": "2025-09-15",
      "days_to_expiry": 64,
      "monthly_cost": "4800.00",
      "priority": "high",
      "status": "pending_review",
      "potential_savings": "720.00",
      "last_analyzed": "2025-06-15T10:30:00Z"
    }
  ],
  "total_count": 15,
  "metrics": {
    "total_contracts_expiring": 42,
    "total_monthly_value_at_risk": "168750.00",
    "potential_annual_savings": "507000.00",
    "average_cost_per_door": "18.25",
    "contracts_under_review": 15,
    "high_priority_renewals": 8,
    "completion_rate": "76.3"
  }
}
```

#### POST /api/renewals/{contract_id}/analyze

Generate comprehensive renewal analysis for a specific contract.

**Path Parameters:**
- `contract_id` (string): Contract identifier (format: CNT-XXXXXX)

**Request Body:**
```json
{
  "contract_id": "CNT-001",
  "include_benchmarks": true,
  "include_alternatives": true,
  "analysis_depth": "standard"
}
```

**Response:**
```json
{
  "analysis_id": "ANA-CNT-001-1689567890",
  "contract_id": "CNT-001",
  "analysis_date": "2025-07-13T10:30:00Z",
  "analyzed_by": "user-pm-001",
  "current_contract": {
    "contract_id": "CNT-001",
    "property_id": "PROP-001",
    "vendor_id": "VND-001",
    "monthly_cost": "4800.00",
    "container_size": 8.0,
    "pickup_frequency": 3
  },
  "cost_analysis": {
    "current_monthly_cost": "4800.00",
    "current_cost_per_door": "19.05",
    "current_cost_per_yard": "50.00",
    "projected_monthly_cost": "4992.00",
    "savings_opportunity": "720.00",
    "savings_percentage": "15.0",
    "roi_months": 8
  },
  "benchmarks": [
    {
      "metric_name": "Cost per Door",
      "property_value": "19.05",
      "industry_average": "16.50",
      "industry_percentile": 75,
      "best_in_class": "12.00",
      "variance_percentage": "15.45",
      "improvement_opportunity": "640.80"
    }
  ],
  "alternative_vendors": [
    {
      "vendor_id": "VND-ALT-001",
      "vendor_name": "EcoWaste Solutions",
      "proposed_monthly_cost": "4080.00",
      "estimated_savings": "720.00",
      "service_quality_score": 8,
      "transition_risk": "low"
    }
  ],
  "renewal_recommendation": "renegotiate",
  "confidence_score": 85,
  "risk_factors": [
    "Current vendor has price increase history",
    "Limited alternative options in market"
  ],
  "next_steps": [
    "Request competitive bids from top 3 alternatives",
    "Negotiate price freeze with current vendor",
    "Review contract terms for improvement opportunities"
  ]
}
```

#### GET /api/renewals/{contract_id}/notification-history

Retrieve communication timeline for a specific contract.

**Path Parameters:**
- `contract_id` (string): Contract identifier

**Query Parameters:**
- `limit` (int, optional): Maximum notifications to return (1-200, default: 50)

**Response:**
```json
[
  {
    "notification_id": "NOT-CNT-001-001",
    "contract_id": "CNT-001",
    "recipient": "<EMAIL>",
    "notification_type": "renewal_reminder",
    "sent_date": "2025-07-06T10:30:00Z",
    "delivery_status": "read",
    "content_summary": "Contract expiring in 60 days - analysis required"
  }
]
```

#### GET /api/renewals/dashboard

Get aggregated metrics for the renewal dashboard (cached for 5 minutes).

**Query Parameters:**
- `region_filter` (string, optional): Filter by region ID

**Response:**
```json
{
  "total_contracts_expiring": 42,
  "total_monthly_value_at_risk": "168750.00",
  "potential_annual_savings": "507000.00",
  "average_cost_per_door": "18.25",
  "contracts_under_review": 15,
  "high_priority_renewals": 8,
  "completion_rate": "76.3"
}
```

#### GET /api/renewals/reports

Generate executive summary and detailed renewal reports.

**Query Parameters:**
- `report_type` (enum, required): Type of report (`executive_summary`, `detailed_analysis`, `cost_comparison`, `benchmark_analysis`, `savings_opportunity`)
- `date_range_start` (date, optional): Report start date (YYYY-MM-DD)
- `date_range_end` (date, optional): Report end date (YYYY-MM-DD)
- `property_ids` (array, optional): Filter by specific properties
- `include_projections` (boolean, optional): Include future projections (default: true)
- `format` (enum, optional): Report format (`json`, `pdf`, `excel`, default: `json`)

**Response:**
```json
{
  "success": true,
  "message": "Report generated successfully",
  "data": {
    "report_id": "RPT-executive_summary-1689567890",
    "generated_by": "user-exec-001",
    "generated_at": "2025-07-13T10:30:00Z",
    "report_type": "executive_summary",
    "summary": {
      "total_contracts_reviewed": 42,
      "total_savings_identified": "507000.00",
      "average_savings_per_contract": "12071.43",
      "recommendations": {
        "renew": 18,
        "renegotiate": 15,
        "switch": 7,
        "terminate": 2
      }
    },
    "format": "json",
    "download_url": "/api/reports/download/executive_summary-1689567890.json"
  }
}
```

### Notification Management

#### GET /api/notifications/preferences

Get current user's notification preferences.

**Response:**
```json
{
  "user_id": "user-pm-001",
  "renewal_reminders": "weekly",
  "cost_alerts": "immediate",
  "contract_expiry_days": 60,
  "email_enabled": true,
  "dashboard_alerts": true,
  "mobile_push": false
}
```

#### PUT /api/notifications/preferences

Update notification preferences for the authenticated user.

**Request Body:**
```json
{
  "user_id": "user-pm-001",
  "renewal_reminders": "daily",
  "cost_alerts": "immediate",
  "contract_expiry_days": 45,
  "email_enabled": true,
  "dashboard_alerts": true,
  "mobile_push": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Notification preferences updated successfully",
  "data": {
    "user_id": "user-pm-001"
  }
}
```

#### POST /api/notifications/send-renewal-reminder

Send renewal reminder notification for a specific contract.

**Request Body:**
```json
{
  "contract_id": "CNT-001",
  "message": "Custom reminder message",
  "urgent": false
}
```

#### POST /api/notifications/send-cost-alert

Send cost alert notification for significant cost changes.

**Request Body:**
```json
{
  "contract_id": "CNT-001",
  "alert_type": "cost_increase",
  "cost_change_amount": 480.00,
  "cost_change_percentage": 10.0
}
```

#### GET /api/notifications/history

Retrieve notification history for the authenticated user.

**Query Parameters:**
- `limit` (int, optional): Maximum notifications (1-200, default: 50)
- `notification_type` (string, optional): Filter by notification type
- `date_from` (datetime, optional): Start date filter
- `date_to` (datetime, optional): End date filter

#### POST /api/notifications/bulk-send

Send bulk notifications to multiple recipients (admin only).

**Request Body:**
```json
{
  "recipient_user_ids": ["user-001", "user-002"],
  "notification_type": "system_announcement",
  "message": "System maintenance scheduled for tonight",
  "urgent": true
}
```

## Error Handling

The API uses standard HTTP status codes with detailed error responses:

### 400 Bad Request
```json
{
  "error": "VALIDATION_ERROR",
  "message": "Validation failed for days_ahead: must be between 30 and 365",
  "details": {
    "field": "days_ahead",
    "value": "10",
    "reason": "must be between 30 and 365"
  }
}
```

### 401 Unauthorized
```json
{
  "error": "AUTHENTICATION_REQUIRED",
  "message": "Could not validate credentials",
  "details": {}
}
```

### 403 Forbidden
```json
{
  "error": "INSUFFICIENT_PERMISSIONS",
  "message": "Insufficient permissions. Required: view_executive_reports",
  "details": {
    "required_permission": "view_executive_reports",
    "user_role": "property_manager"
  }
}
```

### 404 Not Found
```json
{
  "error": "CONTRACT_NOT_FOUND",
  "message": "Contract CNT-999 not found",
  "details": {
    "contract_id": "CNT-999"
  }
}
```

### 429 Too Many Requests
```json
{
  "error": "RATE_LIMIT_EXCEEDED",
  "message": "Rate limit exceeded for /api/renewals/dashboard. Retry after 60 seconds",
  "details": {
    "endpoint": "/api/renewals/dashboard",
    "retry_after": 60
  }
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "message": "An unexpected error occurred. Please contact support.",
  "request_id": "1689567890"
}
```

## Data Models

### RenewalItem
```json
{
  "contract_id": "string",
  "property_name": "string", 
  "vendor_name": "string",
  "expiry_date": "date",
  "days_to_expiry": "integer",
  "monthly_cost": "decimal",
  "priority": "enum[low,medium,high,critical]",
  "status": "enum[pending_review,under_analysis,negotiating,awaiting_approval,approved,renewed,declined]",
  "potential_savings": "decimal",
  "last_analyzed": "datetime"
}
```

### ContractDetails
```json
{
  "contract_id": "string",
  "property_id": "string",
  "vendor_id": "string", 
  "contract_type": "string",
  "start_date": "date",
  "end_date": "date",
  "monthly_cost": "decimal",
  "container_size": "number",
  "pickup_frequency": "integer",
  "fuel_surcharge": "decimal",
  "environmental_fee": "decimal",
  "admin_fee": "decimal",
  "early_termination_fee": "decimal",
  "price_increase_cap": "decimal"
}
```

### BenchmarkData
```json
{
  "metric_name": "string",
  "property_value": "decimal",
  "industry_average": "decimal", 
  "industry_percentile": "integer",
  "best_in_class": "decimal",
  "variance_percentage": "decimal",
  "improvement_opportunity": "decimal"
}
```

## Business Logic Constants

### Industry Benchmarks
- **Cost per door**: $10-30 per unit monthly (average: $16.50)
- **Volume per door**: 1.0-2.5 cubic yards per unit monthly
- **Standard container sizes**: 2, 4, 6, 8, 30, 34 cubic yards
- **Pickup frequency**: 1-7 times per week (typical: 3)

### Validation Rules
- Contract IDs: Format `CNT-XXXXXX`
- Property IDs: Format `PROP-XXXXXX`
- Monthly costs: $100 - $50,000 range
- Container sizes: 2-40 cubic yards
- Pickup frequency: 1-7 times per week
- Price increases: 0-100% range

### Alert Thresholds
- Cost increase warning: 5%
- Cost increase critical: 10%
- Savings opportunity: 10%
- High risk variance: 20% above industry average

## SDK Examples

### Python SDK Example
```python
import requests

class AdvantageWasteAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {token}"}
    
    def get_upcoming_renewals(self, days_ahead=90, priority=None):
        params = {"days_ahead": days_ahead}
        if priority:
            params["priority_filter"] = priority
        
        response = requests.get(
            f"{self.base_url}/api/renewals/upcoming",
            headers=self.headers,
            params=params
        )
        return response.json()
    
    def analyze_contract(self, contract_id, include_alternatives=True):
        data = {
            "contract_id": contract_id,
            "include_benchmarks": True,
            "include_alternatives": include_alternatives,
            "analysis_depth": "standard"
        }
        
        response = requests.post(
            f"{self.base_url}/api/renewals/{contract_id}/analyze",
            headers=self.headers,
            json=data
        )
        return response.json()

# Usage
api = AdvantageWasteAPI("http://localhost:8000", "your-token-here")
renewals = api.get_upcoming_renewals(days_ahead=120, priority="high")
analysis = api.analyze_contract("CNT-001")
```

### JavaScript SDK Example
```javascript
class AdvantageWasteAPI {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  async getUpcomingRenewals(daysAhead = 90, priority = null) {
    const params = new URLSearchParams({ days_ahead: daysAhead });
    if (priority) params.append('priority_filter', priority);

    const response = await fetch(
      `${this.baseUrl}/api/renewals/upcoming?${params}`,
      { headers: this.headers }
    );
    return response.json();
  }

  async analyzeContract(contractId, includeAlternatives = true) {
    const data = {
      contract_id: contractId,
      include_benchmarks: true,
      include_alternatives: includeAlternatives,
      analysis_depth: 'standard'
    };

    const response = await fetch(
      `${this.baseUrl}/api/renewals/${contractId}/analyze`,
      {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(data)
      }
    );
    return response.json();
  }
}

// Usage
const api = new AdvantageWasteAPI('http://localhost:8000', 'your-token-here');
const renewals = await api.getUpcomingRenewals(120, 'high');
const analysis = await api.analyzeContract('CNT-001');
```

## Testing

Run the comprehensive test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov

# Run all tests
pytest backend/tests/ -v

# Run with coverage
pytest backend/tests/ --cov=backend/src --cov-report=html

# Run specific test categories
pytest backend/tests/test_renewals_api.py::TestUpcomingRenewals -v
```

## Deployment

### Environment Variables
```bash
# Required
SECRET_KEY=your-secure-secret-key-here
DATABASE_URL=postgresql://user:password@localhost/advantage_waste

# Optional
ENVIRONMENT=production
DEBUG=false
ALLOWED_ORIGINS=["https://advantage-waste.greystar.com"]
REDIS_URL=redis://localhost:6379
EMAIL_SERVICE_URL=https://api.sendgrid.com
EMAIL_API_KEY=your-sendgrid-api-key
```

### Docker Deployment
```bash
# Build image
docker build -t advantage-waste-api .

# Run container
docker run -p 8000:8000 \
  -e SECRET_KEY=your-secret-key \
  -e DATABASE_URL=**********************************/advantage_waste \
  advantage-waste-api
```

## Support

For API support, please contact:
- **Development Team**: <EMAIL>
- **Documentation**: https://docs.advantage-waste.greystar.com
- **Status Page**: https://status.advantage-waste.greystar.com