"""
Performance and Scale Testing for Advantage Waste Enterprise
===========================================================

Load testing and performance validation for enterprise-scale operations
handling 3,850+ Greystar properties with realistic data volumes.

Built by Performance Testing Agent - Enterprise Development Force
"""

import pytest
import asyncio
import time
import statistics
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

# Import application components
from src.models.renewal import (
    Contract, Property, Vendor, RenewalAlert, RenewalAnalysis
)
from src.services.renewal_analyzer import EnterpriseRenewalAnalyzer
from src.services.email_service import EmailService, EmailRecipient
from src.tasks.renewal_scanner import scan_contract_expirations


class TestEnterpriseScalePerformance:
    """Performance tests for enterprise-scale operations"""
    
    @pytest.fixture
    def performance_database_session(self):
        """High-performance mock database session"""
        session = Mock()
        
        # Optimize mock responses for performance testing
        query_mock = Mock()
        session.query.return_value = query_mock
        
        # Fast query chaining
        query_mock.filter.return_value = query_mock
        query_mock.join.return_value = query_mock
        query_mock.limit.return_value = query_mock
        query_mock.all.return_value = []
        query_mock.first.return_value = None
        query_mock.scalar.return_value = 0
        
        # Fast session operations
        session.add = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.close = Mock()
        
        return session
    
    def generate_portfolio_contracts(self, count: int) -> List[Contract]:
        """Generate realistic portfolio of contracts for performance testing"""
        
        contracts = []
        property_types = ["garden-style", "mid-rise", "high-rise"]
        vendors = [
            "Waste Management", "Republic Services", "GFL Environmental", 
            "Waste Connections", "Casella Waste", "Advanced Disposal"
        ]
        states = ["TX", "GA", "FL", "NC", "CA", "AZ", "CO", "VA"]
        
        for i in range(count):
            # Distribute properties across realistic scenarios
            property_type = property_types[i % len(property_types)]
            vendor_name = vendors[i % len(vendors)]
            state = states[i % len(states)]
            
            # Unit counts based on property type
            if property_type == "garden-style":
                unit_count = 200 + (i % 300)  # 200-500 units
                base_rate = Decimal(str(4000 + (i % 3000)))  # $4K-7K
            elif property_type == "mid-rise":
                unit_count = 150 + (i % 200)  # 150-350 units
                base_rate = Decimal(str(3500 + (i % 2500)))  # $3.5K-6K
            else:  # high-rise
                unit_count = 100 + (i % 250)  # 100-350 units
                base_rate = Decimal(str(3000 + (i % 2000)))  # $3K-5K
            
            # Create property
            property_obj = Property(
                id=f"perf-prop-{i:04d}",
                name=f"Property {i:04d}",
                address=f"{1000 + i} Main Street",
                city=f"City{i % 50}",
                state=state,
                zip_code=f"{10000 + (i % 90000):05d}",
                unit_count=unit_count,
                property_type=property_type
            )
            
            # Create vendor
            vendor_obj = Vendor(
                id=f"perf-vendor-{i % len(vendors)}",
                name=vendor_name,
                contact_email=f"sales@{vendor_name.lower().replace(' ', '')}.com",
                service_areas={state: [f"City{i % 50}"]}
            )
            
            # Create contract with realistic expiration distribution
            days_to_expiration = 1 + (i % 365)  # Spread over 365 days
            
            contract = Contract(
                id=f"perf-contract-{i:04d}",
                property_id=property_obj.id,
                vendor_id=vendor_obj.id,
                contract_number=f"PERF-{i:04d}",
                vendor_name=vendor_name,
                contact_name=f"Contact {i}",
                contact_email=f"contact{i}@{vendor_name.lower().replace(' ', '')}.com",
                
                # Contract dates
                contract_start_date=datetime.utcnow() - timedelta(days=365),
                contract_end_date=datetime.utcnow() + timedelta(days=days_to_expiration),
                contract_length_months=12,
                automatic_renewal=True,
                renewal_term_months=12,
                termination_notice_days=30,
                
                # Service configuration
                container_size_yards=30 if property_type == "high-rise" else 34,
                container_type="compactor",
                container_quantity=1 if unit_count < 200 else 2,
                pickup_frequency_weekly=2 if unit_count > 300 else 1,
                
                # Financial terms
                base_monthly_rate=base_rate,
                fuel_surcharge_percent=Decimal("5.0") + Decimal(str(i % 10)),
                environmental_fee_percent=Decimal(str(i % 5)),
                admin_fee=Decimal("50.00"),
                
                # Contract terms
                cpi_increases=True,
                max_annual_increase_percent=Decimal("4.0"),
                payment_terms_days=30,
                
                # Relationships
                property=property_obj,
                vendor=vendor_obj
            )
            
            contracts.append(contract)
        
        return contracts
    
    @pytest.mark.performance
    def test_full_portfolio_scanning_performance(self, performance_database_session):
        """Test scanning performance with full 3,850 property portfolio"""
        
        # Generate full Greystar portfolio
        full_portfolio = self.generate_portfolio_contracts(3850)
        
        # Mock database to return full portfolio
        performance_database_session.query.return_value.filter.return_value.join.return_value.join.return_value.all.return_value = full_portfolio
        
        with patch('src.tasks.renewal_scanner.get_database_session', return_value=performance_database_session):
            with patch('src.tasks.renewal_scanner.process_notification_queue.delay'):
                with patch('src.tasks.renewal_scanner.schedule_contract_analysis.delay'):
                    
                    # Performance measurement
                    start_time = time.time()
                    
                    scan_result = scan_contract_expirations()
                    
                    end_time = time.time()
                    total_time = end_time - start_time
                    
                    # Performance assertions for enterprise scale
                    assert scan_result['status'] == 'success'
                    assert scan_result['contracts_processed'] == 3850
                    assert total_time < 300  # Complete in under 5 minutes
                    
                    # Calculate performance metrics
                    contracts_per_second = 3850 / total_time
                    avg_time_per_contract = total_time / 3850
                    
                    # Performance targets
                    assert contracts_per_second > 20  # Process >20 contracts/second
                    assert avg_time_per_contract < 0.05  # <50ms per contract
                    
                    print(f"Portfolio scan performance:")
                    print(f"  Total time: {total_time:.2f} seconds")
                    print(f"  Contracts/second: {contracts_per_second:.1f}")
                    print(f"  Avg time/contract: {avg_time_per_contract*1000:.1f}ms")
    
    @pytest.mark.performance
    def test_contract_analysis_performance_at_scale(self, performance_database_session):
        """Test contract analysis performance with realistic load"""
        
        # Test batch of 100 contracts requiring analysis
        contracts_for_analysis = self.generate_portfolio_contracts(100)
        
        # Focus on contracts expiring in 90 days (typical analysis trigger)
        for contract in contracts_for_analysis:
            contract.contract_end_date = datetime.utcnow() + timedelta(days=90)
            contract.renewal_analysis_completed = False
        
        analyzer = EnterpriseRenewalAnalyzer(performance_database_session)
        
        # Mock vendor alternatives (realistic enterprise scenario)
        mock_alternatives = [
            Vendor(id=f"alt-{i}", name=f"Alternative Vendor {i}", service_areas={"TX": ["Dallas"]})
            for i in range(3)
        ]
        
        analysis_times = []
        
        with patch.object(analyzer, '_find_alternative_vendors', return_value=mock_alternatives):
            
            for i, contract in enumerate(contracts_for_analysis[:25]):  # Test subset for timing
                start_time = time.time()
                
                try:
                    analysis = analyzer.analyze_renewal_opportunity(
                        contract=contract,
                        property_info=contract.property,
                        alternative_vendors=mock_alternatives
                    )
                    
                    end_time = time.time()
                    analysis_time = end_time - start_time
                    analysis_times.append(analysis_time)
                    
                    # Verify analysis completeness
                    assert analysis.primary_recommendation is not None
                    assert analysis.recommendation_confidence > 0
                    
                except Exception as e:
                    print(f"Analysis failed for contract {i}: {e}")
                    # Don't fail the test, but track the issue
                    analysis_times.append(10.0)  # Penalty time for failures
        
        # Performance analysis
        if analysis_times:
            avg_analysis_time = statistics.mean(analysis_times)
            max_analysis_time = max(analysis_times)
            min_analysis_time = min(analysis_times)
            
            # Performance targets for contract analysis
            assert avg_analysis_time < 2.0  # Average under 2 seconds
            assert max_analysis_time < 5.0  # No analysis over 5 seconds
            
            print(f"Contract analysis performance:")
            print(f"  Avg analysis time: {avg_analysis_time:.2f}s")
            print(f"  Max analysis time: {max_analysis_time:.2f}s") 
            print(f"  Min analysis time: {min_analysis_time:.2f}s")
            
            # Extrapolate to full portfolio
            full_portfolio_time = avg_analysis_time * 3850
            print(f"  Estimated full portfolio time: {full_portfolio_time/3600:.1f} hours")
            
            # Should be able to analyze full portfolio in reasonable time
            assert full_portfolio_time < 28800  # Under 8 hours
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_notification_system_performance(self):
        """Test email notification system performance at scale"""
        
        # Generate large recipient list (realistic for enterprise)
        recipients = []
        for i in range(500):  # 500 notifications (typical daily volume)
            recipients.append(EmailRecipient(
                email=f"user{i:03d}@greystar.com",
                name=f"User {i:03d}",
                role="property_manager" if i % 3 == 0 else "regional_director"
            ))
        
        # Mock high-performance email sending
        async def mock_fast_send_email(**kwargs):
            await asyncio.sleep(0.01)  # Simulate 10ms network latency
            return True
        
        with patch('src.services.email_service.EmailService._send_email', side_effect=mock_fast_send_email):
            email_service = EmailService()
            
            # Create sample contract and alert for notifications
            sample_contract = Mock()
            sample_contract.id = "perf-test-contract"
            sample_contract.contract_number = "PERF-001"
            sample_contract.property = Mock(name="Performance Test Property", unit_count=250)
            sample_contract.vendor_name = "Test Vendor"
            sample_contract.base_monthly_rate = Decimal("5000.00")
            sample_contract.contract_end_date = datetime.utcnow() + timedelta(days=90)
            
            sample_alert = Mock()
            sample_alert.id = "perf-test-alert"
            sample_alert.days_before_expiration = 90
            sample_alert.priority_level = "medium"
            
            # Performance test: concurrent notifications
            start_time = time.time()
            
            # Send notifications concurrently (realistic enterprise pattern)
            tasks = []
            batch_size = 50  # Process in batches to avoid overwhelming system
            
            for i in range(0, len(recipients), batch_size):
                batch = recipients[i:i + batch_size]
                
                batch_tasks = [
                    email_service.send_renewal_notification(
                        recipient=recipient,
                        contract=sample_contract,
                        alert=sample_alert
                    )
                    for recipient in batch
                ]
                
                # Execute batch concurrently
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # Verify batch results
                successful_sends = sum(1 for result in batch_results if result is True)
                assert successful_sends >= len(batch) * 0.95  # 95% success rate minimum
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Performance assertions
            notifications_per_second = len(recipients) / total_time
            avg_time_per_notification = total_time / len(recipients)
            
            assert notifications_per_second > 10  # >10 notifications/second
            assert avg_time_per_notification < 0.1  # <100ms per notification
            assert total_time < 60  # Complete 500 notifications in under 1 minute
            
            print(f"Notification performance:")
            print(f"  Total time: {total_time:.2f}s")
            print(f"  Notifications/second: {notifications_per_second:.1f}")
            print(f"  Avg time/notification: {avg_time_per_notification*1000:.1f}ms")
    
    @pytest.mark.performance
    def test_concurrent_workflow_performance(self, performance_database_session):
        """Test performance of concurrent renewal workflows"""
        
        # Generate contracts requiring simultaneous processing
        concurrent_contracts = self.generate_portfolio_contracts(100)
        
        # Set up concurrent processing scenario
        for i, contract in enumerate(concurrent_contracts):
            # Different expiration windows to trigger different workflows
            if i % 4 == 0:
                contract.contract_end_date = datetime.utcnow() + timedelta(days=30)  # Critical
            elif i % 4 == 1:
                contract.contract_end_date = datetime.utcnow() + timedelta(days=60)  # High
            elif i % 4 == 2:
                contract.contract_end_date = datetime.utcnow() + timedelta(days=90)  # Medium
            else:
                contract.contract_end_date = datetime.utcnow() + timedelta(days=120)  # Low
        
        def process_contract_workflow(contract):
            """Simulate complete contract workflow processing"""
            start_time = time.time()
            
            try:
                # Step 1: Alert creation (simulated)
                alert_creation_time = 0.01
                
                # Step 2: Analysis execution (simulated)
                analyzer = EnterpriseRenewalAnalyzer(performance_database_session)
                
                with patch.object(analyzer, 'analyze_renewal_opportunity') as mock_analysis:
                    mock_result = Mock()
                    mock_result.primary_recommendation = "renegotiate"
                    mock_result.savings_opportunity = Decimal("5000.00")
                    mock_analysis.return_value = mock_result
                    
                    analysis_result = analyzer.analyze_renewal_opportunity(
                        contract=contract,
                        property_info=contract.property
                    )
                
                # Step 3: Notification processing (simulated)
                notification_time = 0.02
                
                end_time = time.time()
                total_workflow_time = end_time - start_time
                
                return {
                    "contract_id": contract.id,
                    "workflow_time": total_workflow_time,
                    "success": True
                }
                
            except Exception as e:
                end_time = time.time()
                return {
                    "contract_id": contract.id,
                    "workflow_time": end_time - start_time,
                    "success": False,
                    "error": str(e)
                }
        
        # Execute concurrent workflows
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            # Submit all workflows concurrently
            future_to_contract = {
                executor.submit(process_contract_workflow, contract): contract
                for contract in concurrent_contracts
            }
            
            # Collect results
            workflow_results = []
            for future in as_completed(future_to_contract):
                result = future.result()
                workflow_results.append(result)
        
        end_time = time.time()
        total_concurrent_time = end_time - start_time
        
        # Analyze concurrent performance
        successful_workflows = [r for r in workflow_results if r["success"]]
        failed_workflows = [r for r in workflow_results if not r["success"]]
        
        # Performance assertions
        success_rate = len(successful_workflows) / len(workflow_results)
        avg_workflow_time = statistics.mean([r["workflow_time"] for r in successful_workflows])
        
        assert success_rate > 0.95  # 95% success rate
        assert avg_workflow_time < 3.0  # Average workflow under 3 seconds
        assert total_concurrent_time < 60  # All workflows complete in under 1 minute
        
        print(f"Concurrent workflow performance:")
        print(f"  Success rate: {success_rate*100:.1f}%")
        print(f"  Total concurrent time: {total_concurrent_time:.2f}s")
        print(f"  Avg workflow time: {avg_workflow_time:.2f}s")
        print(f"  Failed workflows: {len(failed_workflows)}")
        
        # Verify concurrency benefit
        sequential_time_estimate = avg_workflow_time * len(concurrent_contracts)
        concurrency_speedup = sequential_time_estimate / total_concurrent_time
        
        assert concurrency_speedup > 5  # At least 5x speedup from concurrency
        print(f"  Concurrency speedup: {concurrency_speedup:.1f}x")
    
    @pytest.mark.performance
    def test_memory_usage_efficiency(self):
        """Test memory efficiency with large datasets"""
        
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Generate large portfolio and process in batches
        total_contracts = 10000  # Larger than actual portfolio for stress testing
        batch_size = 500
        
        max_memory_usage = initial_memory
        
        for batch_start in range(0, total_contracts, batch_size):
            batch_end = min(batch_start + batch_size, total_contracts)
            batch_contracts = self.generate_portfolio_contracts(batch_end - batch_start)
            
            # Process batch (simulate real operations)
            for contract in batch_contracts:
                # Simulate data processing
                contract_data = {
                    "id": contract.id,
                    "annual_cost": float(contract.base_monthly_rate * 12),
                    "property_info": {
                        "name": contract.property.name,
                        "units": contract.property.unit_count
                    }
                }
                
                # Simulate JSON serialization (common in API responses)
                json_data = json.dumps(contract_data)
            
            # Check memory usage after batch
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            max_memory_usage = max(max_memory_usage, current_memory)
            
            # Clear batch to simulate proper memory management
            del batch_contracts
        
        # Memory efficiency assertions
        memory_increase = max_memory_usage - initial_memory
        memory_per_1000_contracts = memory_increase / (total_contracts / 1000)
        
        assert memory_increase < 500  # Less than 500MB increase for 10K contracts
        assert memory_per_1000_contracts < 50  # Less than 50MB per 1000 contracts
        
        print(f"Memory efficiency:")
        print(f"  Initial memory: {initial_memory:.1f}MB")
        print(f"  Max memory: {max_memory_usage:.1f}MB")
        print(f"  Memory increase: {memory_increase:.1f}MB")
        print(f"  Memory per 1K contracts: {memory_per_1000_contracts:.1f}MB")
    
    @pytest.mark.performance
    def test_database_query_optimization(self, performance_database_session):
        """Test database query performance optimization"""
        
        # Simulate complex queries typical in renewal processing
        query_performance_tests = [
            {
                "name": "contract_expiration_scan",
                "description": "Daily scan for expiring contracts",
                "expected_max_time": 0.1  # 100ms
            },
            {
                "name": "vendor_performance_analysis", 
                "description": "Weekly vendor performance queries",
                "expected_max_time": 0.5  # 500ms
            },
            {
                "name": "portfolio_metrics_calculation",
                "description": "Monthly portfolio-wide calculations",
                "expected_max_time": 1.0  # 1 second
            }
        ]
        
        query_results = []
        
        for test in query_performance_tests:
            # Simulate query execution time
            start_time = time.time()
            
            # Mock database operations (in real test, these would be actual queries)
            if test["name"] == "contract_expiration_scan":
                # Simulate contract filtering and joining
                result = performance_database_session.query.return_value.filter.return_value.join.return_value.join.return_value.all.return_value
            elif test["name"] == "vendor_performance_analysis":
                # Simulate vendor aggregation queries
                result = performance_database_session.query.return_value.filter.return_value.limit.return_value.all.return_value
            else:
                # Simulate portfolio-wide calculations
                result = performance_database_session.query.return_value.scalar.return_value
            
            end_time = time.time()
            query_time = end_time - start_time
            
            query_results.append({
                "name": test["name"],
                "time": query_time,
                "expected_max": test["expected_max_time"],
                "passed": query_time <= test["expected_max_time"]
            })
        
        # Verify all queries meet performance expectations
        all_passed = all(result["passed"] for result in query_results)
        
        print("Database query performance:")
        for result in query_results:
            status = "PASS" if result["passed"] else "FAIL"
            print(f"  {result['name']}: {result['time']*1000:.1f}ms (max: {result['expected_max']*1000:.0f}ms) - {status}")
        
        assert all_passed, "Some database queries exceeded performance expectations"


class TestLoadTesting:
    """Load testing for peak usage scenarios"""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_peak_load_simulation(self):
        """Simulate peak load conditions (end of quarter, multiple renewals)"""
        
        # Simulate end-of-quarter scenario: 500 contracts expiring simultaneously
        peak_load_contracts = []
        
        for i in range(500):
            contract = Mock()
            contract.id = f"peak-load-{i:03d}"
            contract.contract_end_date = datetime.utcnow() + timedelta(days=30)  # All critical
            contract.base_monthly_rate = Decimal("5000.00")
            contract.property = Mock(name=f"Peak Property {i}", unit_count=250)
            contract.vendor_name = f"Vendor {i % 10}"
            peak_load_contracts.append(contract)
        
        # Test system behavior under peak load
        start_time = time.time()
        
        # Simulate concurrent processing of critical renewals
        async def process_critical_renewal(contract):
            """Process a single critical renewal"""
            await asyncio.sleep(0.1)  # Simulate processing time
            return {
                "contract_id": contract.id,
                "processed": True,
                "processing_time": 0.1
            }
        
        # Execute peak load processing
        tasks = [process_critical_renewal(contract) for contract in peak_load_contracts]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_peak_time = end_time - start_time
        
        # Peak load performance assertions
        successful_results = [r for r in results if isinstance(r, dict) and r.get("processed")]
        success_rate = len(successful_results) / len(peak_load_contracts)
        
        assert success_rate > 0.98  # 98% success rate under peak load
        assert total_peak_time < 120  # Handle peak load in under 2 minutes
        
        throughput = len(peak_load_contracts) / total_peak_time
        assert throughput > 5  # Process >5 critical renewals per second
        
        print(f"Peak load performance:")
        print(f"  Contracts processed: {len(successful_results)}")
        print(f"  Success rate: {success_rate*100:.1f}%")
        print(f"  Total time: {total_peak_time:.2f}s")
        print(f"  Throughput: {throughput:.1f} contracts/second")
    
    @pytest.mark.performance
    def test_sustained_load_endurance(self):
        """Test system endurance under sustained load"""
        
        # Simulate sustained processing over extended period
        duration_minutes = 5  # 5-minute endurance test
        contracts_per_minute = 50  # 50 contracts per minute sustained
        
        total_contracts_processed = 0
        processing_times = []
        error_count = 0
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        while time.time() < end_time:
            minute_start = time.time()
            
            # Process contracts for this minute
            for i in range(contracts_per_minute):
                contract_start = time.time()
                
                try:
                    # Simulate contract processing
                    time.sleep(0.01)  # 10ms processing time
                    
                    contract_end = time.time()
                    processing_times.append(contract_end - contract_start)
                    total_contracts_processed += 1
                    
                except Exception:
                    error_count += 1
            
            # Maintain sustained rate
            minute_elapsed = time.time() - minute_start
            if minute_elapsed < 60:
                time.sleep(60 - minute_elapsed)
        
        actual_duration = time.time() - start_time
        
        # Endurance performance assertions
        avg_processing_time = statistics.mean(processing_times)
        actual_rate = total_contracts_processed / (actual_duration / 60)  # contracts per minute
        error_rate = error_count / total_contracts_processed if total_contracts_processed > 0 else 1
        
        assert actual_rate >= contracts_per_minute * 0.95  # Maintain 95% of target rate
        assert error_rate < 0.01  # Less than 1% error rate
        assert avg_processing_time < 0.05  # Average processing under 50ms
        
        print(f"Sustained load endurance:")
        print(f"  Duration: {actual_duration/60:.1f} minutes")
        print(f"  Contracts processed: {total_contracts_processed}")
        print(f"  Actual rate: {actual_rate:.1f} contracts/minute")
        print(f"  Error rate: {error_rate*100:.2f}%")
        print(f"  Avg processing time: {avg_processing_time*1000:.1f}ms")


# Run performance tests
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-m", "performance"])