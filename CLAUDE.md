# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Advantage Waste Enterprise - Claude Code Rules

## Project Overview
Enterprise waste management solution with contract analysis, cost optimization, and comprehensive reporting capabilities for Greystar's Advantage Waste division.

## Core Principles
- **Industry Focus**: Multifamily property waste management (200+ units typical)
- **Enterprise Grade**: Production-ready, scalable, secure
- **Data-Driven**: Analytics and optimization at the core
- **User-Centric**: Designed for property managers and waste professionals
- **Greystar Standards**: Follow Advantage Waste methodology and best practices

## Architecture
- **Backend**: FastAPI with SQLAlchemy, PostgreSQL
- **Frontend**: React/TypeScript with Vite and Tailwind (integrate with existing Lovable frontend)
- **Integration**: RESTful APIs with comprehensive validation
- **Deployment**: Docker containers with CI/CD

## Code Standards
- **Python**: PEP 8, type hints, async/await patterns
- **TypeScript**: Strict mode, comprehensive typing
- **Testing**: 90%+ coverage, unit + integration + e2e
- **Documentation**: Comprehensive docstrings and comments
- **Security**: Proper input validation, authentication, authorization

## Development Commands
- Backend tests: `pytest backend/tests/ -v`
- Frontend tests: `npm test`
- Linting: `ruff check backend/src/` and `npm run lint`
- Type checking: `mypy backend/src/` and `npm run type-check`
- Start backend: `uvicorn backend.src.main:app --reload`
- Start frontend: `npm run dev`

## File Size Limits
- Python files: 500 lines max
- TypeScript files: 300 lines max
- Components: 200 lines max
- Break larger files into logical modules

## Waste Management Domain Knowledge

### Key Metrics
- **Cost per door**: $10-30 per unit monthly (industry benchmark)
- **Yards per door**: 1.0-2.5 cubic yards per unit monthly
- **Pickup frequency**: 1-3 times per week typical
- **Container sizes**: 2, 4, 6, 8 cubic yards (front-load), 30-34 cubic yards (compactor)
- **Compaction ratios**: 3:1 to 5:1 typical

### Industry Formulas
- **Monthly volume**: Container Size × Quantity × Pickups/Week × 4.33
- **Cost per yard**: Total Monthly Cost ÷ Total Monthly Volume
- **Cost per door**: Total Monthly Cost ÷ Number of Units
- **Compacted volume**: (Tons × 2000) ÷ (Density × Compaction Ratio)

### Contract Best Practices
- **Contract length**: 12-24 months for flexibility
- **Price increases**: Capped at CPI or 4% maximum
- **Fuel surcharges**: Avoid or cap at 5%
- **Environmental fees**: Avoid when possible
- **Early termination**: 3-4 months average charges maximum

### Property Types
- **Garden-style apartments**: 2.0-2.25 yd³/door, higher costs
- **Mid-rise/Mixed-use**: ~1.5 yd³/door, moderate costs  
- **High-rise**: 1.0-1.5 yd³/door, lower costs per door

## Always Check When Building Features
1. **Validate calculations** using industry formulas above
2. **Ensure contract terms** follow Advantage Waste best practices
3. **Include proper error handling** for all external integrations
4. **Follow Greystar branding** and design standards
5. **Test with realistic data** (252-unit property examples)
6. **Consider scalability** for 3,850+ Greystar locations
7. **Include audit trails** for all financial calculations
8. **Validate user permissions** for data access

## Data Validation Rules
- **Container sizes** must be standard industry sizes
- **Pickup frequency** must be 1-7 times per week
- **Contract amounts** must be positive decimals
- **Property units** must be positive integers
- **Percentages** must be 0-100%
- **Dates** must be valid and logical (start < end)

## Integration Requirements
- **Greystar systems**: Must integrate with existing property management
- **Advantage Waste portal**: Use existing authentication and permissions
- **Financial systems**: Proper cost tracking and reporting
- **Vendor APIs**: Support major waste management company APIs

## Security & Compliance
- **Data encryption**: All sensitive data encrypted at rest and in transit
- **Access control**: Role-based permissions (property managers, directors, admins)
- **Audit logging**: All financial calculations and decisions logged
- **Data retention**: Follow corporate data retention policies
- **Privacy**: Handle vendor and property data with appropriate controls

## Error Handling Patterns
- **API errors**: Return structured error responses with helpful messages
- **Validation errors**: Clear field-level validation messages
- **Calculation errors**: Graceful handling with fallback values
- **File processing**: Detailed error reporting for uploads
- **External API failures**: Retry logic with exponential backoff

## Testing Requirements
- **Unit tests**: All calculation functions, validators, utilities
- **Integration tests**: API endpoints, database operations
- **E2E tests**: Critical user workflows (contract analysis, reporting)
- **Mock data**: Use realistic waste management scenarios
- **Performance tests**: Handle large property portfolios

## Documentation Standards
- **API docs**: OpenAPI/Swagger with examples
- **Code comments**: Explain business logic and calculations
- **User guides**: Step-by-step workflows for property managers
- **Technical docs**: Architecture decisions and integration guides

Remember: This is an enterprise solution for Greystar's waste management operations. Every feature should reflect the professional standards and business requirements of a major real estate company managing thousands of properties.
