# Execute PRP (Product Requirements Prompt)

Implement a feature using a comprehensive PRP document, following the implementation blueprint and validation gates to achieve working code.

## Arguments: $ARGUMENTS

The first argument should be the path to a PRP markdown file in the PRPs/ directory.

## Pre-Implementation Analysis

1. **Read the complete PRP** from the specified file path
2. **Load all context** referenced in the PRP (documentation, examples, gotchas)
3. **Understand the goal** and success criteria
4. **Review implementation blueprint** and identify dependencies
5. **Prepare validation commands** for each validation level

## Implementation Strategy

### Context Loading
- Load all files referenced in "Code Patterns to Follow"
- Read industry documentation and standards
- Review existing codebase for integration points
- Understand data models and API patterns

### Planning Phase
- Break down implementation blueprint into specific tasks
- Identify file changes needed (backend, frontend, database)
- Plan validation strategy for each component
- Consider error handling and edge cases

### Progressive Implementation
- Start with foundation/core structure
- Implement business logic with proper validation
- Add API endpoints and database integration
- Build frontend components and user workflows
- Implement comprehensive testing

## Implementation Pattern

### 1. Backend Implementation
```python
# Follow patterns from examples/contract_analysis_example.py
# Use proper data models with validation
# Implement service layer for business logic
# Add comprehensive error handling
# Include audit logging for financial operations
```

### 2. Frontend Implementation
```typescript
// Follow React best practices with TypeScript
// Use proper state management and data fetching
// Implement user-friendly error handling
// Include loading states and validation feedback
// Follow Advantage Waste design standards
```

### 3. Database Changes
```sql
-- Create migrations following Alembic patterns
-- Use proper foreign key relationships
-- Include audit columns (created_at, updated_at, created_by)
-- Add appropriate indexes for performance
```

### 4. API Integration
```python
# RESTful endpoints with proper HTTP status codes
# Comprehensive input validation using Pydantic
# Standardized error response format
# OpenAPI documentation with examples
# Authentication and authorization checks
```

## Validation Loop Execution

### Level 1: Syntax & Style
```bash
# Backend validation
cd backend
ruff check src/ --fix
mypy src/
black src/

# Frontend validation  
cd frontend
npm run lint:fix
npm run type-check
```

### Level 2: Unit Tests
```bash
# Run unit tests for new functionality
pytest backend/tests/unit/test_[feature].py -v
npm test -- --testPathPattern=[feature]
```

### Level 3: Integration Tests
```bash
# Test API endpoints and database integration
pytest backend/tests/integration/test_[feature]_api.py -v
```

### Level 4: Business Logic Validation
```python
# Validate waste management calculations
# Test industry benchmark comparisons
# Verify contract analysis accuracy
# Confirm cost optimization logic
```

### Level 5: User Acceptance Testing
```bash
# End-to-end workflow testing
npm run test:e2e -- --spec="[feature].spec.ts"
```

## File Organization

### Backend Files
- `backend/src/api/[domain]/` - API endpoints
- `backend/src/core/` - Business logic
- `backend/src/models/` - Data models
- `backend/src/services/` - Service layer
- `backend/src/utils/` - Utility functions
- `backend/tests/` - Comprehensive tests

### Frontend Files
- `frontend/src/components/[feature]/` - Feature components
- `frontend/src/pages/` - Page components
- `frontend/src/services/` - API services
- `frontend/src/hooks/` - Custom hooks
- `frontend/src/types/` - TypeScript definitions

### Documentation
- `docs/api/` - API documentation updates
- `docs/user-guide/` - User workflow documentation
- Update README.md with new features

## Error Handling Strategy

### Backend Error Handling
```python
from fastapi import HTTPException
from src.core.exceptions import (
    ContractValidationError,
    CalculationError,
    IntegrationError
)

# Comprehensive error handling with proper HTTP status codes
# Detailed error messages for debugging
# User-friendly error messages for frontend
# Audit logging for all errors
```

### Frontend Error Handling
```typescript
// Comprehensive error boundaries
// User-friendly error messages
// Retry mechanisms for transient failures
// Graceful degradation for partial failures
```

## Security Implementation

### Data Protection
- Encrypt sensitive contract and financial data
- Implement proper input validation and sanitization
- Use parameterized queries to prevent SQL injection
- Validate file uploads (PDF contracts, Excel reports)

### Access Control
- Role-based authentication (property managers, directors, admins)
- API endpoint authorization
- Data access logging and audit trails
- Session management and timeout handling

### Compliance
- Follow Greystar data retention policies
- Implement audit logging for financial calculations
- Ensure vendor data privacy protection
- Handle PII according to corporate policies

## Performance Considerations

### Backend Performance
- Database query optimization with proper indexes
- Caching for frequently accessed data (industry benchmarks)
- Async/await patterns for I/O operations
- Connection pooling for database access

### Frontend Performance
- Lazy loading for large datasets
- Optimistic updates for better UX
- Proper loading states and error boundaries
- Component memoization for expensive calculations

## Integration Requirements

### Advantage Waste Portal
- Single sign-on integration
- User role synchronization
- Property data synchronization
- Consistent branding and navigation

### Greystar Systems
- Property management system integration
- Financial system reporting
- Vendor management coordination
- Contract lifecycle management

### External APIs
- Waste management vendor APIs
- Industry benchmark data sources
- Geographic and regulatory data
- Email and notification services

## Validation Criteria

Before marking implementation complete:

### Functional Requirements
- [ ] All success criteria from PRP met
- [ ] Business logic calculations verified
- [ ] Integration points working correctly
- [ ] Error handling comprehensive

### Technical Requirements  
- [ ] Code follows project standards (CLAUDE.md)
- [ ] All tests passing with >90% coverage
- [ ] Performance requirements met
- [ ] Security requirements implemented

### Documentation
- [ ] API documentation updated
- [ ] User guide sections added
- [ ] Code comments for complex business logic
- [ ] Deployment notes updated

## Post-Implementation

### Code Review
- Verify implementation follows PRP blueprint
- Check adherence to waste management standards
- Validate integration with existing systems
- Confirm security and performance requirements

### Deployment Preparation
- Update environment configurations
- Prepare database migrations
- Create deployment documentation
- Plan rollback procedures

### Knowledge Transfer
- Update examples/ with new patterns
- Document lessons learned
- Update team knowledge base
- Prepare training materials

## Iteration Strategy

If validation fails:
1. **Identify root cause** of validation failure
2. **Refer back to PRP context** for correct approach
3. **Fix specific issues** without breaking existing functionality
4. **Re-run validation loop** to confirm fixes
5. **Continue until all criteria met**

## Success Metrics

Track implementation success:
- Time to complete validation loop
- Number of iteration cycles needed
- Test coverage percentage
- Performance benchmark achievement
- User acceptance test results

Target: One-pass implementation with minimal iteration cycles.

## Example Usage

```bash
/execute-prp PRPs/contract-comparison-dashboard.md
```

This will implement the complete feature according to the PRP blueprint, run all validation gates, and ensure integration with the Advantage Waste enterprise system.

Remember: Follow the implementation blueprint exactly, use all provided context, and don't deviate from proven patterns without compelling technical reasons.
