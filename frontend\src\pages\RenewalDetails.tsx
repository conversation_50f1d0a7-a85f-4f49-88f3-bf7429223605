import { useParams, useNavigate } from 'react-router-dom'

export default function RenewalDetails() {
  const { id } = useParams()
  const navigate = useNavigate()

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">Contract Details</h1>
            <button
              onClick={() => navigate('/renewals')}
              className="text-greystar-blue hover:text-greystar-dark-blue"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg p-6">
          <p className="text-gray-500">Contract ID: {id}</p>
          <p className="text-gray-500 mt-4">Contract details will be displayed here.</p>
        </div>
      </main>
    </div>
  )
}