#!/bin/bash
# Claude Code startup script for Advantage Waste Enterprise

echo "Starting Claude Code for Advantage Waste Enterprise..."

# Set the project directory
PROJECT_DIR="/mnt/c/Users/<USER>/Documents/Claude Code/advantage-waste-enterprise"

# Change to project directory
cd "$PROJECT_DIR"

# Set environment variables for waste management context
export CLAUDE_PROJECT="advantage-waste-enterprise"
export CLAUDE_CONTEXT_FILES="CLAUDE.md,examples/contract_analysis_example.py,PRPs/templates/prp_base.md"

# Start Claude Code with project context
claude --project-root "$PROJECT_DIR" --context-files "$CLAUDE_CONTEXT_FILES"
