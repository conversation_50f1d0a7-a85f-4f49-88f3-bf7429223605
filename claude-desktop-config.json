{"mcpServers": {"advantage-waste-filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Documents\\Claude Code\\advantage-waste-enterprise"]}, "advantage-waste-github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}, "desktop-commander": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@wonderwhy-er/desktop-commander"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@mcp/server-sequential-thinking"]}, "n8n-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@mcp/server-n8n"], "env": {"N8N_BASE_URL": "http://localhost:5678", "N8N_API_KEY": "your-n8n-api-key-here"}}}}