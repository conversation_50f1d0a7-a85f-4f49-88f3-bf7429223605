"""
Service line database models for Advantage Waste Enterprise.
Handles detailed line items within waste management contracts.
"""

from sqlalchemy import (
    Column, Integer, String, Numeric, Boolean, ForeignKey,
    Index, JSON, CheckConstraint
)
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from ..core.database import Base
from .base import TimestampMixin


class ContainerType(PyEnum):
    """Container type enumeration"""
    FRONT_LOAD = "front_load"
    REAR_LOAD = "rear_load"
    ROLL_OFF = "roll_off"
    COMPACTOR = "compactor"
    RECYCLING = "recycling"
    ORGANICS = "organics"


class ServiceLine(Base, TimestampMixin):
    """
    Service line model for contract details.
    Represents individual waste service components within a contract.
    """
    __tablename__ = "service_lines"

    id = Column(Integer, primary_key=True, index=True)
    
    # Contract relationship
    contract_id = Column(Integer, ForeignKey("contracts.id"), nullable=False)
    
    # Service identification
    line_number = Column(Integer, nullable=False)
    service_description = Column(String(200), nullable=False)
    
    # Container details
    container_type = Column(String(20), nullable=False)
    container_size = Column(Numeric(4, 1), nullable=False)  # Cubic yards
    quantity = Column(Integer, nullable=False)
    
    # Service frequency
    pickups_per_week = Column(Integer, nullable=False)
    service_days = Column(JSON, nullable=True)  # Array of weekday numbers [1-7]
    
    # Pricing
    monthly_cost = Column(Numeric(10, 2), nullable=False)
    unit_price = Column(Numeric(8, 2), nullable=True)  # Price per pickup
    overage_rate = Column(Numeric(8, 2), nullable=True)  # Extra pickup rate
    
    # Service specifics
    is_compacted = Column(Boolean, default=False)
    compaction_ratio = Column(Numeric(3, 1), nullable=True)  # e.g., 4.0 for 4:1
    includes_rental = Column(Boolean, default=True)
    includes_disposal = Column(Boolean, default=True)
    
    # Location details
    service_location = Column(String(100), nullable=True)  # e.g., "North Building"
    access_notes = Column(String(500), nullable=True)
    
    # Additional charges
    fuel_surcharge_applicable = Column(Boolean, default=True)
    environmental_fee_applicable = Column(Boolean, default=True)
    additional_fees = Column(JSON, nullable=True)  # Array of {name, amount, type}
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Metadata
    service_metadata = Column(JSON, nullable=True)
    
    # Relationships
    contract = relationship("Contract", backref="service_lines")
    
    # Constraints
    __table_args__ = (
        Index("idx_service_line_contract", "contract_id"),
        Index("idx_service_line_type_size", "container_type", "container_size"),
        CheckConstraint("container_size > 0", name="check_container_size_positive"),
        CheckConstraint("quantity > 0", name="check_quantity_positive"),
        CheckConstraint("pickups_per_week BETWEEN 1 AND 7", name="check_pickups_valid"),
        CheckConstraint("monthly_cost >= 0", name="check_monthly_cost_non_negative"),
    )
    
    @property
    def monthly_volume(self) -> float:
        """Calculate monthly volume for this service line"""
        base_volume = float(self.container_size * self.quantity * self.pickups_per_week * 4.33)
        
        # Adjust for compaction if applicable
        if self.is_compacted and self.compaction_ratio:
            return base_volume * float(self.compaction_ratio)
        
        return base_volume
    
    @property
    def cost_per_pickup(self) -> float:
        """Calculate cost per pickup"""
        monthly_pickups = self.pickups_per_week * 4.33
        if monthly_pickups > 0:
            return float(self.monthly_cost / monthly_pickups)
        return 0.0
    
    @property
    def cost_per_yard(self) -> float:
        """Calculate cost per cubic yard"""
        volume = self.monthly_volume
        if volume > 0:
            return float(self.monthly_cost / volume)
        return 0.0
    
    @property
    def efficiency_ratio(self) -> float:
        """Calculate efficiency ratio (cost per yard per pickup)"""
        if self.pickups_per_week > 0 and self.container_size > 0:
            return float(self.monthly_cost / (self.container_size * self.pickups_per_week * 4.33))
        return 0.0