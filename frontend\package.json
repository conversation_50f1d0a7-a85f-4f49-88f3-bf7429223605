{"name": "advantage-waste-enterprise-frontend", "version": "0.1.0", "private": true, "description": "Enterprise waste management frontend for Greystar Advantage Waste", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.{ts,tsx,js,jsx,css,md}"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "@tanstack/react-query": "^5.17.9", "@tanstack/react-query-devtools": "^5.17.9", "axios": "^1.6.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.292.0", "react-hot-toast": "^2.4.1", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "react-error-boundary": "^4.0.11", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.11", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@vitest/coverage-v8": "^1.1.3", "@vitest/ui": "^1.1.3", "jsdom": "^23.2.0", "vitest": "^1.1.3", "prettier": "^3.0.3", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10"}, "keywords": ["waste-management", "greystar", "enterprise", "multifamily", "contract-analysis"], "author": "<PERSON> <<EMAIL>>", "license": "Private"}