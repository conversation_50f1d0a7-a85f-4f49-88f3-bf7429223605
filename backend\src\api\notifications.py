"""
Notification Management API
==========================

FastAPI router for managing notification preferences, sending alerts, 
and tracking communication history for the Advantage Waste system.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, status, Depends, Query, Path, BackgroundTasks
from fastapi.responses import JSONResponse
import structlog

from ..core.security import AuthenticatedUser, Admin
from ..schemas.renewal import (
    NotificationPreferences,
    NotificationHistory,
    NotificationFrequency,
    APIResponse,
    RenewalItem,
    PriorityLevel
)

logger = structlog.get_logger()
router = APIRouter()

# Mock notification preferences storage
MOCK_PREFERENCES = {
    "user-123": NotificationPreferences(
        user_id="user-123",
        renewal_reminders=NotificationFrequency.WEEKLY,
        cost_alerts=NotificationFrequency.IMMEDIATE,
        contract_expiry_days=60,
        email_enabled=True,
        dashboard_alerts=True,
        mobile_push=False
    )
}

@router.get(
    "/preferences",
    response_model=NotificationPreferences,
    summary="Get user notification preferences",
    description="Retrieve current notification preferences for the authenticated user",
    tags=["notifications"]
)
async def get_notification_preferences(
    current_user: AuthenticatedUser = Depends()
) -> NotificationPreferences:
    """
    Get the current user's notification preferences.
    
    Returns all notification settings including:
    - Renewal reminder frequency
    - Cost alert preferences
    - Communication channels
    - Timing settings
    """
    try:
        logger.info(
            "Fetching notification preferences",
            user_id=current_user.user_id
        )
        
        # Get preferences from storage (mock implementation)
        preferences = MOCK_PREFERENCES.get(
            current_user.user_id,
            NotificationPreferences(
                user_id=current_user.user_id,
                renewal_reminders=NotificationFrequency.WEEKLY,
                cost_alerts=NotificationFrequency.IMMEDIATE,
                contract_expiry_days=60,
                email_enabled=True,
                dashboard_alerts=True,
                mobile_push=False
            )
        )
        
        return preferences
        
    except Exception as e:
        logger.error("Error fetching notification preferences", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch notification preferences"
        )

@router.put(
    "/preferences",
    response_model=APIResponse,
    summary="Update notification preferences",
    description="Update notification preferences for the authenticated user",
    tags=["notifications"]
)
async def update_notification_preferences(
    preferences: NotificationPreferences,
    current_user: AuthenticatedUser = Depends()
) -> APIResponse:
    """
    Update the user's notification preferences.
    
    Allows updating:
    - Frequency settings for different notification types
    - Communication channel preferences
    - Alert thresholds and timing
    """
    try:
        logger.info(
            "Updating notification preferences",
            user_id=current_user.user_id,
            renewal_reminders=preferences.renewal_reminders,
            cost_alerts=preferences.cost_alerts,
            email_enabled=preferences.email_enabled
        )
        
        # Ensure user can only update their own preferences
        if preferences.user_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot update preferences for another user"
            )
        
        # Validate expiry days is reasonable
        if preferences.contract_expiry_days < 30 or preferences.contract_expiry_days > 365:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Contract expiry notification days must be between 30 and 365"
            )
        
        # Store updated preferences (mock implementation)
        MOCK_PREFERENCES[current_user.user_id] = preferences
        
        logger.info(
            "Notification preferences updated successfully",
            user_id=current_user.user_id
        )
        
        return APIResponse(
            success=True,
            message="Notification preferences updated successfully",
            data={"user_id": current_user.user_id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error updating notification preferences", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update notification preferences"
        )

@router.post(
    "/send-renewal-reminder",
    response_model=APIResponse,
    summary="Send renewal reminder notification",
    description="Send a renewal reminder notification for a specific contract",
    tags=["notifications"]
)
async def send_renewal_reminder(
    contract_id: str,
    message: Optional[str] = None,
    urgent: bool = False,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: AuthenticatedUser = Depends()
) -> APIResponse:
    """
    Send a renewal reminder notification for a specific contract.
    
    Can be triggered manually or automatically based on user preferences.
    Supports custom messages and urgent priority flags.
    """
    try:
        logger.info(
            "Sending renewal reminder",
            contract_id=contract_id,
            user_id=current_user.user_id,
            urgent=urgent
        )
        
        # Validate contract exists and user has access
        # TODO: Replace with actual database lookup
        if contract_id not in ["CNT-001", "CNT-002", "CNT-003"]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Contract {contract_id} not found"
            )
        
        # Get notification preferences
        preferences = MOCK_PREFERENCES.get(
            current_user.user_id,
            NotificationPreferences(user_id=current_user.user_id)
        )
        
        # Check if notifications are enabled
        if not preferences.email_enabled:
            return APIResponse(
                success=False,
                message="Email notifications are disabled for this user",
                data={"contract_id": contract_id}
            )
        
        # Schedule notification sending in background
        background_tasks.add_task(
            send_notification_email,
            recipient=current_user.email,
            notification_type="renewal_reminder",
            contract_id=contract_id,
            message=message,
            urgent=urgent
        )
        
        # Create notification history record
        notification_id = f"NOT-{contract_id}-{int(datetime.now().timestamp())}"
        
        logger.info(
            "Renewal reminder scheduled",
            contract_id=contract_id,
            notification_id=notification_id,
            recipient=current_user.email
        )
        
        return APIResponse(
            success=True,
            message="Renewal reminder sent successfully",
            data={
                "notification_id": notification_id,
                "contract_id": contract_id,
                "recipient": current_user.email
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error sending renewal reminder", error=str(e), contract_id=contract_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send renewal reminder"
        )

@router.post(
    "/send-cost-alert",
    response_model=APIResponse,
    summary="Send cost alert notification",
    description="Send a cost alert notification for significant cost changes or opportunities",
    tags=["notifications"]
)
async def send_cost_alert(
    contract_id: str,
    alert_type: str,
    cost_change_amount: float,
    cost_change_percentage: float,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: AuthenticatedUser = Depends()
) -> APIResponse:
    """
    Send a cost alert notification for significant cost changes.
    
    Triggered automatically when:
    - Contract costs increase beyond thresholds
    - Savings opportunities are identified
    - Market rate changes affect contracts
    """
    try:
        logger.info(
            "Sending cost alert",
            contract_id=contract_id,
            alert_type=alert_type,
            cost_change_amount=cost_change_amount,
            cost_change_percentage=cost_change_percentage,
            user_id=current_user.user_id
        )
        
        # Validate alert type
        valid_alert_types = ["cost_increase", "savings_opportunity", "market_change", "contract_anomaly"]
        if alert_type not in valid_alert_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid alert type. Must be one of: {valid_alert_types}"
            )
        
        # Get notification preferences
        preferences = MOCK_PREFERENCES.get(
            current_user.user_id,
            NotificationPreferences(user_id=current_user.user_id)
        )
        
        # Check if cost alerts are enabled and frequency allows sending
        if preferences.cost_alerts == NotificationFrequency.DISABLED:
            return APIResponse(
                success=False,
                message="Cost alerts are disabled for this user",
                data={"contract_id": contract_id}
            )
        
        # Check if alert meets threshold for immediate sending
        should_send_immediately = (
            preferences.cost_alerts == NotificationFrequency.IMMEDIATE or
            abs(cost_change_percentage) > 10.0 or  # 10% change threshold
            alert_type == "contract_anomaly"
        )
        
        if should_send_immediately:
            # Send immediately
            background_tasks.add_task(
                send_notification_email,
                recipient=current_user.email,
                notification_type="cost_alert",
                contract_id=contract_id,
                alert_data={
                    "alert_type": alert_type,
                    "cost_change_amount": cost_change_amount,
                    "cost_change_percentage": cost_change_percentage
                }
            )
            
            notification_id = f"NOT-{contract_id}-{int(datetime.now().timestamp())}"
            
            return APIResponse(
                success=True,
                message="Cost alert sent immediately",
                data={
                    "notification_id": notification_id,
                    "contract_id": contract_id,
                    "alert_type": alert_type
                }
            )
        else:
            # Queue for batch sending based on frequency preference
            background_tasks.add_task(
                queue_notification_for_batch,
                user_id=current_user.user_id,
                notification_type="cost_alert",
                contract_id=contract_id,
                alert_data={
                    "alert_type": alert_type,
                    "cost_change_amount": cost_change_amount,
                    "cost_change_percentage": cost_change_percentage
                }
            )
            
            return APIResponse(
                success=True,
                message=f"Cost alert queued for {preferences.cost_alerts.value} delivery",
                data={
                    "contract_id": contract_id,
                    "alert_type": alert_type,
                    "delivery_frequency": preferences.cost_alerts.value
                }
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error sending cost alert", error=str(e), contract_id=contract_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send cost alert"
        )

@router.get(
    "/history",
    response_model=List[NotificationHistory],
    summary="Get notification history",
    description="Retrieve notification history for the authenticated user",
    tags=["notifications"]
)
async def get_notification_history(
    limit: int = Query(50, ge=1, le=200, description="Maximum number of notifications"),
    notification_type: Optional[str] = Query(None, description="Filter by notification type"),
    date_from: Optional[datetime] = Query(None, description="Start date for history"),
    date_to: Optional[datetime] = Query(None, description="End date for history"),
    current_user: AuthenticatedUser = Depends()
) -> List[NotificationHistory]:
    """
    Get notification history for the current user.
    
    Supports filtering by:
    - Notification type
    - Date range
    - Delivery status
    """
    try:
        logger.info(
            "Fetching notification history",
            user_id=current_user.user_id,
            limit=limit,
            notification_type=notification_type
        )
        
        # Mock notification history
        notifications = [
            NotificationHistory(
                notification_id="NOT-CNT-001-001",
                contract_id="CNT-001",
                recipient=current_user.email,
                notification_type="renewal_reminder",
                sent_date=datetime.now() - timedelta(days=7),
                delivery_status="read",
                content_summary="Contract expiring in 60 days - analysis required"
            ),
            NotificationHistory(
                notification_id="NOT-CNT-002-001",
                contract_id="CNT-002",
                recipient=current_user.email,
                notification_type="cost_alert",
                sent_date=datetime.now() - timedelta(days=5),
                delivery_status="delivered",
                content_summary="Cost increase of 8% detected - review recommended"
            ),
            NotificationHistory(
                notification_id="NOT-CNT-001-002",
                contract_id="CNT-001",
                recipient=current_user.email,
                notification_type="analysis_complete",
                sent_date=datetime.now() - timedelta(days=3),
                delivery_status="delivered",
                content_summary="Renewal analysis complete - 15% savings opportunity identified"
            ),
            NotificationHistory(
                notification_id="NOT-CNT-003-001",
                contract_id="CNT-003",
                recipient=current_user.email,
                notification_type="approval_request",
                sent_date=datetime.now() - timedelta(days=1),
                delivery_status="sent",
                content_summary="Approval requested for vendor switch recommendation"
            )
        ]
        
        # Apply filters
        filtered_notifications = notifications
        
        if notification_type:
            filtered_notifications = [n for n in filtered_notifications if n.notification_type == notification_type]
        
        if date_from:
            filtered_notifications = [n for n in filtered_notifications if n.sent_date >= date_from]
        
        if date_to:
            filtered_notifications = [n for n in filtered_notifications if n.sent_date <= date_to]
        
        # Apply limit
        return filtered_notifications[:limit]
        
    except Exception as e:
        logger.error("Error fetching notification history", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch notification history"
        )

@router.post(
    "/bulk-send",
    response_model=APIResponse,
    summary="Send bulk notifications",
    description="Send notifications to multiple recipients (admin only)",
    tags=["notifications"]
)
async def send_bulk_notifications(
    recipient_user_ids: List[str],
    notification_type: str,
    message: str,
    contract_ids: Optional[List[str]] = None,
    urgent: bool = False,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: Admin = Depends()
) -> APIResponse:
    """
    Send bulk notifications to multiple users.
    
    Admin-only endpoint for:
    - System-wide announcements
    - Emergency notifications
    - Bulk contract reminders
    """
    try:
        logger.info(
            "Sending bulk notifications",
            recipient_count=len(recipient_user_ids),
            notification_type=notification_type,
            admin_user=current_user.user_id,
            urgent=urgent
        )
        
        # Validate recipient limit
        if len(recipient_user_ids) > 1000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot send to more than 1000 recipients at once"
            )
        
        # Schedule bulk sending in background
        background_tasks.add_task(
            send_bulk_notifications_task,
            recipient_user_ids=recipient_user_ids,
            notification_type=notification_type,
            message=message,
            contract_ids=contract_ids,
            urgent=urgent,
            sender_user_id=current_user.user_id
        )
        
        return APIResponse(
            success=True,
            message=f"Bulk notification scheduled for {len(recipient_user_ids)} recipients",
            data={
                "recipient_count": len(recipient_user_ids),
                "notification_type": notification_type,
                "urgent": urgent
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error sending bulk notifications", error=str(e), admin_user=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send bulk notifications"
        )

# Background task functions
async def send_notification_email(
    recipient: str,
    notification_type: str,
    contract_id: Optional[str] = None,
    message: Optional[str] = None,
    urgent: bool = False,
    alert_data: Optional[Dict[str, Any]] = None
):
    """Background task to send notification email"""
    try:
        logger.info(
            "Sending notification email",
            recipient=recipient,
            notification_type=notification_type,
            contract_id=contract_id,
            urgent=urgent
        )
        
        # Mock email sending (replace with actual email service)
        # This would typically integrate with services like:
        # - SendGrid
        # - AWS SES
        # - Microsoft Graph API
        
        # Simulate email sending delay
        import asyncio
        await asyncio.sleep(1)
        
        logger.info(
            "Notification email sent successfully",
            recipient=recipient,
            notification_type=notification_type,
            contract_id=contract_id
        )
        
    except Exception as e:
        logger.error(
            "Error sending notification email",
            error=str(e),
            recipient=recipient,
            notification_type=notification_type
        )

async def queue_notification_for_batch(
    user_id: str,
    notification_type: str,
    contract_id: Optional[str] = None,
    alert_data: Optional[Dict[str, Any]] = None
):
    """Queue notification for batch delivery based on user preferences"""
    try:
        logger.info(
            "Queueing notification for batch delivery",
            user_id=user_id,
            notification_type=notification_type,
            contract_id=contract_id
        )
        
        # Mock batch queueing (replace with actual queue system)
        # This would typically use:
        # - Redis for caching queued notifications
        # - Celery for scheduled batch processing
        # - Database for persistent storage
        
        logger.info(
            "Notification queued successfully",
            user_id=user_id,
            notification_type=notification_type
        )
        
    except Exception as e:
        logger.error(
            "Error queueing notification",
            error=str(e),
            user_id=user_id,
            notification_type=notification_type
        )

async def send_bulk_notifications_task(
    recipient_user_ids: List[str],
    notification_type: str,
    message: str,
    contract_ids: Optional[List[str]] = None,
    urgent: bool = False,
    sender_user_id: str = None
):
    """Background task for sending bulk notifications"""
    try:
        logger.info(
            "Starting bulk notification task",
            recipient_count=len(recipient_user_ids),
            notification_type=notification_type,
            sender_user_id=sender_user_id
        )
        
        # Mock bulk sending with rate limiting
        import asyncio
        
        successful_sends = 0
        failed_sends = 0
        
        for user_id in recipient_user_ids:
            try:
                # Mock email lookup and sending
                await asyncio.sleep(0.1)  # Rate limiting
                successful_sends += 1
                
            except Exception as e:
                logger.error(
                    "Failed to send notification to user",
                    error=str(e),
                    user_id=user_id
                )
                failed_sends += 1
        
        logger.info(
            "Bulk notification task completed",
            successful_sends=successful_sends,
            failed_sends=failed_sends,
            sender_user_id=sender_user_id
        )
        
    except Exception as e:
        logger.error(
            "Error in bulk notification task",
            error=str(e),
            sender_user_id=sender_user_id
        )