# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Data processing and analysis
pandas==2.1.4
numpy==1.25.2
openpyxl==3.1.2
xlsxwriter==3.1.9

# PDF processing
PyPDF2==3.0.1
pdfplumber==0.10.3

# HTTP client
httpx==0.25.2
requests==2.31.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Code quality
ruff==0.1.6
mypy==1.7.1
black==23.11.0

# Development
python-dotenv==1.0.0
rich==13.7.0

# Background tasks, caching, and queue management
celery==5.3.4
redis==5.0.1
fastapi-cache2==0.2.1

# Email and notifications
emails==0.6.0
jinja2==3.1.2
premailer==3.10.0

# Monitoring and logging
structlog==23.2.0

# Additional security and API features
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
bcrypt==4.1.2

# Development and testing enhancements
factory-boy==3.3.0
faker==20.1.0
