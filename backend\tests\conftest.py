"""
Test Configuration and Fixtures
================================

Shared test configuration, fixtures, and utilities for the Advantage Waste test suite.
Provides realistic test data scenarios based on Greystar's actual property portfolio.
"""

import pytest
import asyncio
from typing import Dict, Any, List
from decimal import Decimal
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from examples.contract_analysis_example import (
    ContractTerms, PropertyInfo, ContractAnalyzer, ContractGrade
)

@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# Test Data Factories
@pytest.fixture
def sample_property_garden_style():
    """252-unit garden-style property (typical Greystar property)"""
    return PropertyInfo(
        name="Columbia Square Living",
        address="123 Main St, Dallas, TX",
        unit_count=252,
        property_type="garden-style"
    )

@pytest.fixture
def sample_property_high_rise():
    """High-rise property with different metrics"""
    return PropertyInfo(
        name="Metropolitan Tower",
        address="789 Urban Ave, Atlanta, GA",
        unit_count=450,
        property_type="high-rise"
    )

@pytest.fixture
def sample_property_mid_rise():
    """Mid-rise property for comparison testing"""
    return PropertyInfo(
        name="Riverside Commons",
        address="456 River Dr, Austin, TX",
        unit_count=180,
        property_type="mid-rise"
    )

@pytest.fixture
def excellent_contract():
    """Grade A+ contract with optimal terms"""
    return ContractTerms(
        vendor_name="Optimal Waste Solutions",
        contact_name="Jane Smith",
        contact_info="<EMAIL>",
        quote_number="OPT-2024-001",
        contract_length_months=24,
        effective_date=datetime(2024, 1, 1),
        automatic_renewal=True,
        renewal_term_months=12,
        termination_notice_days=60,
        container_size_yards=34,
        container_type="compactor",
        container_quantity=1,
        pickup_frequency_weekly=1,
        base_monthly_rate=Decimal("4500.00"),
        fuel_surcharge_percent=None,  # No fuel surcharge
        environmental_fee_percent=None,  # No env fees
        admin_fee=None,
        container_rental_fee=None,
        max_annual_increase_percent=3.0,  # CPI capped at 3%
        cpi_increases=True,
        cpi_index="US Bureau of Labor Statistics CPI-U"
    )

@pytest.fixture
def poor_contract():
    """Grade D contract with problematic terms"""
    return ContractTerms(
        vendor_name="Expensive Waste Co",
        contact_name="Bob Johnson",
        contact_info="<EMAIL>",
        quote_number="EXP-2024-001",
        contract_length_months=60,  # Too long
        effective_date=datetime(2024, 1, 1),
        automatic_renewal=True,
        renewal_term_months=24,
        termination_notice_days=180,  # Too long
        container_size_yards=34,
        container_type="compactor",
        container_quantity=1,
        pickup_frequency_weekly=1,
        base_monthly_rate=Decimal("8000.00"),  # Very expensive
        fuel_surcharge_percent=12.0,  # High fuel surcharge
        environmental_fee_percent=8.0,  # High env fees
        admin_fee=Decimal("150.00"),
        container_rental_fee=Decimal("300.00"),
        max_annual_increase_percent=10.0,  # No protection
        early_termination_fee_calculation="12 months remaining rent"
    )

@pytest.fixture
def realistic_gfl_contract():
    """Realistic GFL contract from examples"""
    return ContractTerms(
        vendor_name="GFL Environmental",
        contact_name="John Smith",
        contact_info="<EMAIL>",
        quote_number="GFL-2024-001",
        contract_length_months=60,
        effective_date=datetime(2024, 1, 1),
        automatic_renewal=True,
        renewal_term_months=12,
        termination_notice_days=30,
        container_size_yards=34,
        container_type="compactor",
        container_quantity=1,
        pickup_frequency_weekly=1,
        base_monthly_rate=Decimal("663.91"),
        fuel_surcharge_percent=8.0,
        environmental_fee_percent=5.0,
        extra_pickup_cost=Decimal("246.75"),
        max_annual_increase_percent=6.0
    )

@pytest.fixture
def realistic_wm_contract():
    """Realistic Waste Management contract from examples"""
    return ContractTerms(
        vendor_name="Waste Management",
        contact_name="Jane Doe",
        contact_info="<EMAIL>",
        quote_number="WM-2024-002",
        contract_length_months=12,
        effective_date=datetime(2024, 1, 1),
        automatic_renewal=True,
        renewal_term_months=12,
        termination_notice_days=30,
        container_size_yards=34,
        container_type="compactor",
        container_quantity=1,
        pickup_frequency_weekly=1,
        base_monthly_rate=Decimal("745.00"),
        container_rental_fee=Decimal("650.00"),
        fuel_surcharge_percent=None,
        environmental_fee_percent=None,
        extra_pickup_cost=Decimal("195.00"),
        max_annual_increase_percent=4.0
    )

@pytest.fixture
def edge_case_contracts():
    """Edge case contracts for testing validation"""
    return [
        # Zero cost contract
        ContractTerms(
            vendor_name="Free Waste",
            contact_name="Free Person",
            contact_info="<EMAIL>",
            quote_number="FREE-001",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=False,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=4,
            container_type="front-load",
            container_quantity=1,
            pickup_frequency_weekly=1,
            base_monthly_rate=Decimal("0.00")
        ),
        # Very high frequency pickup
        ContractTerms(
            vendor_name="Daily Pickup Co",
            contact_name="Daily Person",
            contact_info="<EMAIL>",
            quote_number="DAILY-001",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=False,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=2,
            container_type="front-load",
            container_quantity=5,
            pickup_frequency_weekly=7,  # Daily pickup
            base_monthly_rate=Decimal("15000.00")
        ),
        # Large container setup
        ContractTerms(
            vendor_name="Big Container Co",
            contact_name="Big Person",
            contact_info="<EMAIL>",
            quote_number="BIG-001",
            contract_length_months=12,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=False,
            renewal_term_months=12,
            termination_notice_days=30,
            container_size_yards=40,  # Large compactor
            container_type="compactor",
            container_quantity=3,
            pickup_frequency_weekly=2,
            base_monthly_rate=Decimal("12000.00")
        )
    ]

@pytest.fixture
def contract_analyzer():
    """Contract analyzer instance"""
    return ContractAnalyzer()

@pytest.fixture
def performance_test_properties():
    """Large dataset for performance testing"""
    properties = []
    property_types = ["garden-style", "mid-rise", "high-rise"]
    
    for i in range(100):  # Create 100 properties for testing
        prop_type = property_types[i % 3]
        unit_count = {
            "garden-style": 200 + (i * 5),
            "mid-rise": 150 + (i * 3),
            "high-rise": 300 + (i * 8)
        }[prop_type]
        
        properties.append(PropertyInfo(
            name=f"Test Property {i+1}",
            address=f"{100+i} Test St, Test City, TX",
            unit_count=unit_count,
            property_type=prop_type
        ))
    
    return properties

@pytest.fixture
def enterprise_scale_contracts():
    """Generate contracts for enterprise-scale testing (3,850+ properties)"""
    contracts = []
    base_rates = [500, 750, 1200, 1800, 2500]  # Different rate tiers
    
    for i in range(50):  # 50 different contract templates
        base_rate = base_rates[i % len(base_rates)]
        
        contracts.append(ContractTerms(
            vendor_name=f"Vendor {i+1}",
            contact_name=f"Contact {i+1}",
            contact_info=f"contact{i+1}@vendor.com",
            quote_number=f"VENDOR-2024-{i+1:03d}",
            contract_length_months=12 if i % 2 == 0 else 24,
            effective_date=datetime(2024, 1, 1),
            automatic_renewal=True,
            renewal_term_months=12,
            termination_notice_days=30 + (i % 4) * 30,
            container_size_yards=34 if i % 3 == 0 else 4,
            container_type="compactor" if i % 3 == 0 else "front-load",
            container_quantity=1 + (i % 3),
            pickup_frequency_weekly=1 + (i % 3),
            base_monthly_rate=Decimal(str(base_rate + (i * 50))),
            fuel_surcharge_percent=float(i % 10) if i % 4 == 0 else None,
            environmental_fee_percent=float((i % 5) + 1) if i % 5 == 0 else None,
            max_annual_increase_percent=3.0 + (i % 7)
        ))
    
    return contracts

# Test Utilities
class TestDataValidator:
    """Utilities for validating test data integrity"""
    
    @staticmethod
    def validate_contract_metrics(metrics: Dict[str, Any]) -> bool:
        """Validate that calculated metrics are reasonable"""
        required_keys = [
            'monthly_volume_yards', 'total_monthly_cost', 'cost_per_yard',
            'cost_per_door', 'yards_per_door', 'annual_cost'
        ]
        
        # Check all required keys exist
        if not all(key in metrics for key in required_keys):
            return False
        
        # Validate reasonable ranges
        if metrics['cost_per_door'] < 0 or metrics['cost_per_door'] > 200:
            return False
        
        if metrics['yards_per_door'] < 0 or metrics['yards_per_door'] > 10:
            return False
        
        if metrics['monthly_volume_yards'] <= 0:
            return False
        
        return True
    
    @staticmethod
    def validate_benchmark_analysis(analysis: Dict[str, Any]) -> bool:
        """Validate benchmark analysis structure"""
        required_sections = ['cost_per_door', 'yards_per_door']
        
        for section in required_sections:
            if section not in analysis:
                return False
            
            section_data = analysis[section]
            required_fields = ['value', 'benchmark_range', 'status', 'variance_percent']
            
            if not all(field in section_data for field in required_fields):
                return False
        
        return True

@pytest.fixture
def test_data_validator():
    """Test data validator instance"""
    return TestDataValidator()

# Mock fixtures for external dependencies
@pytest.fixture
def mock_database():
    """Mock database connection"""
    mock_db = Mock()
    mock_db.execute = AsyncMock()
    mock_db.fetch_all = AsyncMock()
    mock_db.fetch_one = AsyncMock()
    return mock_db

@pytest.fixture
def mock_email_service():
    """Mock email service for notification testing"""
    mock_email = Mock()
    mock_email.send_renewal_alert = AsyncMock()
    mock_email.send_analysis_report = AsyncMock()
    return mock_email

@pytest.fixture
def mock_vendor_api():
    """Mock vendor API responses"""
    mock_api = Mock()
    mock_api.get_current_rates = AsyncMock()
    mock_api.validate_service_area = AsyncMock()
    return mock_api

# Performance testing configuration
@pytest.fixture
def performance_config():
    """Configuration for performance tests"""
    return {
        'max_response_time_seconds': 2.0,
        'max_analysis_time_seconds': 0.5,
        'concurrent_users': 500,
        'properties_per_batch': 100,
        'memory_limit_mb': 512,
        'cpu_usage_limit_percent': 80
    }

# Test environment configuration
@pytest.fixture(scope="session")
def test_settings():
    """Test environment settings"""
    return {
        'database_url': 'sqlite:///test_advantage_waste.db',
        'redis_url': 'redis://localhost:6379/15',  # Use test DB
        'log_level': 'DEBUG',
        'testing': True,
        'disable_auth': True,
        'mock_external_apis': True
    }